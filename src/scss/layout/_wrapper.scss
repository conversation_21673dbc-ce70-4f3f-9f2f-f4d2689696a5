@use 'shared' as *;

.wrapper {
  max-width: $article-max-width;
  width: calc(100% - #{$side-padding * 2});
  margin-inline: auto;

  @include media-breakpoint-down(sm) {
    width: calc(100% - #{$mobile-side-padding * 2});
  }

  &.page {
    width: 100% !important;
    max-width: calc(100% - #{$side-padding * 2}) !important;

    @include media-breakpoint-down(sm) {
      width: 100% !important;
    }
  }
}

section,
header,
footer,
nav,
article {
  > .wrapper {
    max-width: $article-max-width;
    width: 100%;
    margin-inline: auto;

    @include media-breakpoint-down(md) {
      width: 100%;
    }
  }
}

kesma-layout {
  .content-element {
    margin-bottom: 24px;
    max-width: 100%;
    width: 100%;

    &::ng-deep > .article-card {
      margin-bottom: 0;
      display: block;
      @include media-breakpoint-down(md) {
        margin-bottom: 0;
      }
    }

    &.sidebar {
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
