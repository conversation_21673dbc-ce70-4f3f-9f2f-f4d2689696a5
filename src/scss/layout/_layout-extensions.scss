@use 'shared' as *;

.wrapper.with-aside {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 38px;

  @include media-breakpoint-down(sm) {
    margin-top: 15px;
  }

  > .left-column {
    display: block;
    width: calc(100% - #{$aside-width} - #{$aside-gap});
    @include media-breakpoint-down(sm) {
      width: 100%;
    }
  }

  > aside,
  > app-sidebar {
    display: block;
    width: $aside-width;
    margin-bottom: 30px;
    @include media-breakpoint-down(sm) {
      width: 100%;
    }

    > * {
      margin-bottom: $block-bottom-margin;
    }

    .aside-box {
      .heading-line {
        margin-bottom: 24px;
      }

      mno-article-card {
        margin-bottom: 24px;
      }
    }

    kesma-advertisement {
      display: block;

      @include media-breakpoint-down(sm) {
        margin-left: -15px;
        margin-right: -15px;
        width: calc(100% + 30px);
      }
    }
  }
}

kesma-layout {
  .main-element,
  .vertical,
  .row-element.vertical {
    background-color: initial;
  }

  .content-container.content-element.content-type-branding-box.layout-element,
  .content-container.content-element.content-type-branding-box-ex.layout-element {
    margin: 0;
  }
}

.row.horizontal .col-12 {
  > [mno-article-card],
  > mno-opinion-card {
    height: 100%;
  }
}
