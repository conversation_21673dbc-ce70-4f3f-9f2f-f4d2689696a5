@use 'shared' as *;

.full-width {
  width: 100%;
}

.desktop {
  @include media-breakpoint-down(md) {
    display: none;
  }
}

.mobile {
  @include media-breakpoint-up(md) {
    display: none;
  }
}

.desktop-only {
  @include media-breakpoint-down(md) {
    display: none !important;
  }
}

.mobile-only {
  @include media-breakpoint-up(md) {
    display: none !important;
  }
}

.d-flex {
  display: flex !important;
}

.w-100,
.w-full {
  width: 100%;
}

.h-100,
.h-full {
  height: 100%;
}

.flex-column {
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.align-items {
  &-start {
    align-items: flex-start;
  }

  &-center {
    align-items: center;
  }

  &-end {
    align-items: flex-end;
  }

  &-stretch {
    align-items: stretch;
  }
}

.justify-content {
  &-center {
    justify-content: center;
  }

  &-between {
    justify-content: space-between;
  }

  &-around {
    justify-content: space-around;
  }

  &-start {
    justify-content: flex-start;
  }

  &-end {
    justify-content: flex-end;
  }

  &-evenly {
    justify-content: space-evenly;
  }

  &-stretch {
    justify-content: stretch;
  }
}

.list-square-blue {
  list-style-type: square;
  list-style-position: outside;
  margin-left: 16px;

  li {
    &::marker {
      color: var(--kui-blue-500);
      font-size: 18px;
    }

    color: var(--kui-slate-950);
  }
}

.list-plus-darkblue {
  li {
    list-style-position: inside;
    margin: 4px 0;
    background: url('/assets/images/icons/add-square-darkblue-small.svg') no-repeat left center;
    padding: 0 0 0 24px;
    list-style-type: none;

    a {
      &,
      &:visited,
      &:active,
      &:focus {
        @include transition;
        color: var(--kui-slate-950);
        font-size: 14px;
        font-weight: 500;
        line-height: 18px; /* 128.571% */
      }

      &:hover {
        color: var(--kui-blue-700);
        @include transition;
      }
    }
  }
}

:root,
swiper-container {
  --swiper-theme-color: var(--kui-primary);
}
