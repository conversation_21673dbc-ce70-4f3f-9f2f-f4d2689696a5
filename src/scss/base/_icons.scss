@use 'shared' as *;

.icon {
  display: inline-block;
  min-width: 5px;
  min-height: 5px;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;

  &.eb-labdarugo {
    @include icon('eb-labdarugo.svg');
  }

  &.icon-logo {
    @include icon('logo-mno-dark.svg');
  }

  &.icon-logo-small {
    @include icon('logo-mno-small-dark.svg');
  }

  &.icon-logo-small-light {
    @include icon('logo-mno-small-slightblue.svg');
  }

  &.icon-chevron-up-dark {
    @include icon('icons/chevron-up-dark.svg');
  }

  &.icon-chevron-right-red {
    @include icon('icons/chevron-right-red.svg');
  }

  &.icon-dismiss-circle-red {
    @include icon('icons/dismiss-circle-red.svg');
  }

  &.icon-chevron-right-blue {
    @include icon('icons/chevron-right-blue.svg');
  }

  &.icon-chevron-right-white {
    @include icon('icons/chevron-right-white.svg');
  }

  &.icon-dismiss-circle-blue {
    @include icon('icons/dismiss-circle-blue.svg');
  }
}

// Global icons
button,
.icon {
  &.icon-logo {
    @include icon('logo-mno-dark.svg');
  }
}
