@use 'shared' as *;

// Typography
html,
body {
  height: 100%;
  background-color: var(--kui-white);
}

html,
body,
p {
  font-family: var(--kui-font-primary);
  color: var(--kui-slate-950);
  font-size: 16px;
  line-height: 24px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--kui-font-primary);
  font-weight: 700;
}

// Cikk cime
h1 {
  font-size: 32px;
  line-height: 40px;
  @include media-breakpoint-down(md) {
    font-size: 24px;
    line-height: 32px;
    letter-spacing: 0.24px;
  }
}

h2 {
  // Foldali fo hir, amelynek pl fekete/kek a hattere vagy termeklistazo oldalon a termekek cime
  font-size: 24px;
  line-height: 32px;

  // tag/kereso listazoban a cikk cime
  &.tag-title {
    font-size: 32px;
    line-height: 36px;
    text-decoration: underline;
  }
}

h3 {
  //cikk h3
  font-size: 30px;
  line-height: 30px;
}

//jobb oldali cikk listazo cim
h4 {
  font-size: 16px;
  line-height: 21px;
}

a.link {
  &,
  &:visited,
  &:link,
  &:focus {
    color: var(--kui-blue-500);
    transition-duration: 0.3s;
    font-weight: 700;
    border-bottom: 1px solid transparent;
  }

  &:hover {
    transition-duration: 0.3s;
    border-bottom: 1px solid var(--kui-blue-500);
  }
}

.article-card {
  a {
    &,
    &:visited,
    &:link,
    &:focus {
      color: var(--kui-slate-950);
      border-bottom: initial;
      font-weight: 400;
    }

    &:hover {
      border-bottom: initial;
      &,
      & p {
        color: var(--kui-blue-700);
        transition-duration: 0.3s;
      }
    }
  }
}
