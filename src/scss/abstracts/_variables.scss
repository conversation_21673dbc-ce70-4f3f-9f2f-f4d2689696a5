$global-wrapper-width: 1272px;

// Aside settings
$aside-gap: 24px;
$aside-width: 300px;
$block-bottom-margin: 24px;

// Images path
$images-path: '/assets/images/';

// Bootstrap variables
$grid-columns: 12;
$grid-gutter-width: 24px;

$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px,
);

$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
);

//sizes
$header-mobile-height: 49px;
$header-desktop-height: 80px;
$pager-icon-size: 40px;
$input-height: 50px;

$article-card-aspect-ratio: 1.77648;
$article-card-square-aspect-ratio: 1.3333; //4:3 aspect ratio for some specific article cards

$layout-max-width: 1272px;
$article-max-width: 1056px;
$colorized-layout-row-gap: 0;

$side-padding: 84px;
$mobile-side-padding: 16px;
