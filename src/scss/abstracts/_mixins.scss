@use 'variables' as *;

// Vertical center
%center-vertically {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// Display block icon
%icon {
  display: inline-block;
  min-width: 5px;
  min-height: 5px;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
}

// Background image
%img {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

// Background image
@mixin img($file) {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url($images-path + $file);
}

// IE10 IE11 only
@mixin ieonly() {
  @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    @content;
  }
}

// Firefox only
@mixin ffonly() {
  @-moz-document url-prefix() {
    @content;
  }
}

@mixin imgRatio($x, $y) {
  display: block;
  width: 100%;
  background-size: cover;
  background-position: center;
  background-color: var(--kui-gray-500);
  padding-top: calc($y / $x) * 100%;
}

@mixin icon($name, $important: false) {
  $importantFlag: '';
  @if ($important) {
    $importantFlag: '!important';
  }
  background-image: url($images-path + $name) #{$importantFlag};
}

@mixin icon_mask($name) {
  -webkit-mask-image: url($images-path + $name);
  mask-image: url($images-path + $name);
}

@mixin transition {
  transition-duration: 0.3s;
  transition-timing-function: ease-out;
  transition-property: border-color, background-size, background-color, color, transform, position, top, height, width, opacity, background-opacity, transform;
}

@mixin layoutFullWidth {
  width: 100vw;
  position: relative;
  left: calc(-50vw + 50%);
}

@mixin GhostButtonHighlight($fgColor: var(--kui-slate-50), $bgColor: var(--kui-slate-950)) {
  background-color: $fgColor;
  color: $bgColor;
  @include transition;

  .icon {
    &.icon-chevron {
      @include icon('icons/chevron-down-dark.svg');
    }

    &.icon-play {
      @include icon('icons/play-dark.svg');
    }

    &.icon-image {
      @include icon('icons/image-dark.svg');
    }

    &.icon-mic {
      @include icon('icons/mic-dark.svg');
    }

    &.icon-mail {
      @include icon('icons/mail-dark.svg');
    }

    &.icon-news {
      @include icon('icons/news-dark.svg');
    }

    &.icon-search {
      @include icon('icons/search-dark.svg');
    }
  }
}
