// Globals & Variables

$micro-automotor-yellow: #fff200;
$micro-automotor-white: #ffffff;
$micro-automotor-grey-1: #f5f5f5;
$micro-automotor-grey-2: #d4d4d4;
$micro-automotor-grey-3: #3e3e3e;
$micro-automotor-grey-4: #292929;

.micro-automotor {
  font-family: 'Roboto';
  font-weight: 300;

  h1,
  h2,
  h3,
  h4,
  h5 {
    font-family: 'Open Sans';
    font-weight: 900;
    color: $micro-automotor-grey-3;
  }

  h1 {
    font-size: 34px;
    font-weight: 700;
    line-height: 44px;
    color: $micro-automotor-yellow;
  }

  h3 {
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;
  }

  p {
    font-size: 16px;
    font-weight: 300;
    color: $micro-automotor-grey-3;
  }

  button {
    font-family: 'Roboto';
  }
}
