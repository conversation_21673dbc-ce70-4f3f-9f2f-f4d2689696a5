@use 'shared' as *;

.btn-ghost,
a.btn-ghost {
  // color: var(--kui-slate-50);
  border-radius: 2px;
  border: 1px solid;

  &:hover {
    @include GhostButtonHighlight;
  }

  &-white-transparent {
    background: transparent;
    color: var(--kui-slate-50);
    border-color: var(--kui-slate-50);

    &:hover {
      @include GhostButtonHighlight(var(--kui-slate-50), var(--kui-slate-950));
    }
  }

  &-white-transparent-reversed {
    background: var(--kui-white);
    color: var(--kui-blue-800);
    border-color: var(--kui-blue-500);

    &:hover {
      @include GhostButtonHighlight(transparent, var(--white));
    }
  }

  &-dark-transparent {
    background: transparent;
    color: var(--kui-slate-950);
    border-color: var(--kui-slate-950);

    &:hover {
      @include GhostButtonHighlight(var(--kui-slate-900), var(--kui-slate-50));
    }
  }

  &-blue-transparent {
    background: transparent;
    color: var(--kui-blue-500);
    border-color: var(--kui-blue-500);

    &:hover {
      @include GhostButtonHighlight(var(--kui-blue-500), var(--kui-white));
    }
  }

  .icon {
    width: 22px;
    height: 22px;
    @include transition;
    transform: rotate(0deg);

    &.icon-chevron {
      @include icon('icons/chevron-down-white.svg');
    }

    &.icon-play {
      @include icon('icons/play-white.svg');
    }

    &.icon-image {
      @include icon('icons/image-white.svg');
    }

    &.icon-mic {
      @include icon('icons/mic-white.svg');
    }

    &.icon-mail {
      @include icon('icons/mail-white.svg');
    }

    &.icon-news {
      @include icon('icons/news-white.svg');
    }

    &.icon-search {
      @include icon('icons/search-white.svg');
    }
  }
}

.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 18px;
  cursor: pointer;

  .icon {
    width: 20px;
  }
}
