@use 'shared' as *;

.mno-form {
  small {
    font-size: 10px;
    padding: 10px 30px;
    display: block;
  }

  &-row {
    margin-bottom: 30px;
  }

  &-group {
    color: var(--kui-green-800);
    font-size: 20px;
    font-weight: 700;
    margin: 10px 0 20px 30px;
  }

  &-label {
    display: flex;
    align-items: center;
    color: var(--kui-green-800);
    font-family: var(--kui-font-primary);
    font-size: 14px;
    font-weight: 600;
    line-height: 18px;
    margin: 0 0 10px 30px;

    &-optional {
      opacity: 0.5;
      margin-left: 10px;
      font-weight: normal;
    }

    koponyeg-info-tooltip {
      margin-left: 5px;
    }
  }

  &-input {
    @extend %input;

    &:disabled {
      background-color: var(--kui-gray-340);
    }

    &-password {
      position: relative;

      &-img {
        position: absolute;
        right: 20px;
        top: 17px;
        cursor: pointer;
      }
    }
  }

  &-textarea {
    @extend %input;
  }

  &-general-error {
    color: var(--kui-red-500);
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 20px;
  }

  &-checkbox,
  &-radio {
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer;
    font-family: var(--kui-font-primary);
    font-size: 14px;
    line-height: 18px;
    color: var(--kui-green-800);
    padding-left: 40px;
    min-height: 30px;
    margin-bottom: 20px;

    &-link {
      color: var(--kui-blue-600);
      font-weight: bold;
    }

    input {
      display: none;
    }

    &:before {
      content: '';
      display: block;
      width: 30px;
      height: 30px;
      border: 1px solid var(--kui-gray-300);
      border-radius: 8px;
      background-color: var(--kui-white);
      position: absolute;
      top: 0;
      left: 0;
    }

    input:checked {
      & + span:before {
        content: '';
        display: block;
        width: 30px;
        height: 30px;
        border-radius: 8px;
        background-color: var(--kui-blue-600);
        border: 1px solid var(--kui-blue-600);
        position: absolute;
        top: 0;
        left: 0;
      }

      & + span:after {
        content: '';
        display: block;
        width: 16px;
        height: 13px;
        background-image: url('/assets/images/icons/checkbox-checked-dark.svg');
        position: absolute;
        top: 8.5px;
        left: 7px;
      }
    }
  }

  &-radio {
    margin-bottom: 0;

    &::before {
      border-radius: 50%;
    }

    input:checked {
      & + span::before {
        border: 1px solid var(--kui-blue-600);
        border-radius: 50%;
        background-color: transparent;
      }

      & + span::after {
        background-color: var(--kui-blue-600);
        background-image: none;
        border-radius: 50%;
        width: 22px;
        height: 22px;
        top: 4px;
        left: 4px;
      }
    }
  }

  &-select {
    .ng-select-container {
      border: 1px solid var(--kui-gray-300) !important;
      background-color: var(--kui-white);
      color: var(--kui-green-800);
      font-family: var(--kui-font-primary);
      font-weight: 500;
      font-size: 16px;
      line-height: 20px;
      transition: none;
      padding: 0;
      border-radius: 26px !important;
      min-height: 50px;
      box-shadow: none !important;

      .ng-value-container {
        position: relative;
        width: 100%;
        height: 100%;
        padding: 14px 30px;

        .ng-input {
          top: 0 !important;
          padding: 0 !important;
          height: 100%;

          input {
            height: 100%;
            box-sizing: border-box !important;
            color: var(--kui-green-800);
            font-family: var(--kui-font-primary);
            font-weight: 500;
            font-size: 16px;
            line-height: 20px;
          }
        }
      }

      .ng-arrow-wrapper {
        height: 7px;
        width: 10px;
        background-image: url('/assets/images/icons/chevron-down-dark.svg');
        background-repeat: no-repeat;
        transition: transform 0.2s;
        margin-right: 15px;

        .ng-arrow {
          display: none;
        }
      }

      .ng-clear-wrapper {
        height: 8px;
        width: 8px;
        background-image: url('/assets/images/icons/dismiss-dark.svg');
        background-repeat: no-repeat;
        transition: opacity 0.2s;
        margin-right: 10px;

        &:hover {
          opacity: 0.9;
        }

        .ng-clear {
          display: none;
        }
      }
    }

    &.ng-select-opened .ng-select-container .ng-arrow-wrapper {
      transform: rotate(-180deg);
    }

    .ng-dropdown-panel {
      border: 1px solid var(--kui-gray-300) !important;
      border-radius: 26px !important;
      padding: 10px;
      box-shadow: none;
      margin-top: 5px;

      .ng-dropdown-panel-items {
        .ng-option {
          padding: 10px;
          color: var(--kui-green-800);
          font-family: var(--kui-font-primary);
          font-weight: 500;
          font-size: 16px;
          line-height: 20px;
          border-radius: 5px;
          margin: 2px 0;

          &.ng-option-marked {
            color: var(--kui-green-800);
            background: var(--kui-blue-300);
          }

          &.ng-option-selected {
            color: var(--kui-green-800);
            background: var(--kui-blue-310);
          }
        }
      }
    }

    &-option {
      display: flex;
      align-items: center;

      &-img {
        margin-right: 10px;
        flex: 0 0 30px;

        img {
          width: 30px;
          height: 30px;
          object-fit: contain;
        }
      }

      &-label {
        color: var(--kui-green-800);
        font-family: var(--kui-font-primary);
        font-weight: 500;
        font-size: 16px;
        line-height: 20px;
        white-space: break-spaces;
        text-overflow: ellipsis;
        flex: 0 0 auto;
      }

      &-clear {
        margin: 0 2px 0 5px;
        border-left: 1px solid var(--kui-blue-310);
        padding-left: 5px;
        height: 100%;
        cursor: pointer;
        display: none;

        img {
          vertical-align: top;
          margin-top: 6px;
        }
      }
    }

    &:not(.ng-select-multiple) {
      .ng-select-container {
        .ng-value-container {
          .ng-input {
            input {
              padding: 14px 30px !important;
            }
          }
        }
      }
    }

    &.ng-select-multiple {
      .ng-select-container {
        .ng-value-container {
          padding: 6px 30px 1px 25px;

          .ng-placeholder {
            padding: 0;
            top: 3px;
          }

          .ng-value {
            padding: 3px 5px;
            color: var(--kui-green-800);
            font-family: var(--kui-font-primary);
            font-weight: 500;
            font-size: 16px;
            line-height: 20px;
            border-radius: 5px;
          }
        }
      }

      .mno-form-select-option-clear {
        display: inline;
      }
    }
  }
}

.form-error {
  top: initial !important;
  right: initial !important;
  left: 30px;
  padding-top: 2px;
}

.checkbox .form-error {
  bottom: -20px;
  left: 40px !important;
}

%input {
  display: block;
  border: 1px solid var(--kui-gray-300);
  border-radius: 26px;
  background-color: var(--kui-white);
  width: 100%;
  color: var(--kui-green-800);
  font-family: var(--kui-font-primary);
  font-size: 16px;
  line-height: 20px;
  padding: 14px 30px;
  font-weight: 500;
}

input,
select,
textarea {
  font-size: 16px;
  line-height: 21px;
}
