import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  ArticleCard,
  ArticleSearchResult,
  LimitableMeta,
  NEWSLETTER_COMPONENT_TYPE,
  PrimaryColumn,
  searchResultToArticleCard,
  Sponsorship,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { Observable, Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { ConfigService, ListPageService, PortalConfigService, SearchFilterNavigationService } from '../services';
import { calculateFilters, calculatePageTypeFilters } from '../utils';
import { defaultMetaInfo } from '../constants';
import { ArticleCardType, BackendAuthorData, ListPageData } from '../definitions';

@Component({
  selector: 'app-base-search-page',
  template: '',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BaseSearchPageComponent implements OnInit, OnDestroy {
  articles: ArticleCard[] = [];
  columns: PrimaryColumn[] = [];
  authors: BackendAuthorData[] = [];
  sponsorship: Sponsorship | null = null;
  limitable?: LimitableMeta;
  globalFilter?: string;
  page = 0;
  resultsCount: number;
  maxResultsPerPage = 10;
  adverts: AdvertisementsByMedium;

  /**
   * Alternative title can be used if there is an explicit need to show a title based on some search conditions.
   * For example: article type: video => title: Videós cikkek
   */
  alternativeTitle?: string;

  readonly ArticleCardType = ArticleCardType;
  readonly unsubscribe$: Subject<boolean> = new Subject();
  isMobile$: Observable<boolean>;

  constructor(
    protected readonly route: ActivatedRoute,
    protected readonly cdr: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly searchPageService: ListPageService,
    protected readonly seo: SeoService,
    protected readonly router: Router,
    private readonly searchFilterNavigation: SearchFilterNavigationService,
    protected readonly analyticsService: AnalyticsService,
    private readonly configService: ConfigService,
    public readonly portalConfigService: PortalConfigService
  ) {}

  ngOnInit(): void {
    this.isMobile$ = this.configService.isMobile$;
    this.route.queryParams.pipe(takeUntil(this.unsubscribe$)).subscribe((params) => {
      this.alternativeTitle = '';
      let page = parseInt(params['page']);
      this.page = page ? --page : 0;

      if (params['content_types[]'] === 'articleVideo') {
        this.alternativeTitle = 'Videós cikkek';
      }
      if (params['content_types[]'] === 'articlePodcast') {
        this.alternativeTitle = 'Podcastos cikkek';
      }
      if (params['content_types[]'] === 'opinion') {
        this.alternativeTitle = 'Vélemények';
      }

      const snapshotParams = this.route.snapshot.params;
      this.globalFilter = params['global_filter'] || params['tagSlugs[]'];
      params = calculateFilters(params);
      params = calculatePageTypeFilters(calculateFilters(params), snapshotParams);
      this.onSearch(params);
    });

    (this.route.data as Observable<{ data: ListPageData }>)
      .pipe(
        map(({ data }) => data),
        takeUntil(this.unsubscribe$)
      )
      .subscribe(({ articles, limitable, columns, authors, sponsorship }) => {
        this.articles = articles;
        this.limitable = limitable;
        this.columns = columns;
        this.authors = authors || [];
        this.sponsorship = sponsorship ?? null;
        this.setMetaData();
        this.cdr.markForCheck();
      });

    this.initAds();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  onChangeFilters(filters: Record<string, string>): void {
    this.searchFilterNavigation.navigateBySearchFilters(filters);
  }

  onSearch(filters: Record<string, string>): void {
    this.searchPageService
      .searchArticle(this.page, this.maxResultsPerPage, filters)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(({ data, meta }) => {
        this.articles = data?.map((sr: ArticleSearchResult) => searchResultToArticleCard(sr));
        this.limitable = meta?.limitable;
        this.calculateResultsCount();
        this.setMetaData();
        this.cdr.markForCheck();
      });
  }

  calculateResultsCount(): void {
    const page = this.page + 1;
    const pageMaxResults = page * this.maxResultsPerPage;

    if (this.limitable?.rowAllCount) {
      this.resultsCount = pageMaxResults < this.limitable.rowAllCount ? this.limitable.rowOnPageCount! * page : this.limitable.rowAllCount;
    } else {
      this.resultsCount = pageMaxResults;
    }
  }

  onSubscriptionClick(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.LAYOUT);
  }

  protected initAds(): void {
    this.adStore.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);

      this.cdr.detectChanges();
    });
  }

  protected setMetaData(): void {
    const plainTitle = this.alternativeTitle ? this.alternativeTitle : this.globalFilter ? `Keresés: ${this.globalFilter}` : 'Keresés';
    const title = `${plainTitle} | Magyar Nemzet`;
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('kereses');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }
}
