import { Pipe, PipeTransform } from '@angular/core';
import { DateFormat } from '@trendency/kesma-core';
import { addMinutes } from 'date-fns';
import { format, toZonedTime } from 'date-fns-tz';
import { hu } from 'date-fns/locale/hu';

type ConversionOption = 'local' | 'utc';

const LOCAL_TIMEZONE = 'Europe/Budapest';

@Pipe({
  name: 'formatDate',
})
export class FormatDate implements PipeTransform {
  transform(value: string | Date, formatType: DateFormat, convertTo: ConversionOption = 'local'): string {
    let dateFormat = 'yyyy. LLLL d.';

    if (!value) {
      return value as string;
    }

    switch (formatType) {
      case 'month':
        dateFormat = 'LL';
        break;
      case 'year':
        dateFormat = 'yyyy';
        break;
      case 'date':
        dateFormat = 'yyyy.MM.dd';
        break;
      case 'h-m-s':
        dateFormat = 'HH:mm:ss';
        break;
      case 'h-m':
        dateFormat = 'HH:mm';
        break;
      case 'l-d':
        dateFormat = 'LLLL d.';
        break;
      case 'l-d-e':
        dateFormat = 'LLLL d. EEEE';
        break;
      case 'l-d-h-m':
        dateFormat = 'LLL d. HH:mm';
        break;
      case 'y-l-d-h-m':
        dateFormat = 'yyyy. LLLL d. HH:mm';
        break;
      case 'dateTime':
        dateFormat = 'yyyy.MM.dd. HH:mm:ss';
        break;
      case 'hungarianLong':
        dateFormat = 'yyyy. LLLL d. EEEE HH:mm';
        break;
      case 'hungarian':
        dateFormat = 'yyyy. LLLL d.';
        break;
      case 'y-l-m-d-h-m':
        dateFormat = 'yyyy.MM.dd. HH:mm';
        break;
    }

    const date = this.getDate(value, convertTo);

    switch (convertTo) {
      case 'local': {
        const localDate = this.convertDateFromUTCToLocal(date);
        return format(localDate, dateFormat, {
          locale: hu,
        });
      }
      case 'utc': {
        const utcDate = this.convertDateFromLocalToUTC(date);
        return format(utcDate, dateFormat, {
          locale: hu,
        });
      }
    }
  }

  private convertDateFromLocalToUTC(localDate: Date): Date {
    const utcDate = addMinutes(localDate, localDate.getTimezoneOffset());
    return utcDate;
  }

  private convertDateFromUTCToLocal(utcDate: Date): Date {
    const localDate = toZonedTime(utcDate, LOCAL_TIMEZONE);
    return localDate;
  }

  private getDate(date: string | Date, convertTo: ConversionOption): Date {
    switch (convertTo) {
      case 'local': {
        return typeof date === 'string' ? new Date(`${date.replace(' ', 'T')}Z`) : new Date(date.toISOString());
      }
      case 'utc': {
        return typeof date === 'string' ? new Date(`${date.replace(' ', 'T')}`) : date;
      }
    }
  }
}
