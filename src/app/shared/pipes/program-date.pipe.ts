import { Pipe, PipeTransform } from '@angular/core';
import { FormatDatePipe } from '@trendency/kesma-core';

@Pipe({
  name: 'programDate',
})
export class ProgramDate implements PipeTransform {
  transform(initialDate?: string | Date, secondDate?: string | Date): string | undefined {
    const formatDatePipe = new FormatDatePipe();

    if (!initialDate || !secondDate) {
      return undefined;
    }

    const startDate = formatDatePipe.transform(initialDate, 'date');
    const endDate = formatDatePipe.transform(secondDate, 'date');

    if (startDate === endDate) {
      return formatDatePipe.transform(initialDate, 'h-m');
    } else {
      return formatDatePipe.transform(initialDate, 'l-d-h-m');
    }
  }
}
