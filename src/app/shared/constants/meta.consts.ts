import { ArticleSchema } from '@trendency/kesma-core';
import { environment } from '../../../environments/environment';

export const defaultMetaInfo = {
  title: '<PERSON><PERSON><PERSON><PERSON> napilap és hírportál | <PERSON><PERSON><PERSON>',
  robots: 'index, follow, max-image-preview:large',
  description: 'Szellemi honvédelem 1938 óta.',
  ogTitle: '<PERSON><PERSON><PERSON><PERSON> napilap és hírportál | Magyar Nemzet',
  ogImageWidth: '1200',
  ogImageHeight: '600',
  ogLocale: 'hu_HU',
  // eslint-disable-next-line max-len
  ogDescription:
    'Ma<PERSON><PERSON>emzet - Szellemi honvédelem 1938 óta. Friss hírek hazai, k<PERSON><PERSON><PERSON>ld<PERSON>, sport, kultúra és gazdasági témakörökben. ' +
    'Véleménycikkek és történelmi visszatekintések.',
  ogSiteName: '<PERSON><PERSON><PERSON>',
  ogType: 'website',
  twitterImage: '@MagyarNemzetOn',
};

export const galleryMetaInfo = {
  title: 'Galéria - Magyar Nemzet',
  robots: 'index, follow, max-image-preview:large',
  ogType: 'gallery',
};
export const weatherMetaInfo = {
  title: 'Napi időjárás, előrejelzés',
  //eslint-disable-next-line max-len
  description:
    'Budapest és Pest megyei települések részletes időjárási adatai, hőmérséklet, csapadék, légnyomás értékek. ' +
    'Mindennap frissülő országos időjárás előrejelzés.',
};

export const SchemaOrgWebpageDataTemplate: ArticleSchema = {
  '@type': 'WebPage',
  name: 'Magyar Nemzet',
  url: `${environment?.siteUrl}`,
  image: `${environment?.siteUrl}/assets/images/magyar-nemzet.png`,
  publisher: {
    '@type': 'Organization',
    name: 'Mediaworks Hungary Zrt.',
    logo: {
      '@type': 'ImageObject',
      height: '112',
      width: '988',
      url: `${environment?.siteUrl}/assets/images/mw/mw-logo-2024-red-white.svg`,
    },
    address: [
      {
        '@type': 'PostalAddress',
        streetAddress: 'Üllői út 48.',
        addressLocality: 'Budapest',
        postalCode: '1082',
        addressCountry: 'HU',
      },
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: ' +36 1 999 47 60',
      email: '<EMAIL>',
    },
  },
};
