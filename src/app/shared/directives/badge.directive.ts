import { Directive, Input, OnInit } from '@angular/core';
import { IconComponent } from '../components/icon/icon.component'; // Do not shorten this to avoid circular dependency

@Directive({
  selector: '[mnoBadge]',
})
export class BadgeDirective implements OnInit {
  @Input() set type(value: string) {
    this.#type = value;
    this.updateComponent();
  }

  @Input() set showLabel(value: boolean) {
    this.#showLabel = value;
    this.updateComponent();
  }

  @Input() set label(value: string) {
    this.#label = value;
    this.updateComponent();
  }

  @Input() set size(value: number) {
    this.#size = value;
    this.updateComponent();
  }

  @Input() set color(value: string) {
    this.#color = value;
    this.updateComponent();
  }

  @Input() set icon(value: string) {
    this.#icon = value;
    this.updateComponent();
  }

  #type?: string;
  #icon = '';
  #color = 'dark';
  #size = 24;
  #label = '';
  #showLabel = true;

  constructor(private readonly component: IconComponent) {}

  ngOnInit(): void {
    this.updateComponent();
  }

  private updateComponent(): void {
    switch (this.#type) {
      case 'opinion':
        this.#label = 'Vélemény';
        this.#icon = 'idezojelek';
        this.#color = 'darkblue';
        this.#showLabel = false;
        break;
      case 'video':
        this.#color = 'blue';
        this.#label = 'Video';
        this.#icon = 'video-clip';
        break;
      case 'podcast':
        this.#color = 'blue';
        this.#label = 'Podcast';
        this.#icon = 'mic';
        break;
      case 'gallery':
        this.#color = 'blue';
        this.#label = 'Galéria';
        this.#icon = 'image';
        break;
      case 'adult':
        this.#color = 'red';
        this.#label = 'Felnőtt tartalom';
        this.#showLabel = false;
        this.#icon = '18-circle';
        break;
      default:
        this.#icon = this.type ?? 'placeholder';
    }
    this.component.color = this.#color;
    this.component.icon = this.#icon;
    this.component.label = this.#label;
    this.component.showLabel = this.#showLabel;
    this.component.size = this.#size;
  }
}
