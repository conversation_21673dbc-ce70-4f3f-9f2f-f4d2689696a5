import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
/**
 * Prevents the pager queryparam to be other than a valid positive number.
 * In every other case it will redirect to 404.
 * This only checks the validity of the parameter as a valid number,
 * it does not take into account if the given page really exists on the list.
 */
export class PagerValidatorGuard {
  constructor(private readonly router: Router) {}

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    //If ?page= in queryparams
    if (
      route.queryParams['page'] &&
      (isNaN(route.queryParams['page']) || //not a number
        route.queryParams['page'] <= 0 || //less than 0
        route.queryParams['page'] > Number.MAX_SAFE_INTEGER) // bigger than the maximum integer that J<PERSON> can use without exponents
    ) {
      //If any of the above conditions satisfied, prevent activation and redirect to 404.
      this.router.navigate(['/', '404'], {
        skipLocationChange: true,
      });
      return false;
    }
    return true;
  }
}
