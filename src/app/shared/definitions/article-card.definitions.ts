export const PALCEHOLDER_IMG = 'magyar-nemzet.png';

export enum ArticleCardType {
  Img16TopTagsTitleLeadBadge = 1,
  Img16TopTagsTitleLeadLarge,
  Img16TopTagsTitleLeadLargeBorder,
  Img4TopTitleLeadBadgeLarge,
  ImgRightTagsTitleLeadBadge,
  ImgRightTagsTitleBadgeSmall,
  ImgRightTagsTitleLeadWide,
  ImgRightTagsTitleLead50,
  NoImgBorderLeftTagsTitleLeadBadge,
  NoImgBorderAllTagsTitleLeadBadge,
  NoImgBorderBottomRightDateTitleBadge,
  DateImgRightTagsTitleLeadWide,
  DateImgRightTagsTitleLeadWideLarge,
  NoImgTitleBadge,
  Img4TopTagsTitleLeadSmallBorder,
  Img1TopTagsTitleLeadBadge,
  ImgRightTagsTitleLeadWideBorder,
  ExternalRecommendation,
  RelatedArticle,
  Gallery,
  Podcast,
  NoImgNoBorderAllTagsTitleLeadBadge,
}

export enum BrandingBoxBrand {
  Mindmegette = 'mindmegette',
  Orszagszerte = 'orszagszerte',
  Automotor = 'automotor',
  Unilife = 'unilife',
  Lakaskultura = 'Lakaskultura',
  Magyarnemzet = 'magyarnemzet',
  Vilaggazdasag = 'vilaggazdasag',
}
