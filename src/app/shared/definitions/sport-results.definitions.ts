import { ChampionshipSchedule, Column, LeagueTeam, LiveSport, Phase, Player, Team } from '@trendency/kesma-ui';

export type ScheduleByCompetitions = {
  schedules: ChampionshipSchedule[];
};

export type SportCompetitions = {
  publicTitle: string;
  competition: Column;
  order?: number;
  currentRound?: string;
  hasPhases?: boolean;
};
export type SportPhases = {
  groups: SportPhasesItem[];
  playOff: SportPhasesItem;
  qualifications: SportPhasesItem[];
};

export type SportPhasesItem = {
  id: string;
  name: string;
  rounds: {
    [round: string]: LiveSchedule[];
  }[];
};

export type TimeZone = Readonly<{
  date: Date;
  timezone_type: number;
  timezone: string;
}>;

export declare type LiveSchedule = {
  homeScore: string;
  awayScore: string;
  slug: string;
  sportCategory?: string;
  homeTeam: LeagueTeam;
  awayTeam: LeagueTeam;
  liveEvents?: LiveEvent[];
  scheduleDate?: TimeZone;
  id?: string;
  scheduleTime?: string;
  visitors?: number;
  information?: string;
  homeLineupForm?: string;
  awayLineupForm?: string;
  round?: string;
  laps?: string;
  playoffGroup?: string;
  scheduleStatus?: string;
  competition: LiveScheculeCompetition;
  phase?: Phase;
  facility: LiveScheduleFacility;
  tvStation?: string;
  referees?: any[];
  assistants?: any[];
  players?: any[];
};

export type ScheduleEvent = Readonly<{
  id: string;
  icon: string;
}>;

export type LiveEvent = Readonly<{
  id: string;
  eventText: string;
  minute: string;
  scheduleEvent: ScheduleEvent;
  competitionTeamPlayer?: Player;
  competitionTeam?: Team;
}>;

export type LiveScheduleFacility = {
  id: string;
  title: string;
  publicTitle: string;
  address: string;
  capacity: number;
  latitude: string;
  longitude: string;
};

export type LiveScheculeCompetition = {
  id: string;
  title: string;
  slug: string;
  publicTitle: string;
  logo: string;
  season: CompetitionSeason;
  tabellaStatus: true;
  column?: Column[];
  position: number;
};

export type CompetitionSeason = {
  id: string;
  title: string;
  seasonStart: number;
  seasonEnd: number;
  liveSport: LiveSport;
};
