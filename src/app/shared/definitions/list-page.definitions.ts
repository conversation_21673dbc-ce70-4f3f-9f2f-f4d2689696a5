import { ArticleCard, LimitableMeta, PrimaryColumn, Sponsorship, Tag } from '@trendency/kesma-ui';
import { BackendAuthorData } from './search.definitions';

export interface ListPageData {
  articles: ArticleCard[];
  limitable: LimitableMeta;
  columns: PrimaryColumn[];
  tags?: Tag[];
  authors?: BackendAuthorData[];
  author?: BackendAuthorData;
  sponsorship?: Sponsorship | null;
  title?: string;
}
