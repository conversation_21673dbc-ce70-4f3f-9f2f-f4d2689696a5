export enum SocialInteractionEventType {
  LinkCopy = 'link-copy',
  EmailShare = 'email-share',
  FacebookShare = 'facebook-share',
  TwitterShare = 'twitter-share',
  Reaction = 'reaction',
  Like = 'like',
  Comment = 'comment',
  MobileShare = 'mobile-share',
}

export class SocialInteractionEvent {
  constructor(
    public event: SocialInteractionEventType,
    public url?: string,
    public title?: string,
    public linkText?: string,
    public publishDate?: Date
  ) {}
}

declare global {
  interface Window {
    AndroidShareHandler?: {
      share: (text: string) => void;
    };
  }
}
