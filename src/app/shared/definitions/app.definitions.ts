import { ArticleBodyDetailType, ArticleBodyType, VoteData } from '@trendency/kesma-ui';

export type WeatherApi = Readonly<{
  city: string;
  icon2: string;
  sky_view: string;
  temperature: string;
}>;

export type MediaStreamApi = Readonly<{
  hirTv: HirTv;
  karcFm: KarcFm;
}>;

export type KarcFm = Readonly<{
  stream: string;
}>;

export type HirTv = Readonly<{
  subTitle: string;
  stream: string;
  title: string;
}>;

export type SearchForm = Readonly<{
  searchBar: string;
  columnSelector: string;
  fromDate: string;
  toDate: string;
  author: string;
}>;
export type GlobalFilter = Readonly<{
  global_filter?: string;
}>;
export type AuthorFilter = Readonly<{
  author?: string;
}>;

export type TagFilter = Readonly<{
  column?: string;
}>;

export type SearchQuery = Readonly<{
  global_filter?: string;
  author?: string;
  from_date?: string;
  to_date?: string;
  column?: string;
}>;

export type SelectValues = Readonly<{
  title: string;
  slug: string;
}>;

export type TimeframeFilter = Readonly<{
  from_date?: string;
  to_date?: string;
}>;
export type SearchResults = Readonly<{
  columnSlug?: string;
  columnTitle?: string;
  contentType?: string;
  lead?: string;
  length?: string;
  publishDate?: string;
  slug?: string;
  title?: string;
  brand?: string;
}>;

export type ArticleSearch = Readonly<{
  data: SearchResults[];
  meta: any;
}>;
export type ArticleSearchMeta = Readonly<{
  responseType: string;
  dataCount: number;
  requestUrl: string;
  filterable: ArticleSearchFilters;
  orderable: any;
  limitable: Limitable;
}>;
export type Limitable = Readonly<{
  pageCurrent: number;
  pageMax: number;
  rowAllCount: number;
  rowFrom: number;
  rowOnPageCount: number;
}>;
export type ArticleSearchFilters = Readonly<{
  global_filter: ArticleSearchFilterParms;
  author: ArticleSearchFilterParms;
  content_types: ArticleSearchFilterParms;
  column: ArticleSearchFilterParms;
  tags: ArticleSearchFilterParms;
  from_date: ArticleSearchFilterParms;
  to_date: ArticleSearchFilterParms;
}>;
export type ArticleSearchFilterParms = Readonly<{
  requestQueryKey: string;
  sourceUrl: string;
  selectKeyProperty: string;
  selectDisplayProperty: string;
}>;

export type ArticleCategory = Readonly<{
  slug: string;
  name?: string;
}>;

export type SearchQueryParam = Readonly<{
  global_filter?: string;
  author?: string;
  from_date?: string;
  to_date?: string;
  column?: string;
  page: string;
}>;

export type AllColumnsResponse = Readonly<{
  data: AllColumns[];
  meta: Meta;
}>;

export type ArticleResponse = Readonly<{
  data: ArticleCard[];
  meta: Meta;
}>;

export type ReviewsResponse = {
  data: ArticleReview[];
  meta: Meta;
};

export interface Meta {
  readonly dataCount: number;
  readonly filterable: Filterable;
  readonly limitable: Limitable;
  readonly orderable: Orderable;
  readonly requestUrl: string;
  readonly responseType: string;
}

interface Filterable {
  readonly global_filter?: Filter;
}

interface Filter {
  readonly requestQueryKey: string;
  readonly sourceUrl?: string;
  readonly selectKeyProperty?: any;
  readonly selectDisplayProperty?: any;
}

interface Orderable {
  readonly title_order?: Order;
}

interface Order {
  readonly requestQueryKey: string;
}

export type ArticleCard = Readonly<{
  id?: string;
  title: string;
  slug: string;
  category: ArticleCategory;
  publishDate: string | Date;
  readingTime: string;
  label: Label;
  length?: number;
  lead?: string;
  thumbnail?: ThumbnailImage;
  author?: Author;
  publishYear: string | number;
  publishMonth: string | number;
  sponsorTitle?: string;
  contentType?: string;
  columnTitle?: string;
  leadingArticle?: boolean;
  preTitle?: string;
  hasLenia?: boolean;
  thumbnailUrl?: string;
  brandingType?: string;
  brand?: string;
  columnSlug?: string;
  excerpt?: string;
}>;

export type Author = Readonly<{
  name: string;
  avatarUrl?: string;
  avatar?: string;
}>;

export type ThumbnailImage = Readonly<{
  url: string;
  alt?: string;
}>;

export type Label = Readonly<{
  text: string;
}>;

export type ITag = Readonly<{
  id: string;
  slug?: string;
  title: string;
}>;
export type IArticleResponse = Readonly<{
  id?: string;
  lead: string;
  title: string;
  printTitle: string;
  publicAuthor: string;
  publicAuthorEmail: string;
  type: string;
  isAdultsOnly?: boolean;
  tags: ITag[];
  body: IComponentData[];
  publishDate: string;
  primaryColumn: IColumn;
  avatar?: string;
  thumbnail?: string;
  description?: string;
  withoutAds?: boolean;
  aniCode?: string;
  lastUpdated?: string;
  subTitle: string;
  minuteToMinute?: MinuteToMinuteState;
  minuteToMinuteBlocks: MinuteToMinuteBlock[];
  languages: ArticleLanguages[];
  articleSource: string;
  dossier: { slug: string; thumbnailUrl: string; title: string };
  thumbnailUrl?: string;
  technicalMetaTag?: string;
  seo?: ArticleSeoFields;
  topicLevel1?: string;
  topicLevel2?: string;
  topicLevel3?: string;
  reviews?: ArticleReview[];
}>;

export type ArticleReview = {
  body: Body;
  id: string;
  publicAuthorAvatarThumbnailUrl: string;
  publicAuthorId: string;
  publicAuthorImageUrl: string;
  publicAuthorName: string;
  publicAuthor?: {
    authorData?: {
      fullName: string;
      slug: string;
    };
    avatarFullSizeUrl?: string;
    avatarThumbnail?: string;
  };
  slug: string;
  title: string;
};

export type User = Readonly<{
  id: string;
  fullName: string;
}>;

export type ArticleSeoFields = Readonly<{
  seoTitle?: string;
  seoDescription?: string;
  seoMainKeyword?: string;
  seoOtherKeywords?: string;
  seoLongTailKeywords?: string;
  seoCanonicalUrl?: string;
  seoRobotsMeta?: string;
}>;

export type IDate = Readonly<{
  date: string;
}>;
export type IColumn = Readonly<{
  title: string;
  seoLead: string;
  seoTitle: string;
  slug: string;
  thumbnail: ThumbnailImage;
  tags: ITag[];
}>;
export type IComponentData = Readonly<{
  type: ArticleBodyType;
  uuid: string;
  key: string;
  subComponents: IComponentData[];
  details: IComponentFieldData[];
  autoplay?: boolean;
  adId?: number;
  cssClass?: string;
}>;
export type ArticleRecommendation = Readonly<{
  highPriorityArticles: IArticle[];
  lowPriorityArticles: IArticle[];
  brandingBoxes?: IArticle[];
  videos: ArticleCard[];
  externalRecommendation: ExternalRecommendation[];
}>;

export type IArticle = Readonly<{
  title: string;
  excerpt: string;
  slug: string;
  typeName?: string;
  publishDate?: string;
  category?: ArticleCategory;
  columnSlug?: string;
}>;

export type ArticleCategoryParams = Readonly<{
  columnSlug?: string;
  rowCount_limit?: string;
  page_limit?: string;
  'excludedArticleIds[]'?: string[];
  year?: string;
  month?: string;
  rowFrom_limit?: string;
}>;

export type IComponentFieldData = Readonly<{
  type: ArticleBodyDetailType;
  inputType: string;
  key: string;
  uuid: string;
  value: SubsequentDossierValue | string;
  multiple?: boolean;
  properties?: any;
  valueDetails?: any;
}>;

export type SubsequentDossierValue = Readonly<{
  coverImage: string;
  headerColor: string;
  id: string;
  isActive: boolean;
  isDeleted: string;
  slug: string;
  relatedArticles: DossierRelatedArticles[];
  title: string;
}>;

export type Suggestion = Readonly<{
  imagePath?: string;
  url?: string;
  title?: string;
  lead?: string;
  rss_content_id?: string;
  created_time?: string;
  spr?: number;
  siteName?: string;
  data_orig_lap_id?: string;
  url_original?: string;
}>;

export type DossierRelatedArticles = Readonly<{
  columnId: string;
  columnParentId?: string;
  columnParentSlug?: string;
  columnParentTitle?: string;
  columnSlug: string;
  columnTitle: string;
  publishDate: string;
  slug: string;
  id: string;
  title: string;
}>;
export type ExternalRecommendation = Readonly<{
  spr: string;
  imagePath: string;
  siteName: string;
  title: string;
  url: string;
}>;

export type DateFormat =
  | 'month'
  | 'year'
  | 'date'
  | 'h-m-s'
  | 'h-m'
  | 'l-d'
  | 'l-d-e'
  | 'l-d-h-m'
  | 'y-l-d-h-m'
  | 'y-l-m-d-h-m'
  | 'dateTime'
  | 'hungarian'
  | 'hungarianLong';

export type RelatedObj = Readonly<{
  id: string;
  slug: string;
  title: string;
  publishDate?: { date?: string };
  columnSlug?: string;
}>;

export type HeaderResponse = Readonly<{
  id: string;
  title: string;
  isActive: boolean;
  sort: number;
  targetBlank: boolean;
  children: HeaderResponse[];
  related?: RelatedObj;
  relatedKey?: string;
  relatedType: string;
  customUrl?: string;
  link?: ILink;
  subLinks?: HeaderResponse[];
}>;

export type MenuResponse = Readonly<{
  footer: HeaderResponse[];
  header: HeaderResponse[];
}>;

export type ILink = Readonly<{
  routerLink: string[];
  href: string;
  targetBlank?: boolean;
}>;

export type GalleriesResponse = Readonly<{
  highlightedImageUrl: string;
  id: string;
  publicDate: string;
  slug: string;
  title: string;
}>;

export type GallerySize = 'large';

export type AllColumns = Readonly<{
  id: string;
  title: string;
  slug: string;
  description: string;
  showInRss: string;
  parentId: string;
  parentTitle: string;
  parentDescription: string;
  parentShowInRss: any;
  data: any;
}>;
export type ColumnsResponse = Readonly<{
  data: AllColumns[];
}>;

export type TagsResponse = Readonly<{
  articles: { data: Array<TagsArticle>; meta: any };
}>;
export type TagsArticle = Readonly<{
  id: string;
  isActive: string;
  publishDate: string;
  slug: string;
  title: string;
  excerpt: string;
  columnId: number;
  columnTitle: string;
  avatarThumbnail: string;
  thumbnail: string;
  isOpinion: string;
}>;
export type ProgramTypes = Readonly<{
  id: string;
  isActive: string;
  isHighlighted: string;
  slug: string;
  title: string;
}>;

export interface ProgramSelects {
  id?: string;
  title: string;
  slug: string;
}

export interface ProgramDateItem {
  title: string;
  slug: string;
  date_from?: Date;
  date_until?: Date;
}

export type RecommendedArticle = Readonly<{
  avatarThumbnail: string;
  avatarThumbnailCreatedAt: string;
  columnId: string;
  columnSlug: string;
  columnTitle: string;
  excerpt: string;
  id: string;
  isActive: string;
  isOpinion: string;
  publishDate: string;
  readingLength: string;
  slug: string;
  thumbnail: string;
  thumbnailCreatedAt: string;
  title: string;
}>;

export interface ProgramQueryParams {
  'program_type[]'?: ProgramSelects[];
  'program_location[]'?: ProgramSelects[];
  date?: ProgramSelects;
  dateName?: ProgramSelects;
  date_from?: string;
  date_until?: string;
  rowCount_limit?: number;
  page_limit?: number;
}

export interface DatePare {
  date_from?: string;
  date_until?: string;
}

export interface ProgramQueryParamsFromUrl {
  'program_type[]'?: ProgramSelects;
  'program_location[]'?: ProgramSelects;
  date?: ProgramSelects;
  dateName?: ProgramSelects;
  date_from?: Date;
  date_until?: Date;
  rowCount_limit?: number;
  page_limit?: number;
}

export type Body = {
  details: BodyDetail[];
  id: string;
  subComponents: string[];
  type: string;
};

export type BodyDetail = {
  key: string;
  type: string;
  value: string;
};

export type ProgramDetail = Readonly<{
  body: Body[];
  externalURL: string;
  dates: { startDate: string; endDate: string }[];
  id: string;
  isActive: boolean;
  lead: string;
  listedLocations: string[];
  programLocations: ProgramSelects[];
  programTypes: ProgramSelects[];
  tags: ProgramSelects[];
  slug: string;

  title: string;
  longitude: string;
  latitude: string;
}>;

export type ProgramRecommendations = Readonly<{
  date: Date;
  programRecommendations: ProgramRecommendationItem[];
}>;

export type ProgramRecommendationItem = Readonly<{
  endDate: string;
  id: string;
  isActive: string;
  lead: string;
  listedLocations: string[];
  locations: { slug: string; title: string }[];
  types: { slug: string; title: string }[];
  tags: { slug: string; title: string }[];
  slug: string;
  startDate: string;
  title: string;
  categorySlug: string;
  image: string;
}>;

export type googleMapsOptions = Readonly<{
  lat: number;
  lng: number;
  zoom?: number;
  iconUrl?: string;
  iconWidth?: number;
  iconHeight?: number;
  disableDefaultUI: boolean;
}>;

export type WysiwygMain = Readonly<{
  id: string;
  details: WysiwygText[];
  type: string;
  subComponents: any;
}>;

export type WysiwygText = Readonly<{
  type: string;
  key: string;
  value: string;
}>;

export type MinuteToMinuteState = 'not' | 'running' | 'closed';

export type MinuteToMinuteBlock = Readonly<{
  title: string;
  date: Date;
  body: IComponentData[];
}>;

export type Dossier = Readonly<{
  articles: DossierArticle[];
  slug: string;
  thumbnail: string;
  title: string;
}>;

export type Dossiers = Readonly<{
  data: Dossier[];
  meta: any;
}>;

export enum DossierCardTypes {
  VERTICAL = 1,
  HORIZONTAL,
}

export type DossierArticle = Readonly<{
  title: string;
  slug: string;
  columnSlug: string;
  publishDate: string;
}>;

export enum ArticleCardTypes {
  WITH_IMAGE = 1,
  ONLY_TITLE,
  ONLY_TEXT,
  ONLY_TITLE_STYLED,
  THUMBNAIL_LEFT,
  NO_THUMBNAIL,
  RECOMMENDED,
}

export enum OpinionCardTypes {
  QUATATION = 1,
  WITH_IMAGE,
  NO_IMAGE,
}

export type ArticleCardRecommended = Readonly<{
  id: string;
  title: string;
  dbCache: {
    slug: string;
    thumbnailUrl: string;
    lead: string;
  };
  primaryColumn: {
    title: string;
    slug: string;
  };
  publishDate: {
    date: string;
    timezone_type: number;
    timezone: string;
  };
}>;

export type DossierSlugResponse = Readonly<{
  data: ArticleCard[];
  meta: any;
}>;

export type ArticleLanguages = Readonly<{
  columnTitle: string;
  columnSlug: string;
  languageTitle: string;
  languageSlug: string;
  articleSlug: string;
  articlePublishDate: string;
}>;

export type HtmlEmbed = Readonly<{
  desktop: string;
  mobile: string;
}>;

export interface TagLink {
  readonly text: string;
  readonly url: string;
  readonly styleId: number;
  isExternal?: boolean;
}

export type MenuType = 'header' | 'footer';

export type DateSelectorType = 'today' | 'tomorrow' | 'this_weekend' | 'next_week';

export type DossierBox = Readonly<{
  title: string;
  headerImage: string;
  slug: string;
  mainArticle: {
    title: string;
    publishDate: string;
    readingTime: string;
    lead?: string;
    thumbnail?: string;
    thumbnailUrl?: string;
    columnSlug?: string;
  };
  secondaryArticles: {
    title: string;
    publishDate: string;
    readingTime: string;
    lead?: string;
    thumbnail?: string;
    thumbnailUrl?: string;
    columnSlug?: string;
  }[];
}>;

export type ArticleDossierData = Readonly<{
  title?: string;
  thumbnailUrl?: string;
  slug?: string;
  mainArticle?: ArticleDossierItem;
  secondaryArticles?: ArticleDossierItem[];
}>;

export type ArticleDossierItem = Readonly<{
  columnId: string;
  columnSlug: string;
  columnTitle: string;
  contentType: string;
  lead: string;
  length: string;
  publishDate: string;
  slug: string;
  thumbnail: string;
  thumbnailCreatedAt: string;
  title: string;
}>;

export type EndDate = Readonly<{
  date: string;
  timezone: string;
  timezone_type: number;
}>;

export type VoteAnswers = Readonly<{
  id: string;
  answer: string;
  voteCount: string;
  order: string;
  votePercentage: number;
}>;

export type ArticleVoteData = Readonly<{
  type: string;
  id: string;
  subComponents: string;
  details: ArticleVoteDetails[];
}>;
export type ArticleVoteDetails = Readonly<{
  key: string;
  type: string;
  value: VoteData;
}>;

export type VoteSubmitResponse = Readonly<{
  data: VoteData;
  meta: VoteSubmitResponseMeta;
}>;

export type VoteSubmitResponseMeta = Readonly<{
  dataCount: 4;
  requestUrl: string;
  responseType: string;
}>;
export interface StaticPageResponse {
  body: IComponentData[];
  id: string;
  publishDate: IDate;
  slug: string;
  title: string;
  type: string;
}

export type GalleriesAndListAdvertisment = Readonly<{
  desktop: {
    roadblock_1_top: string;
    roadblock_2_below_top: string;
    roadblock_3_above_bottom: string;
    roadblock_4_bottom: string;
  };
  mobile: {
    medium_rectangle_1_top: string;
    medium_rectangle_2_below_top: string;
    medium_rectangle_3_above_bottom: string;
    medium_rectangle_4_bottom: string;
  };
}>;
export type ArticleAdvertisment = Readonly<{
  roadblock_1_top: string;
  roadblock_2_below_top: string;
  roadblock_3_above_bottom: string;
  roadblock_4_bottom: string;
  medium_rectangle_1_top: string;
  medium_rectangle_2_below_top: string;
  medium_rectangle_3_above_bottom: string;
  medium_rectangle_4_bottom: string;
}>;

export type VisergradPostArticle = Readonly<{
  columnSlug: string;
  desc: string;
  id: string;
  img: string;
  publishDate: string;
  slug: string;
  title: string;
}>;

export type BreakingNews = Readonly<{
  excerpt: string;
  lead: string;
  publicAuthor: string;
  publicAuthorAvatarFullSizeUrl: string;
  publicAuthorAvatarThumbnailUrl: string;
  publicAuthorEmail: string;
  readingLength: string;
  slug: string;
  thumbnailUrl: string;
  title: string;
  columnSlug: string;
  publishDate: string;
}>;

export type PreviewBackendVoteArticle = Readonly<{
  column: { title: string; slug: string };
  preTitle: string;
  publishDate: string;
  slug: string;
  thumbnailUrl: string;
  title: string;
}>;
