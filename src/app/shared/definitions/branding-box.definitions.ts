export interface BackendSearchArticle {
  id: string;
  thumbnail: string;
  secondaryThumbnail: string;
  avatarImage: string;
  columnSlug: string;
  columnTitle: string;
  columnTitleColor: string;
  contentType: string;
  author: string;
  firstContributorPublicAuthorName: string;
  createdBy: string;
  preTitle: string;
  preTitleColor: string;
  title: string;
  lead: string;
  publishDate: string;
  length: string;
  slug: string;
  foundationTagSlug?: string;
}

export interface SearchArticle {
  id: string;
  slug: string;
  thumbnail: string;
  publishDate: Date;
  columnSlug: string;
  title: string;
  lead: string;
  contentType: string;
  foundationTagSlug?: string;
}

export type BrandingType = 'mindmegette' | 'orszagszerte' | 'auto-motor' | 'automotor' | 'lakaskultura' | 'unilife';
