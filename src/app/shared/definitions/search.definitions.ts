export interface ISearchParams {
  readonly global_filter?: string;
  readonly from_date?: string;
  readonly to_date?: string;
  readonly 'columnSlugs[]'?: string[];
  readonly page_limit?: string;
  readonly rowCount_limit?: string;
  readonly author?: string;
  readonly 'publishDate_order[0]'?: 'asc' | 'desc';
  readonly 'content_types[]'?: string[];
  readonly 'tagSlugs[]'?: string[];
  readonly 'priorityIds[]'?: string[];
  readonly material_types_only?: string;
}

export type AuthorData = Readonly<{
  readonly facebook: string;
  readonly instagram: string;
  readonly publicAuthorDescription: string;
  readonly publicAuthorName: string;
  readonly tiktok: string;
  readonly avatar: AuthorDataAvatar;
  readonly rank?: string;
  readonly id?: string;
}>;

export interface AuthorDataAvatar {
  readonly fullSizeUrl: string;
  readonly thumbnailUrl: string;
  readonly variantId: number;
  readonly altText?: string;
}

export type BackendAuthorData = Readonly<
  AuthorData & {
    public_author_name: string;
    public_author_description?: string;
    avatar?: string;
    slug?: string;
    public_author?: string;
    title?: string;
    opinionAuthor?: boolean;
  }
>;

export interface BackendColumnData {
  id?: string;
  title?: string;
  slug?: string;
}

export interface SearchPageData {
  data?: any;
  columns?: BackendColumnData[];
  authors?: BackendAuthorData[];
}
