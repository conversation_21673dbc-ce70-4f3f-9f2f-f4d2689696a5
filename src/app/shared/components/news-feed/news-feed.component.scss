@use 'shared' as *;

:host {
  display: block;

  .news-feed {
    &-header {
      background-color: var(--kui-blue-800);
      color: var(--kui-white);
      display: flex;
      padding: 6px 8px;
      align-items: center;
      gap: 6px;
      font-size: 24px;
      font-weight: 700;
      line-height: 28px;
      margin-bottom: 8px;

      i {
        width: 20px;
        height: 20px;
      }
    }

    &-content-container {
      display: flex;
      flex-direction: column-reverse;
      gap: 8px;

      @include media-breakpoint-up(md) {
        flex-direction: row;
        gap: 40px;
      }

      div {
        flex: 1;
      }
    }

    &-thumbnail {
      object-fit: cover;
      aspect-ratio: 16 / 9;
      width: calc(66% - 7px);
      height: fit-content;
      @include media-breakpoint-down(sm) {
        width: 100%;
      }
    }

    &-title {
      color: var(--kui-gray-800);
      font-size: 24px;
      font-weight: 700;
      line-height: 28px;
      padding-bottom: 16px;
      border-bottom: 1px solid var(--kui-blue-200);
    }

    &-article {
      padding: 8px 0;
      display: flex;
      gap: 8px;
      border-bottom: 1px solid var(--kui-blue-200);
    }

    &-date {
      display: flex;
      height: fit-content;
      border-radius: 4px;
      padding: 4px 2px;
      justify-content: center;
      align-items: center;
      border: 1px solid var(--kui-blue-800);
      color: var(--kui-blue-800);
      font-size: 14px;
      font-weight: 700;
      line-height: 18px;
    }
  }
}
