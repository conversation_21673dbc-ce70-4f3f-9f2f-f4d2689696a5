import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, DossierData } from '@trendency/kesma-ui';
import { ArticleCardType } from '../../definitions';
import { FormatDatePipe } from '@trendency/kesma-core';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { NgFor, NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'mno-news-feed',
  templateUrl: './news-feed.component.html',
  styleUrls: ['./news-feed.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgIf, NgFor, ArticleCardComponent, FormatDatePipe],
})
export class NewsFeedComponent extends BaseComponent<DossierData<Date>> {
  public dossierLink?: string[];

  readonly ArticleCardType = ArticleCardType;

  override ngOnInit(): void {
    this.dossierLink = ['/', 'hirfolyam', this.data?.slug as string];
  }
}
