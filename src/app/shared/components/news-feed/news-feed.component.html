<div class="news-feed-header" [routerLink]="dossierLink">
  <i class="icon news-feed"></i>
  Hírfolyam
</div>
<div class="news-feed-content-container">
  <div>
    <a [routerLink]="dossierLink">
      <h2 class="news-feed-title">
        {{ data?.title }}
      </h2>
    </a>
    <ng-container *ngIf="data?.secondaryArticles as articles">
      <div class="news-feed-article" *ngFor="let article of articles; trackBy: trackByFn">
        <ng-container *ngIf="article">
          <span class="news-feed-date" *ngIf="article?.publishDate">{{ article.publishDate | formatDate: 'h-m' }}</span>
          <article mno-article-card [styleID]="ArticleCardType.RelatedArticle" [data]="article"></article>
        </ng-container>
      </div>
    </ng-container>
  </div>
  <img class="news-feed-thumbnail" [src]="data?.headerImage || 'assets/placeholder.svg'" alt="Hírfolyam" loading="lazy" />
</div>
