<div class="image-wrapper">
  @if (currentQuestionIndex !== data?.questions?.length) {
    <img class="quiz-question-image" [src]="getQuizImage(currentQuestion?.image) || placeholderImage" alt="Kv<PERSON><PERSON> kérd<PERSON> á<PERSON> kép." loading="lazy" />
  }
  @if (rating && currentQuestionIndex === data?.questions?.length) {
    <img class="quiz-question-image" [src]="getQuizImage(rating?.image) || placeholderImage" alt="Kvíz eredményt ábrázoló kép." loading="lazy" />
  }
</div>

<div class="quiz-question">
  <span class="quiz-stepper"
    >{{ rating && currentQuestionIndex === data?.questions?.length ? 'Eredmény' : currentQuestionIndex + 1 + ' / ' + data?.questions?.length }}
  </span>
  <h5 class="quiz-question-text">
    {{ rating && currentQuestionIndex === data?.questions?.length ? rating.text : currentQuestion?.title }}
  </h5>
</div>

<div class="answer-list" *ngIf="currentQuestionIndex !== data?.questions?.length">
  <div
    class="answer-list-item"
    *ngFor="let answer of currentQuestion?.answers; let answerIndex = index"
    [class.wrong]="!answer.isCorrect && givenAnswers[currentQuestionIndex] !== undefined && givenAnswers[currentQuestionIndex] === answerIndex"
    [class.correct]="answer.isCorrect && givenAnswers[currentQuestionIndex] !== undefined"
    [class.selected]="selectedAnswer"
    #listItem
    (click)="
      givenAnswers[currentQuestionIndex] !== undefined ? $event.stopPropagation() : onSelectAnswer(currentQuestionIndex, answerIndex); onPickAnswer(answer)
    "
  >
    <input class="radio-input" type="radio" [name]="'answer_' + currentQuestion?.id" [id]="'answer_' + currentQuestion?.id + '_' + answerIndex" />
    <label
      class="radio-label"
      [for]="'answer_' + currentQuestion?.id + '_' + answerIndex"
      [class.hidden-original-circle]="listItem.classList.contains('correct') || listItem.classList.contains('wrong')"
    >
      {{ answer.title }}
      <span class="extra-label correct">
        <kesma-icon name="icon-correct-answer" [size]="24" />
      </span>
      <span class="extra-label wrong">
        <kesma-icon name="icon-wrong-answer" [size]="24" />
      </span>
    </label>
    <label class="result-label">
      {{ answer.isCorrect ? 'Helyes válasz' : 'Helytelen válasz' }}
    </label>
  </div>
</div>

<div class="quiz-footer">
  <button
    *ngIf="isNextButtonVisible && currentQuestionIndex !== data?.questions?.length"
    [disabled]="!selectedAnswer"
    [class.disabled]="!selectedAnswer"
    class="quiz-button"
    (click)="onGetNextQuestion()"
  >
    {{ currentQuestionIndex! + 1 < data?.questions?.length! ? 'Következő kérdés' : 'Tovább az eredményre' }}
  </button>
  <div *ngIf="rating && currentQuestionIndex === data?.questions?.length" class="quiz-result">
    <div class="quiz-result-share" (click)="onFBShareClick()">
      <kesma-icon name="facebook-white" [size]="28" />
      Facebook megosztás
    </div>
    @if (data?.quizCategoryPageUrl; as href) {
      <a class="quiz-result-link" [href]="href">További kvízek</a>
    } @else {
      <a class="quiz-result-link" [routerLink]="['/kvizek']">További kvízek</a>
    }
  </div>
</div>
