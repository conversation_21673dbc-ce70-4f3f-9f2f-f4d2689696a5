import { ChangeDetectionStrategy, Component, OnChanges } from '@angular/core';
import { Icon<PERSON><PERSON>ponent, QuizAnswer, QuizComponent as KesmaQuizComponent, QuizQuestionImage } from '@trendency/kesma-ui';
import { <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { DEFAULT_THUMBNAIL } from '../../constants';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-quiz',
  templateUrl: './quiz.component.html',
  styleUrls: ['./quiz.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, IconComponent, RouterLink],
})
export class QuizComponent extends KesmaQuizComponent implements OnChanges {
  currentQuestionIndex = 0;
  selectedAnswer?: QuizAnswer;

  override get placeholderImage(): string {
    return DEFAULT_THUMBNAIL;
  }

  getQuizImage(image: string | QuizQuestionImage): string {
    if (typeof image === 'object') {
      return (image as QuizQuestionImage)?.fullSizeUrl;
    }
    return image ?? '';
  }

  ngOnChanges(): void {
    this.currentQuestion = this.data?.questions?.[0];
  }

  get isNextButtonVisible(): boolean {
    return !!this.data?.questions?.length && this.data.questions.length > this.currentQuestionIndex;
  }

  onPickAnswer(answer: QuizAnswer): void {
    this.selectedAnswer = answer;
  }

  onGetNextQuestion(): void {
    if ((this.data?.questions?.length && this.data.questions.length <= this.currentQuestionIndex) || !this.selectedAnswer) return;

    this.selectedAnswer = undefined;
    this.currentQuestionIndex++;
    this.currentQuestion = this.data?.questions[this.currentQuestionIndex];
  }
}
