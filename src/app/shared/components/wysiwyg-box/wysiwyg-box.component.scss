@use 'shared' as *;

:host {
  color: var(--kui-green-800);
}

.block-content::ng-deep {
  a {
    color: var(--kui-blue-600);
    font-weight: 500;
    text-decoration: none;
  }

  figure.image {
    img {
      border-radius: 20px;
      border: 1px solid var(--kui-gray-500);
    }

    figcaption {
      font-size: 16px;
      line-height: 32px;
      max-width: 800px;
      margin-left: 30px;
      font-weight: 600;

      &:before {
        content: none;
      }
    }
  }

  .custom-text-style {
    &.underlined-text {
      text-decoration-color: var(--kui-black);
      text-decoration-thickness: 1px;
      display: inline-block;
      margin: 0;
      padding: 0;

      @include media-breakpoint-down(xs) {
        font-size: 14px;
        line-height: 28px;
      }
    }

    &.highlight {
      background: var(--kui-blue-200);
      color: var(--kui-green-800);
      padding: 40px;
      width: calc(100% + 80px);
      position: relative;
      left: -40px;
      border-radius: 15px;

      p {
        font-size: 16px;
        line-height: 32px;
        font-weight: 500;
      }
    }

    &.quote {
      position: relative;
      padding: 10px 30px;
      border: none;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 5px;
        border-radius: 2.5px;
        background: var(--kui-blue-600);
      }

      .quoteBlock-content {
        p {
          font-size: 20px;
          line-height: 32px;
          font-style: italic;
          color: var(--kui-green-800);

          @include media-breakpoint-down(xs) {
            font-size: 18px;
            line-height: 29px;
          }
        }
      }

      .quoteBlock-content p:before,
      .quoteBlock-content p:after {
        content: none;
      }
    }

    &.border-text {
      border: 1px solid var(--kui-blue-600);
      background: none;

      @include media-breakpoint-down(xs) {
        font-size: 14px;
        line-height: 28px;
      }

      .borderBlock-content::after {
        content: ' ';
        width: 100px;
        height: 1px;
        display: block;
        background: var(--kui-black);
        margin-top: 5px;
      }
    }
  }

  h2,
  h3,
  h4 {
    margin-bottom: 30px;
    font-weight: bold;
  }

  h2 {
    font-size: 30px;
    line-height: 29px;

    @include media-breakpoint-down(xs) {
      font-size: 26px;
      line-height: 24px;
    }
  }

  h3 {
    font-size: 24px;
    line-height: 23px;

    @include media-breakpoint-down(xs) {
      font-size: 20px;
      line-height: 19px;
    }
  }

  h4 {
    font-size: 18px;
    text-decoration: none;
    line-height: 23px;

    @include media-breakpoint-down(xs) {
      font-size: 16px;
      line-height: 19px;
    }
  }

  p {
    font-size: 16px;
    line-height: 32px;
    font-weight: 500;

    @include media-breakpoint-down(xs) {
      font-size: 14px;
      line-height: 28px;
    }
  }

  ol,
  ul {
    li {
      font-size: 16px;
      font-weight: 500;

      @include media-breakpoint-down(xs) {
        font-size: 14px;
      }
    }
  }

  & > ol {
    & > li {
      padding-left: 0 !important;

      &:before {
        display: inline-block !important;
        margin-right: 5px;
      }
    }
  }

  ol {
    counter-reset: index;
    padding-left: 0;
    margin: 0;

    li {
      margin: 15px 0;
      display: block;
      position: relative;
      padding-left: 20px;

      &:before {
        counter-increment: index;
        content: counters(index, '.') '.';
        display: block;
      }
    }
  }
}
