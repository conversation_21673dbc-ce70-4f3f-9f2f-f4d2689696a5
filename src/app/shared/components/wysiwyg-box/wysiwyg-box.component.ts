import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleFileLinkDirective, BypassPipe, ImageLightboxComponent, WysiwygBoxComponent as KesmaWysiwygBoxComponent } from '@trendency/kesma-ui';
import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { RunScriptsDirective } from '@trendency/kesma-core';

@Component({
  selector: 'mno-wysiwyg-box',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/wysiwyg-box/wysiwyg-box.component.html',
  styleUrls: ['../../../../../node_modules/@trendency/kesma-ui/src/lib/components/wysiwyg-box/wysiwyg-box.component.scss', './wysiwyg-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgI<PERSON>, NgFor, ImageLightboxComponent, BypassPipe, ArticleFileLinkDirective, RunScriptsDirective],
})
export class WysiwygBoxComponent extends KesmaWysiwygBoxComponent {}
