@use 'shared' as *;

:host {
  display: flex;
  gap: 16px;
  position: relative;
  @include media-breakpoint-down(sm) {
    display: inline-flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    max-width: fit-content;
  }

  &.white {
    .btn-share {
      &,
      &:active,
      &:visited {
        color: var(--kui-slate-50);
      }
    }

    .share-container {
      background: var(--kui-opinion-info-bg-color);
    }
  }
}

.btn-share {
  font-size: 12px;
  font-weight: 700;
  line-height: 16px; /* 133.333% */
  display: inline-flex;
  align-items: center;
  gap: 8px;
  border-radius: 24px;
  padding: 0 0 0 12px;
  border: 1px solid var(--kui-slate-100);

  &,
  &:active,
  &:visited {
    color: var(--kui-slate-950);
  }

  [mno-icon] {
    border-radius: 16px;
    border: 1px solid var(--kui-slate-100);
    padding: 4px;
    margin-right: -1px;
  }
}

.share {
  &-container {
    display: flex;
    gap: 16px;

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      position: absolute;
      right: 0;
      top: 0;
      background: #fff;
      border-radius: 24px;

      .share-link {
        @include transition;
        transition-property: opacity, top;
        top: 0;
        opacity: 0;
        width: 0;
        height: 0;
        overflow: hidden;
        position: absolute;
      }
    }

    &.is-open {
      display: flex;
      @include media-breakpoint-down(sm) {
        top: 40px;
      }

      .share-link {
        @include media-breakpoint-down(sm) {
          position: relative;
          @include transition;
          transition-property: opacity, top;
          top: unset;
          opacity: 1;
          width: 24px;
          height: 24px;
        }
      }
    }
  }

  &-copy-link {
    cursor: pointer;
  }

  &-link {
    width: 24px;
    height: 24px;
    border: 1px solid var(--kui-blue-50);
    background-color: transparent;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    @include transition;

    &:hover {
      background-color: var(--kui-blue-50);
      @include transition;

      [mno-icon='facebook'] {
        @include icon('icons/facebook-color.svg', true);
        width: 24px;
        height: 24px;
      }

      [mno-icon='link'] {
        @include icon('icons/link-dark.svg', true);
      }

      [mno-icon='mail'] {
        @include icon('icons/mail-dark.svg', true);
      }

      [mno-icon='twitter'] {
        @include icon('icons/twitter-dark.svg', true);
      }
    }

    &.no-border {
      border: 0 !important;
      background-color: transparent !important;
      padding: 0 !important;
    }
  }
}

:host {
  &.dark {
    .share {
      &-link {
        [mno-icon='facebook-plain'] {
          width: 24px;
          height: 24px;
        }

        &:hover {
          background-color: var(--kui-slate-950);
          border-color: var(--kui-slate-950);
          @include transition;

          [mno-icon='facebook-plain'] {
            @include icon('icons/facebook-color.svg', true);
            width: 24px;
            height: 24px;
          }

          [mno-icon='link'] {
            @include icon('icons/link-white.svg', true);
          }

          [mno-icon='mail'] {
            @include icon('icons/mail-white.svg', true);
          }

          [mno-icon='twitter'] {
            @include icon('icons/twitter-white.svg', true);
          }
        }
      }
    }
  }
}
