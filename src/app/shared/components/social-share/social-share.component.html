<a (click)="onMobileShareClick($event)" *ngIf="isMobile" [href]="shareUrl" [title]="title" class="btn-share">
  <PERSON><PERSON><PERSON>tom
  <img [color]="color" [size]="32" alt="Megosztás ikon" mno-icon="share-android" />
</a>

<div [class.is-open]="!isMobile" class="share-container">
  <div (click)="onUrlCopied()" [copyToClipboard]="shareUrl" [title]="linkCopyLabel" class="share-link share-copy-link">
    <i [color]="color" mno-icon="link"></i>
  </div>

  <a (click)="onEmailShareClick()" [href]="emailUrl" [title]="emailShareLabel" class="share-link">
    <i [color]="color" mno-icon="mail"></i>
  </a>
  <a (click)="onTwitterShareClick()" [href]="twitterUrl" [title]="twitterShareLabel" class="share-link" target="_blank">
    <i [color]="color" mno-icon="twitter"></i>
  </a>
  <a
    (click)="onFacebookShareClick()"
    [class.no-border]="color === 'white'"
    [href]="facebookUrl"
    [title]="facebookShareLabel"
    class="share-link share-facebook"
    target="_blank"
  >
    <i *ngIf="color === 'white'" [color]="color" [size]="28" mno-icon="facebook"></i>
    <i *ngIf="color === 'dark'" [color]="color" mno-icon="facebook-plain"></i>
  </a>
</div>
