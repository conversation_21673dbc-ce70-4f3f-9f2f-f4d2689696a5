import { ChangeDetectionStrategy, Component, EventEmitter, HostBinding, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { CopyToClipboardDirective, getEmailShareUrl, getFacebookShareUrl, getTwitterShareUrl, RoutingHelperService } from '@trendency/kesma-ui';
import { SocialInteractionEvent, SocialInteractionEventType } from '../../definitions';
import { IconComponent } from '../icon/icon.component';
import { NgIf } from '@angular/common';

@Component({
  selector: 'mno-social-share',
  templateUrl: './social-share.component.html',
  styleUrls: ['./social-share.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, IconComponent, CopyToClipboardDirective],
})
export class SocialShareComponent implements OnChanges {
  @Input()
  @HostBinding('class')
  color: 'white' | 'dark' = 'white';

  @Input() set link(link: string[]) {
    if (link) {
      this._link = link;
      this.setArticleLink();
    }
  }

  get link(): string[] {
    return this._link;
  }

  @Input() set hasExternalUrl(hasExternalUrl: boolean) {
    if (hasExternalUrl) {
      this._hasExternalUrl = hasExternalUrl;
      this.setArticleLink();
    }
  }

  get hasExternalUrl(): boolean {
    return this._hasExternalUrl;
  }

  @Input() title?: string;
  @Input() isMobile: boolean | null = false;

  @Output() urlCopied = new EventEmitter<string>();
  @Output() socialInteraction = new EventEmitter<SocialInteractionEvent>();

  _link: string[] = [];
  _hasExternalUrl: boolean = false;
  shareUrl: string = '';
  facebookUrl: string = '';
  twitterUrl: string = '';
  emailUrl: string = '';
  isOpen = false;

  readonly linkCopyLabel = 'Link másolása';
  readonly facebookShareLabel = 'Facebook-megosztás';
  readonly twitterShareLabel = 'X-megosztás';
  readonly emailShareLabel = 'E-mailben küldés';

  constructor(private readonly routingHelperService: RoutingHelperService) {}

  ngOnChanges(changes: SimpleChanges): void {
    const link = changes['link']?.currentValue;
    const title = changes['title']?.currentValue;

    if (link?.length && title) {
      this.getSocialShareUrls(this.shareUrl, title);
    }
  }

  public onUrlCopied(): void {
    this.urlCopied.emit(this.shareUrl);
    this.socialInteraction.emit(new SocialInteractionEvent(SocialInteractionEventType.LinkCopy, this.shareUrl, this.title, this.linkCopyLabel));
  }

  private getSocialShareUrls(articleLink: string, articleTitle: string): void {
    this.facebookUrl = getFacebookShareUrl(articleLink);
    this.twitterUrl = getTwitterShareUrl(articleLink);
    this.emailUrl = getEmailShareUrl(articleLink, articleTitle);
  }

  private setArticleLink(): void {
    this.shareUrl = this.routingHelperService.resolveLink(this.link, this.hasExternalUrl);
  }

  onFacebookShareClick(): void {
    this.socialInteraction.emit(new SocialInteractionEvent(SocialInteractionEventType.FacebookShare, this.shareUrl, this.title, this.facebookShareLabel));
  }

  onTwitterShareClick(): void {
    this.socialInteraction.emit(new SocialInteractionEvent(SocialInteractionEventType.TwitterShare, this.shareUrl, this.title, this.twitterShareLabel));
  }

  onEmailShareClick(): void {
    this.socialInteraction.emit(new SocialInteractionEvent(SocialInteractionEventType.EmailShare, this.shareUrl, this.title, this.emailShareLabel));
  }

  onMobileShareClick(event: MouseEvent): void {
    event.stopPropagation();
    const isAndroid = /android/i.test(navigator.userAgent);
    if (isAndroid && window.AndroidShareHandler?.share) {
      window.AndroidShareHandler.share(`${window.location.href}`);
      event.preventDefault();
      return;
    }
    if (!navigator.share) {
      console.warn('! Mobile sharing is not supported');
      alert('A mobil megosztás ezen az eszközön nem érhető el! Kérjük használja a "hivatkozás másolása" funkciót a megosztás gombon!');
      return;
    }

    navigator
      .share({
        title: this.title,
        text: this.title,
        url: this.shareUrl,
      })
      .then(() =>
        this.socialInteraction.emit(new SocialInteractionEvent(SocialInteractionEventType.MobileShare, this.shareUrl, this.title, this.emailShareLabel))
      )
      .catch((error) => console.log('Error sharing:', error));

    event.preventDefault();
  }
}
