@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  justify-content: stretch;
  align-items: stretch;
  width: 300px;
  height: 358px;
  max-width: 100%;
  margin-bottom: 24px;
  background-repeat: no-repeat;
  background-image:
    linear-gradient(0deg, #0e2a43cc 0%, #0e2a43cc 100%),
    url('https://cdn.magyarnemzet.hu/2023/12/I_Pbb-N33CBEKjjYFTUD6v8oaIGwcreKiRkvuT54r_4/fill/700/394/no/1/aHR0cHM6Ly9jbXNjZG4uYXBwLmNvbnRlbnQucHJpdmF0ZS9jb250ZW50LzlhOTg1YTJkZGU2YzRjZTA5ODVlYTQ1NGE4Y2UwZTQ4.jpg');
  background-color: var(--kui-gray-310);
  background-size: auto 100%;
  background-position: center center;
  @include transition;
  @include media-breakpoint-down(sm) {
    width: 100%;
    height: auto;
  }

  &:hover {
    @include transition;
    background-size: auto 110%;
  }

  &.style-WIDE {
    @include media-breakpoint-up(md) {
      width: 100%;
      height: 84px;

      .card {
        &-lead {
          display: none;
        }

        &-top {
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap;
          align-items: center;
        }

        &-bottom {
          flex-wrap: nowrap;
          align-items: center;
          flex-shrink: 0;
          gap: 24px;

          mno-opinion-author {
            margin-bottom: 0;
          }
        }

        &-background-overlay {
          flex-direction: row;
          padding: 16px;
          gap: 16px;
        }

        &-label {
          border-bottom: 0;
          border-right: 1px solid var(--kui-white);
          flex-shrink: 1;
          flex-grow: 0;
          width: auto;
          padding: 0 16px 0 0;
          margin: 0 16px 0 0;
        }

        &-title {
          display: block;
          max-width: 400px;
          min-width: 400px;
          flex-shrink: 1;
          margin-bottom: 0;
          max-height: 100%;

          font-size: 20px;
          font-weight: 400;
          line-height: 26px;
          letter-spacing: 0.01em;
        }
      }
    }
  }
}

.card {
  &-background-overlay {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: stretch;
    align-items: stretch;
    padding: 24px 16px;
    gap: 24px;

    &:hover {
      .card {
        &-title,
        &-lead {
          color: var(--kui-blue-500);
          @include transition;
        }
      }

      [mno-icon] {
        @include transition;
        background-color: var(--kui-blue-950);
      }

      mno-opinion-author::ng-deep {
        .author-avatar {
          @include transition;
          padding: 0;
        }
      }
    }
  }

  &-label {
    color: var(--kui-blue-50);
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 21px;
    width: 100%;
    border-bottom: 1px solid var(--kui-white);
    padding-bottom: 16px;
    margin-bottom: 16px;
    display: flex;
  }

  &-title {
    font-family: var(--kui-font-secondary);
    color: var(--kui-white);
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 30px;
    margin-bottom: 10px;
    // max-height: calc(50% - 16px);
    @include transition;
  }

  &-lead {
    color: var(--kui-white);
    line-height: 24px;
    // max-height: calc(50% - 40px);
    @include transition;
  }

  &-lead,
  &-title {
    display: -webkit-box;
    line-clamp: 3;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow-y: hidden;
    vertical-align: text-bottom;
  }

  &-top,
  &-bottom {
    flex: 1;
  }

  &-top {
    max-height: 236px;
  }

  &-bottom {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    justify-content: space-between;

    [mno-icon] {
      @include transition;
      border: 1px solid var(--kui-white);
      border-radius: 50%;
      padding: 2px;
      width: 40px;
      height: 40px;
    }

    mno-opinion-author::ng-deep {
      .author-name {
        color: var(--kui-white);
        flex-wrap: nowrap;
        white-space: nowrap;
        overflow-x: hidden;
        text-overflow: ellipsis;
      }

      .author-avatar {
        flex-shrink: 0;
      }

      margin-bottom: 4px;
    }
  }
}
