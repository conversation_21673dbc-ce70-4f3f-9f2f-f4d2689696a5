import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ArticleCard, Author, BaseComponent, buildArticleUrl } from '@trendency/kesma-ui';
import { BlogRecommentationType } from '../../definitions';
import { IconComponent } from '../icon/icon.component';
import { OpinionAuthorComponent } from '../opinion-author/opinion-author.component';
import { NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'mno-blog-recommendation',
  templateUrl: './blog-recommendation.component.html',
  styleUrls: ['./blog-recommendation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgIf, OpinionAuthorComponent, IconComponent],
})
export class BlogRecommendationComponent<C = ArticleCard> extends BaseComponent<C> {
  @HostBinding('style.background-image') bg?: string = '';
  @HostBinding('class') hostClass = '';

  @Input() set styleID(value: BlogRecommentationType) {
    this.#styleID = value;
    this.hostClass = `style-${BlogRecommentationType[this.#styleID]}`;
  }

  get styleID(): BlogRecommentationType {
    return this.#styleID;
  }

  author: Author = {} as Author;
  articleLink: string | string[] = [];
  #styleID = BlogRecommentationType.DEFAULT;

  get articleCard(): ArticleCard {
    return this.data as unknown as ArticleCard;
  }

  protected override setProperties(): void {
    super.setProperties();
    this.bg = `
      linear-gradient(0deg, rgba(14, 42, 67, 0.80) 0%, rgba(14, 42, 67, 0.80) 100%),
      url(${(this.data as unknown as ArticleCard)?.thumbnail?.url})
    `;
    this.author = (this.data as unknown as ArticleCard)?.author as Author;
    this.articleLink = buildArticleUrl(this.data as unknown as ArticleCard);
  }
}
