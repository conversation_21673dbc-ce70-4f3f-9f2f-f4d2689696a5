import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ArticleCard } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { <PERSON><PERSON><PERSON>, NgIf, SlicePipe } from '@angular/common';
import { IconComponent } from '../icon/icon.component';
import { ArticleCardType } from '../../definitions';

@Component({
  selector: 'app-module-heading',
  templateUrl: './module-heading.component.html',
  styleUrls: ['./module-heading.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleCardComponent, SlicePipe, NgIf, NgFor, IconComponent],
})
export class ModuleHeadingComponent {
  @Input() showTitle = true;
  @Input() title = '';
  @Input() articles: ArticleCard[] = [];
  @Input() articleCount = 3;
  @Input() styleID = ArticleCardType.Img16TopTagsTitleLeadBadge;
  @Input() mobileStyleID = ArticleCardType.ImgRightTagsTitleLeadWide;
  @Input() forceDesktopCardStyle = false;
  @Input()
  @HostBinding('class')
  color: 'black' | 'blue' | 'white' | 'gray' = 'white';
  @Input() @HostBinding('class.is-opinion') isOpinion = false;
  @Input() @HostBinding('class.is-video') isVideo = false;
  @Input() @HostBinding('class.is-podcast') isPodcast = false;
  @Input() @HostBinding('class.is-gallery') isGallery = false;

  readonly ArticleCardType = ArticleCardType;

  @Input() isMobile = false;
}
