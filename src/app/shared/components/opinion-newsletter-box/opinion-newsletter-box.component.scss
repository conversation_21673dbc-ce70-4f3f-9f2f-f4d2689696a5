@use 'shared' as *;

:host {
  display: block;

  .opinion-newsletter {
    background-color: var(--kui-blue-800);
    display: flex;
    flex-wrap: nowrap;
    width: 100%;

    &.Column {
      flex-direction: column;
    }

    &.Inline {
      flex-direction: row;
      justify-content: space-between;

      @include media-breakpoint-down(sm) {
        flex-direction: column;
      }
    }

    &-text {
      img {
        width: 53px;
        height: 57px;
      }

      display: flex;
      padding: 16px;
      align-items: center;
      gap: 24px;
      color: var(--kui-white);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
      border-radius: 2px;
      border: 1px solid var(--kui-blue-500);
      box-sizing: border-box;

      strong {
        font-weight: 700;
      }

      a {
        color: var(--kui-blue-400);
      }

      &.Column {
        flex-direction: row-reverse;
      }

      &.Inline {
        border-right: none;

        img {
          @include media-breakpoint-up(lg) {
            width: 33px;
            height: 30px;
          }
        }
      }
    }

    &-button {
      width: 100%;
      display: flex;
      padding: 6px 12px;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: 18px;
      border-radius: 2px;
      border: 1px solid var(--kui-blue-500);
      color: var(--kui-white);

      &.Inline {
        border-top: none;

        @include media-breakpoint-up(md) {
          width: 40%;
          max-width: 300px;
          border-top: 1px solid var(--kui-blue-500);
        }
      }

      &.Column {
        border-top: none;
        height: 36px;
      }
    }
  }
}
