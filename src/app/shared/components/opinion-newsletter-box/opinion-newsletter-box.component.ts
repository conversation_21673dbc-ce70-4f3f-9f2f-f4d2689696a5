import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { OpinionNewsletterBoxType } from '../../definitions';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'mno-opinion-newsletter-box',
  templateUrl: './opinion-newsletter-box.component.html',
  styleUrls: ['./opinion-newsletter-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink],
})
export class OpinionNewsletterBoxComponent {
  @Output() subscribeClicked = new EventEmitter<MouseEvent>();
  @Input() infoLink: string = '';

  @Input()
  set styleId(styleId: OpinionNewsletterBoxType) {
    this.boxStyle = `${OpinionNewsletterBoxType[styleId]}`;
  }

  boxStyle = OpinionNewsletterBoxType.Inline.toString();
}
