@use 'shared' as *;

:host {
  display: block;

  &.has-margin {
    margin-top: 28px;

    @include media-breakpoint-down(sm) {
      margin-top: 12.5px;
    }
  }
}

.cpac {
  background-image: url('/assets/images/cpac-background.svg');
  background-repeat: no-repeat;
  background-size: cover;
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25px 50px;
  gap: 20px;

  @include media-breakpoint-down(md) {
    height: 180px;
    width: calc(100% + 168px);
    margin-left: -84px;

    &-logo {
      width: 186.54px;
    }

    &-wokebusters {
      width: 257.53px;
    }
  }

  @include media-breakpoint-down(sm) {
    flex-direction: column;
    height: 200px;
    padding: 12.5px 50px;

    &-wokebusters {
      width: 180.27px;
    }
  }
}
