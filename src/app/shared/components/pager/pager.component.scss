@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 20px;

  .pager {
    position: relative;

    .button-arrow {
      border-radius: 2px;

      .icon-prev {
        background-image: url('/assets/images/icons/chevron-left-dark.svg');
      }

      .icon-next {
        background-image: url('/assets/images/icons/chevron-right-dark.svg');
      }

      &.last,
      &.first {
        display: none;
      }

      &.next,
      &.prev {
        background-color: var(--kui-gray-100);
      }

      &.next {
        margin-left: 6px;
      }

      &.prev {
        margin-right: 6px;
      }

      .icon {
        height: 24px;
        width: 24px;
      }
    }

    .button-arrow,
    .button-num,
    .count-pager {
      border: 1px solid var(--kui-slate-200);
      border-radius: 2px;
      min-width: 32px;
      height: 32px;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 700;
    }

    .list-pager {
      gap: 6px;

      .button-num {
        padding: 6px 7px;

        &.active {
          color: var(--kui-slate-50);
          background-color: var(--kui-slate-950);
          font-weight: 700;
          border: 1px solid var(--kui-slate-950);
        }
      }
    }

    .buttons + .button-num.more {
      margin-right: 6px;
    }

    .list-pager + .button-num.more {
      margin-left: 6px;
    }

    .separator {
      margin: 0 5px;
    }
  }

  .disabled {
    background-color: var(--kui-gray-50);
  }
}
