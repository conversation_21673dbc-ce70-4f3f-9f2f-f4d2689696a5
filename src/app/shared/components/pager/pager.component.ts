import { ChangeDetectionStrategy, Component } from '@angular/core';
import { PagerComponent as KesmaPagerComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { Ng<PERSON>lass, NgIf } from '@angular/common';

@Component({
  selector: 'mno-pager',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/pager/pager.component.html',
  styleUrls: ['../../../../../node_modules/@trendency/kesma-ui/src/lib/components/pager/pager.component.scss', './pager.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgClass, RouterLink],
})
export class PagerComponent extends KesmaPagerComponent {}
