import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ArticleCard, BaseComponent, buildColumnUrl, buildTagUrl, Tag } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { <PERSON><PERSON><PERSON>, NgIf, NgTemplateOutlet, SlicePipe } from '@angular/common';
import { ColumnLabel, LabelType, TagListLabel } from '../../definitions';

@Component({
  selector: 'mno-tag-list',
  templateUrl: './tag-list.component.html',
  styleUrls: ['./tag-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgI<PERSON>, NgFor, NgTemplateOutlet, RouterLink, SlicePipe],
})
export class TagListComponent extends BaseComponent<(Tag | string | ColumnLabel)[]> {
  @Input() max?: number;

  labels: TagListLabel[] = [];
  readonly LabelType = LabelType;

  protected override setProperties(): void {
    super.setProperties();
    this.labels = (this.data ?? []).map((elem) => {
      let title: string | undefined = undefined;
      let link: string[] | string | undefined = undefined;
      const id: string | undefined = undefined;
      let type = LabelType.LABEL;
      if (typeof elem === 'string') {
        title = elem;
      } else if ((elem as ColumnLabel).columnSlug) {
        title = (elem as ColumnLabel).columnTitle;
        link = buildColumnUrl(elem as ArticleCard);
        type = LabelType.COLUMN;
      } else if ((elem as Tag).slug) {
        ({ title } = elem as Tag);
        link = buildTagUrl({ tags: [elem as Tag] } as ArticleCard);
        type = LabelType.TAG;
      }
      return {
        id,
        title,
        link,
        type,
      };
    }) as TagListLabel[];
  }
}
