<ng-container *ngIf="max !== undefined; else all">
  <ng-container *ngFor="let label of labels | slice: 0 : max; let i = index; let first = first">
    <ng-container [ngTemplateOutletContext]="{ $implicit: label, i: i, first: first }" [ngTemplateOutlet]="label.link ? linkLabel : simple"></ng-container>
  </ng-container>
</ng-container>

<ng-template #all>
  <ng-container *ngFor="let label of labels; let i = index; let first = first">
    <ng-container [ngTemplateOutletContext]="{ $implicit: label, i: i, first: first }" [ngTemplateOutlet]="label.link ? linkLabel : simple"></ng-container>
  </ng-container>
</ng-template>

<ng-template #simple let-first let-label>
  <span [class.first]="first" class="label">{{ label.title }}</span>
</ng-template>
<ng-template #linkLabel let-first let-label>
  <a [class.first]="first" [routerLink]="label.link" class="label">{{ label.title }}</a>
</ng-template>
