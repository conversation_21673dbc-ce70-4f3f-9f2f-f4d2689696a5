@use 'shared' as *;

:host {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  flex-wrap: wrap;
  gap: 8px;
}

.label {
  @include transition;
  color: var(--kui-blue-800);
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  white-space: nowrap;
  border-right: 1px solid var(--kui-slate-300);
  padding-right: 8px;

  &:last-child {
    border-right: 0;
  }

  &:first-child {
    font-weight: 500;
    color: 1px solid var(--kui-blue-900);
  }
}

a.label {
  &:hover {
    @include transition;
    color: var(--kui-blue-500);
  }
}
