import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Meta, ProgramQueryParams, ProgramQueryParamsFromUrl, SluggableItem } from '@trendency/kesma-ui';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { KulturnemzetService } from '../../../../feature/kulturnemzet/kulturnemzet.service';
import { NgFor, NgIf } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-kulturnemzet-searchbar',
  templateUrl: './kulturnemzet-searchbar.component.html',
  styleUrls: ['./kulturnemzet-searchbar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, FormsModule],
})
export class KulturnemzetSearchbarComponent implements OnInit {
  @Input() isSearchPage = true;
  @Input() selects: {
    selectTypes: SluggableItem[];
    selectLocations: SluggableItem[];
    selectDates: SluggableItem[];
  };
  @Input() resultMeta: Meta = {
    dataCount: 0,
  } as Meta;
  searchSelectedType?: SluggableItem;
  searchSelectedLocation?: SluggableItem;
  searchSelectedDate?: SluggableItem;
  from = 0;
  to = 0;
  programCount = 0;
  searchParams: string;
  private queryParams: ProgramQueryParams;
  private readonly destroy$ = new Subject<boolean>();

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly cd: ChangeDetectorRef,
    private readonly kulturnemzetService: KulturnemzetService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe((data: ProgramQueryParamsFromUrl) => {
      if (Object.keys(data).length !== 0) {
        this.searchSelectedDate = data?.dateName;
        this.searchSelectedLocation = data?.['program_location[]'];
        this.searchSelectedType = data?.['program_type[]'];
        this.setQueryParams();
        this.cd.markForCheck();
      }
    });
    this.kulturnemzetService.lastSearchMeta$.pipe(takeUntil(this.destroy$)).subscribe((meta) => {
      const { limitable: l, dataCount } = meta;
      this.from = dataCount > 0 ? (l?.rowFrom ?? 0) + 1 : 0;
      this.to = this.from + dataCount - 1;
      this.programCount = l?.rowAllCount ?? 0;
    });
  }

  public onSearch(hasQueryParams?: boolean): void {
    this.router.navigate(['/', 'kulturnemzet'], {
      queryParams: !hasQueryParams ? this.setQueryParams() : this.queryParams,
    });
  }

  setdateName(dateName: string): SluggableItem | undefined {
    return dateName ? this.selects.selectDates.find((e) => e?.slug === dateName) : undefined;
  }

  private setQueryParams(): ProgramQueryParams {
    this.queryParams = {
      rowCount_limit: 12,
      page_limit: 0,
    };
    const params: (SluggableItem | undefined)[] = [] as SluggableItem[];
    if (this.searchSelectedType) {
      this.queryParams['program_type[]'] = [this.searchSelectedType];
    }

    if (this.searchSelectedDate) {
      this.queryParams.dateName = this.searchSelectedDate;
    }

    if (this.searchSelectedLocation) {
      this.queryParams['program_location[]'] = [this.searchSelectedLocation];
    }

    params.push(this.selects.selectTypes.find(({ slug }) => slug === (this.queryParams['program_type[]']?.[0] as string[] | undefined)?.[0] || ''));
    params.push(this.selects.selectDates.find(({ slug }) => slug === (this.queryParams['dateName'] as unknown as string)));
    params.push(this.selects.selectLocations.find(({ slug }) => slug === (this.queryParams['program_location[]']?.[0] as string[] | undefined)?.[0]));

    this.searchParams = params
      .map((s) => s?.title)
      .filter((s) => !!s)
      .join(', ');
    return this.queryParams;
  }
}
