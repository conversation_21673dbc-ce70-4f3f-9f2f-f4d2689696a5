@use 'shared' as *;
@use 'microsites/micro-kulturnemzet' as *;

:host {
}

.kulturnemzet-searchbar {
  position: relative;
  padding-bottom: 24px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  @include media-breakpoint-down(sm) {
    flex-direction: column;
  }

  .top {
    font-size: 16px;
    font-weight: 700;
    text-align: left;
    flex-grow: 0;
    margin-right: 46px;

    @include media-breakpoint-down(sm) {
      width: 100%;
    }
  }

  .form {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 24px;
    flex-grow: 1;
    @include media-breakpoint-down(sm) {
      grid-template-columns: 1fr;
    }

    .input-box {
      height: 40px;
      position: relative;
      width: 100%;

      &:after {
        width: 20px;
        height: 20px;
        display: block;
        position: absolute;
        right: 10px;
        top: calc(50% - 12px);
        @include icon('icons/chevron-down-dark.svg');
      }

      select {
        display: flex;
        align-items: center;
        height: 40px;
        width: 100%;
        padding: 10px 16px;

        border-radius: 2px;
        border: 1px solid var(--kui-slate-950);
        color: var(--kui-slate-950);
        background-color: transparent;
        text-align: center;

        font-size: 16px;
        font-weight: 500;
        line-height: 22px; /* 137.5% */

        -webkit-appearance: none;
        appearance: none;

        &::-ms-expand {
          display: none;
        }
      }
    }

    .search-button {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
