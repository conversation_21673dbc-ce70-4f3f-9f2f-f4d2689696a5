<div class="kulturnemzet-searchbar">
  <h1 class="top">Programkereső</h1>
  <form class="form" (ngSubmit)="onSearch()">
    <div class="input-box">
      <select [(ngModel)]="searchSelectedType" [attr.data-placeholder]="'Programtípus'" name="programType">
        <option [value]="null" selected disabled hidden>Programtípus</option>
        <ng-container *ngFor="let item of selects.selectTypes">
          <option [value]="item?.slug">{{ item.title }}</option>
        </ng-container>
      </select>
    </div>

    <div class="input-box">
      <select [(ngModel)]="searchSelectedDate" [attr.data-placeholder]="'Programtípus'" name="programDate">
        <option [value]="null" selected disabled hidden>Időpont</option>
        <ng-container *ngFor="let item of selects.selectDates">
          <option [value]="item?.slug">{{ item.title }}</option>
        </ng-container>
      </select>
    </div>

    <div class="input-box">
      <select [(ngModel)]="searchSelectedLocation" [attr.data-placeholder]="'Programtípus'" name="programLocation">
        <option [value]="null" selected disabled hidden>Helyszín</option>
        <ng-container *ngFor="let item of selects.selectLocations">
          <option [value]="item?.slug">{{ item.title }}</option>
        </ng-container>
      </select>
    </div>

    <button class="btn btn-ghost btn-ghost-dark-transparent search-button" type="submit">Keresés</button>
  </form>
</div>
<div class="result-count" *ngIf="isSearchPage">
  <ng-container *ngIf="programCount > 0; else notFound">
    {{ from }} - {{ to }} program megjelenítése a {{ programCount }}-ból:
    {{ searchParams }}
  </ng-container>
  <ng-template #notFound> {{ searchParams }}: a kiválasztott paraméterre jelenleg nincs találat. </ng-template>
</div>
