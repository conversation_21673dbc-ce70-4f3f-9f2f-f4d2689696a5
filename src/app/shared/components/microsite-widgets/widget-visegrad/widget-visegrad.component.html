<div class="micro-visegrad">
  <section class="micro-widget">
    <div class="wrapper">
      <app-whead-visegrad></app-whead-visegrad>
      <div class="article-list">
        <div class="col-article col-12 col-lg-6">
          <div class="visegrad-article-card visegrad-article-big-card" *ngIf="mainArticle">
            <div class="image" *ngIf="mainArticle?.img" trImageLazyLoad [title]="mainArticle?.title" [style.background-image]="'url(' + mainArticle?.img + ')'">
              <div class="title-container">
                <!-- <div class="flags">
                    <ng-container *ngFor="let flagLink of  mainArticle?.lang">
                        <a href="{{ flagLink.link }}" target="_blank" class="flag {{ flagLink.tag }}"
                            [ngStyle]="{'background-image':'url(/assets/images/flags/'+ flagLink.tag + '.png)'}"></a>
                    </ng-container>
                </div> -->
                <a
                  class="title"
                  [routerLink]="[
                    '/',
                    mainArticle?.columnSlug,
                    mainArticle?.publishDate ?? '' | formatDate: 'year',
                    mainArticle?.publishDate ?? '' | formatDate: 'month',
                    mainArticle?.slug,
                  ]"
                  >{{ mainArticle?.title }}</a
                >
              </div>
            </div>
            <p class="desc" *ngIf="mainArticle?.desc">{{ mainArticle?.desc }}</p>
          </div>
        </div>

        <div class="col-article col-12 col-md-6 col-lg-3">
          <ng-container *ngFor="let cardData of secondRowArticles">
            <div class="visegrad-article-card">
              <div
                class="image"
                *ngIf="cardData?.img"
                trImageLazyLoad
                [title]="mainArticle?.title"
                [style.background-image]="'url(' + cardData?.img + ')'"
              ></div>
              <!-- <div class="flags">
                  <ng-container *ngFor="let flagLink of cardData?.lang">
                      <a href="{{ flagLink.link }}" target="_blank" class="flag {{ flagLink.tag }}"
                          [ngStyle]="{'background-image':'url(/assets/images/flags/'+ flagLink.tag + '.png)'}"></a>
                  </ng-container>
              </div> -->
              <a
                class="title"
                [routerLink]="[
                  '/',
                  cardData?.columnSlug,
                  cardData?.publishDate ?? '' | formatDate: 'year',
                  cardData?.publishDate ?? '' | formatDate: 'month',
                  cardData?.slug,
                ]"
                >{{ cardData?.title }}</a
              >
              <div class="desc" *ngIf="cardData?.desc">{{ cardData?.desc }}</div>
            </div>
          </ng-container>
        </div>

        <div class="col-article col-12 col-md-6 col-lg-3">
          <ng-container *ngFor="let cardData of thirdRowArticles">
            <div class="visegrad-article-card">
              <div
                class="image"
                *ngIf="cardData?.img"
                trImageLazyLoad
                [title]="mainArticle?.title"
                [style.background-image]="'url(' + cardData?.img + ')'"
              ></div>
              <!-- <div class="flags">
                  <ng-container *ngFor="let flagLink of cardData?.lang">
                      <a href="{{ flagLink.link }}" target="_blank" class="flag {{ flagLink.tag }}"
                          [ngStyle]="{'background-image':'url(/assets/images/flags/'+ flagLink.tag + '.png)'}"></a>
                  </ng-container>
              </div> -->
              <a
                class="title"
                [routerLink]="[
                  '/',
                  cardData?.columnSlug,
                  cardData?.publishDate ?? '' | formatDate: 'year',
                  cardData?.publishDate ?? '' | formatDate: 'month',
                  cardData?.slug,
                ]"
              >
                {{ cardData?.title }}
              </a>
              <div class="desc" *ngIf="cardData?.desc">{{ cardData?.desc }}</div>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </section>
</div>
