import { Component, Input, OnInit } from '@angular/core';
import { RouterLink } from '@angular/router';
import { FormatDatePipe, ImageLazyLoadDirective } from '@trendency/kesma-core';
import { VisegradPostArticle } from '@trendency/kesma-ui';
import { WheadVisegradComponent } from '../whead-visegrad/whead-visegrad.component';
import { NgForOf, NgIf } from '@angular/common';

//app-whead-visegrad
@Component({
  selector: 'app-widget-visegrad',
  templateUrl: './widget-visegrad.component.html',
  styleUrls: ['./widget-visegrad.component.scss'],
  imports: [RouterLink, FormatDatePipe, WheadVisegradComponent, NgIf, ImageLazyLoadDirective, NgForOf],
})
export class WidgetVisegradComponent implements OnInit {
  @Input() widgetData: VisegradPostArticle[];

  mainArticle?: VisegradPostArticle;
  secondRowArticles: VisegradPostArticle[];
  thirdRowArticles: VisegradPostArticle[];

  ngOnInit(): void {
    if (this.widgetData && this.widgetData.length > 0) {
      this.mainArticle = this.widgetData[0];
      this.secondRowArticles = this.widgetData.slice(1, 4);
      this.thirdRowArticles = this.widgetData.slice(4);
    } else {
      this.mainArticle = undefined;
      this.secondRowArticles = [];
      this.thirdRowArticles = [];
    }
  }
}
