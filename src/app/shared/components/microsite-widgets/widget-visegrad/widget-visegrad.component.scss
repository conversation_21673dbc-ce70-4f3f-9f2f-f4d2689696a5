@use 'shared' as *;
@use 'microsites/micro-visegrad' as *;

.micro-visegrad {
  margin: 30px 0;
  @include media-breakpoint-down(xs) {
    margin-left: -15px;
    margin-right: -15px;
    width: calc(100% + 30px);
  }

  .wrapper {
    background-color: $micro-visegrad-grey;
    border-top: 1px solid $micro-visegrad-blur-2;
    @include media-breakpoint-down(xs) {
      border-top: 2px solid $micro-visegrad-blur-2;
    }
  }

  .article-list {
    display: flex;
    flex-wrap: wrap;
    padding: 20px 15px;
    @include media-breakpoint-down(sm) {
      padding: 20px 5px;
    }

    .col-article {
      width: 50%;
      flex-wrap: wrap;
      display: flex;
      align-items: flex-start;
      flex-direction: column;

      .visegrad-article-card {
        position: relative;
        margin-bottom: 50px;
        width: 100%;

        &:before {
          content: ' ';
          display: block;
          width: 100%;
          height: 2px;
          background-color: $micro-visegrad-grey-2;
          position: absolute;
          bottom: -22px;
          right: 0;
          z-index: 2;
        }

        &:after {
          content: ' ';
          display: block;
          width: 60px;
          height: 2px;
          background-color: $micro-visegrad-orange;
          position: absolute;
          bottom: -22px;
          left: 0;
          z-index: 3;
          @include media-breakpoint-down(sm) {
            width: 28%;
          }
        }

        &:after,
        &:before {
          @include media-breakpoint-down(sm) {
            height: 4px;
          }
        }

        &:last-child {
          @include media-breakpoint-up(md) {
            margin-bottom: 0;
            &:after,
            &:before {
              display: none;
            }
          }
        }

        .desktop-only {
          @include media-breakpoint-down(sm) {
            display: none !important;
          }
        }

        .mobile-only {
          @include media-breakpoint-up(md) {
            display: none !important;
          }
        }

        .image {
          width: 100%;
          margin-bottom: 10px;
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          @include imgRatio(289%, 191%);
          @include media-breakpoint-down(sm) {
            @include imgRatio(335%, 173%);
          }
        }

        .flags {
          display: flex;
          margin-bottom: 2px;

          .flag {
            background-color: $micro-visegrad-grey;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            display: block;
            width: 21px;
            height: 14px;
            margin-right: 7px;
            @include media-breakpoint-down(sm) {
              width: 40px;
              height: 25px;
              margin-top: 10px;
              margin-right: 10px;
            }
          }
        }

        .title {
          color: $micro-visegrad-grey-3;
          font-size: 20px;
          font-weight: 400;
          line-height: 30px;
          margin-bottom: 10px;
          display: block;
          @include media-breakpoint-down(sm) {
            margin-bottom: 10px;
            margin-top: 10px;
            font-size: 24px;
            line-height: 28px;
          }
        }

        .desc {
          color: $micro-visegrad-grey-1;
          font-size: 16px;
          font-weight: 300;
          line-height: 24px;
        }

        &.visegrad-article-big-card {
          width: 100%;
          position: relative;
          @include media-breakpoint-down(sm) {
            margin-bottom: 50px;
          }

          .image {
            width: 100%;
            @include imgRatio(607%, 425%);
            position: relative;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;

            .title-container {
              padding: 15px 25px;
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;

              .flags {
                margin-left: -10px;
                margin-bottom: 7px;
              }

              .title {
                font-size: 32px;
                font-weight: 400;
                font-style: normal;
                letter-spacing: normal;
                line-height: 40px;
                color: var(--kui-white);
                display: inline;
                box-shadow:
                  10px 0 0 $micro-visegrad-orange,
                  -1px 0 0 $micro-visegrad-orange,
                  -10px 0 0 $micro-visegrad-orange,
                  1px 0 0 $micro-visegrad-orange;
                padding: 0;
                background-color: $micro-visegrad-orange;
                -webkit-box-decoration-break: clone;
                -o-box-decoration-break: clone;
                box-decoration-break: clone;
                position: relative;
                z-index: 2;
                @include media-breakpoint-down(sm) {
                  font-size: 24px;
                  line-height: 30px;
                }
              }
            }
          }

          @include media-breakpoint-up(md) {
            .desc {
              color: $micro-visegrad-grey-1;
              font-size: 19px;
              font-weight: 300;
              font-style: normal;
              letter-spacing: normal;
              line-height: 26.67px;
              padding-top: 10px;
            }
          }
        }
      }
    }
  }
}
