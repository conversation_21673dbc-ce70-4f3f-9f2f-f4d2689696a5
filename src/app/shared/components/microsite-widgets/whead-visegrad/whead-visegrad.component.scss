@use 'shared' as *;
@use 'microsites/micro-visegrad' as *;

.whead-visegrad {
  .wrapper {
    display: flex;
    justify-content: flex-start;
    height: 80px;
    align-items: center;
    margin: 0 30px;
    border-bottom: 1px solid $micro-visegrad-grey-2;
    padding-top: 10px;
    padding-bottom: 15px;
    @include media-breakpoint-down(sm) {
      flex-wrap: wrap;
      height: auto;
      margin: 0 20px;
      border-bottom: 0;
      padding-bottom: 0;
    }

    .logo {
      width: 250px;
      margin-right: 50px;
      @include media-breakpoint-down(sm) {
        margin-top: 5px;
      }
    }

    .nav {
      padding-top: 20px;
      display: flex;
      justify-content: flex-start;
      @include media-breakpoint-down(sm) {
        width: 100%;
        flex-wrap: wrap;
        border-top: 1px solid $micro-visegrad-grey-2;
        padding-top: 10px;
        margin-top: 15px;
      }

      .link {
        margin-right: 10px;
        display: flex;
        align-items: center;
        background: $micro-visegrad-blue;
        height: 26px;
        //display: inline-block;
        padding: 0 10px;
        color: var(--kui-white);
        text-transform: uppercase;
        font-size: 13px;
        font-weight: 400;
        line-height: 100%;
        @include media-breakpoint-down(sm) {
          font-size: 16px;
          height: 39px;
          margin-bottom: 10px;
          padding: 0 15px;
        }

        &:first-child {
          margin-left: 0;
        }
      }
    }
  }
}
