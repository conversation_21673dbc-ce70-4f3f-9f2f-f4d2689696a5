import { <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import { Component, HostBinding, Input } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { ProgramTypes } from '@trendency/kesma-ui';
import { IconComponent } from '../../icon/icon.component';

@Component({
  selector: 'app-whead-kulturnemzet',
  templateUrl: './whead-kulturnemzet.component.html',
  styleUrls: ['./whead-kulturnemzet.component.scss'],
  imports: [RouterLink, NgIf, NgFor, IconComponent],
})
export class WheadKulturnemzetComponent {
  @Input() programTypes?: ProgramTypes[] = [];
  @Input()
  @HostBinding('class.is-vertical')
  inSidebar = false;
  @Input()
  @HostBinding('class.no-bg')
  noBackground = false;
  @Input()
  @HostBinding('class.no-types')
  hideTypes = false;
  isOpened = false;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router
  ) {}

  public toggleBurger(): void {
    this.isOpened = !this.isOpened;
  }

  public onSelectSubpage(programType: string): void {
    this.isOpened = false;
    this.router.navigate(['/', 'kulturnemzet'], {
      relativeTo: this.route,
      queryParams: {
        'program_type[]': programType,
        rowCount_limit: 12,
        page_limit: 0,
      },
    });
  }
}
