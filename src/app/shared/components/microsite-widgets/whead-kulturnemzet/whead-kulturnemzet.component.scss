@use 'shared' as *;

:host {
  padding: 32px 0;
  width: 100%;
  display: block;
  background-color: var(--kui-slate-200);

  &.no-bg {
    background-color: transparent;
  }

  &.no-types {
    padding: 0 0 24px 0;

    .wrapper {
      max-width: 100%;
      width: 100%;
    }
  }

  &.is-vertical {
    padding: 0;
    margin-left: -16px;
    margin-right: -16px;
    width: calc(100% + 32px);

    hr {
      border: 0;
    }

    .wrapper {
      margin-top: 0;
      max-width: 100%;
      flex-direction: column;
      width: 100%;
    }

    .kultur-logo {
      width: 221px;
    }

    .icon {
      width: 20px;
    }

    .kultur-nav {
      padding: 0 32px 32px;
      overflow-x: auto;
      margin: 0 16px;
    }
  }

  @include media-breakpoint-down(sm) {
    padding: 16px 0 0;
    margin-left: -16px;
    margin-right: -16px;
    width: calc(100% + 32px);

    .kultur-logo {
      width: 221px;
    }
    .icon {
      width: 20px;
    }
    .kultur-nav {
      padding: 0 32px 32px;
      overflow-x: auto;
      margin: 0 16px;
    }
  }
}

.wrapper {
  width: calc(100% - 2 * $side-padding);
  max-width: $layout-max-width;
  display: flex;
  flex-direction: column;
  gap: 24px;

  @include media-breakpoint-down(sm) {
    max-width: 100%;
    flex-direction: column;
    width: 100%;
  }
}

.kultur {
  &-header {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: stretch;
    gap: 12px;
    @include media-breakpoint-down(md) {
      align-items: center;
      justify-content: center;
    }

    hr {
      flex-grow: 1;
      border-color: var(--kui-slate-950);
      border-width: 1px 0 0 0;
      max-height: 1px;

      @include media-breakpoint-down(md) {
        display: none;
      }
    }
  }

  &-nav {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-evenly;
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;

    &-item {
      margin: 0 8px;
      font-size: 14px;
      font-weight: 700;
      line-height: 18px;
      color: var(--kui-slate-950);
      @include transition;

      &:hover,
      &:active,
      &:focus {
        color: var(--kui-blue-700);
        @include transition;
      }

      cursor: pointer;
    }
  }
}
