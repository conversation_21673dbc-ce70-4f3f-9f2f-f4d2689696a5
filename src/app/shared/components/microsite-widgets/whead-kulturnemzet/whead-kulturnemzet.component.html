<div class="wrapper">
  <div class="kultur-header">
    <hr />
    <a class="kultur-logo" [routerLink]="['/', 'kulturnemzet']" [queryParams]="{ rowCount_limit: 12, page_limit: 0 }">
      <img src="/assets/images/logo-kulturnemzet.png" loading="lazy" alt="Kultúrnemzet" class="logo" width="332" height="36" />
    </a>
    <a class="kultur-info-icon" [routerLink]="['/', 'kulturnemzet-szerkesztesi-iranyelvek']">
      <img mno-icon="info" [size]="32" loading="lazy" alt="kultúrnemzet-info" />
    </a>
    <hr />
  </div>

  <nav class="kultur-nav" *ngIf="!hideTypes">
    <ng-container *ngFor="let item of programTypes">
      <a *ngIf="item?.isHighlighted" class="kultur-nav-item" (click)="onSelectSubpage(item?.slug ?? '')">
        {{ item?.title }}
      </a>
    </ng-container>
  </nav>
</div>
