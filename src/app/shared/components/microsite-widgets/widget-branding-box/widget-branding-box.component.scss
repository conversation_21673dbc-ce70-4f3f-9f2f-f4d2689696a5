@use 'shared' as *;
@use 'microsites/micro-automotor' as *;
@use 'microsites/micro-lakaskultura' as *;
@use 'microsites/micro-mindmegette' as *;
@use 'microsites/micro-orszagszerte' as *;
@use 'microsites/micro-unilife' as *;

:host {
  @include layoutFullWidth;

  &.auto-motor {
    --kui-brand-widget-bg: var(--kui-brand-orszagszerte-widget-bg);
    --kui-brand-bg: var(--kui-brand-automotor-bg);
    --kui-brand-button-bg: var(--kui-brand-automotor-button-bg);
    --kui-brand-button-fg: var(--kui-brand-automotor-button-fg);
    --kui-brand-label-bg: var(--kui-brand-automotor-label-bg);
    --kui-brand-label-fg: var(--kui-brand-automotor-label-fg);
  }

  &.lakaskultura {
    --kui-brand-widget-bg: var(--kui-brand-orszagszerte-widget-bg);
    --kui-brand-bg: var(--kui-brand-lakaskultura-bg);
    --kui-brand-button-bg: var(--kui-brand-lakaskultura-button-bg);
    --kui-brand-button-fg: var(--kui-brand-lakaskultura-button-fg);
    --kui-brand-label-bg: var(--kui-brand-lakaskultura-label-bg);
    --kui-brand-label-fg: var(--kui-brand-lakaskultura-label-fg);
  }

  &.mindmegette {
    --kui-brand-widget-bg: var(--kui-brand-mindmegette-widget-bg);
    --kui-brand-bg: var(--kui-brand-mindmegette-bg);
    --kui-brand-button-bg: var(--kui-brand-mindmegette-button-bg);
    --kui-brand-button-fg: var(--kui-brand-mindmegette-button-fg);
    --kui-brand-label-bg: var(--kui-brand-mindmegette-label-bg);
    --kui-brand-label-fg: var(--kui-brand-mindmegette-label-fg);

    .wrapper {
      padding: 12px;
      border: 1px solid var(--kui-gray-300);
      //border-bottom-left-radius: 8px;
      //border-bottom-right-radius: 8px;
    }

    .branding-article-list {
      [mno-article-card]::ng-deep {
        .article-label {
          // border-radius: 5px;
          font-family: Roboto, sans-serif;
          font-weight: 300;
          padding: 2px 4px;
          text-transform: uppercase;
          background-color: var(--kui-brand-label-bg);
          color: var(--kui-brand-label-fg);
        }

        .article-title {
          // font-family: DM Sans;
          font-family: Roboto, sans-serif;
          font-size: 20px;
          text-transform: uppercase;
          //font-weight: 700;
          font-weight: 300;
          line-height: 26px;
          //color: var(--kui-shark-950);
          color: #5c5c5c;
        }

        .article-thumbnail {
          border-radius: 4px;
        }
      }
    }

    .btn-ghost-branding {
      // font-family: DM Sans;
      font-weight: 300;
      font-family: Roboto, sans-serif;
      text-transform: uppercase;
      // border-radius: 8px;
      &:hover {
        // temporary override until new mindmegette is released
        border-color: #9bce11;
        background: #9bce11;
        color: white;
        opacity: 0.85;
      }
    }
  }

  &.unilife {
    --kui-brand-widget-bg: var(--kui-brand-orszagszerte-widget-bg);
    --kui-brand-bg: var(--kui-brand-unilife-bg);
    --kui-brand-button-bg: var(--kui-brand-unilife-button-bg);
    --kui-brand-button-fg: var(--kui-brand-unilife-button-fg);
    --kui-brand-label-bg: var(--kui-brand-unilife-label-bg);
    --kui-brand-label-fg: var(--kui-brand-unilife-label-fg);
  }

  &.orszagszerte {
    padding: 24px 0;
    --kui-brand-widget-bg: var(--kui-brand-orszagszerte-widget-bg);
    --kui-brand-bg: var(--kui-brand-orszagszerte-bg);
    --kui-brand-button-bg: var(--kui-brand-orszagszerte-button-bg);
    --kui-brand-button-fg: var(--kui-brand-orszagszerte-button-fg);
    --kui-brand-label-bg: var(--kui-brand-orszagszerte-label-bg);
    --kui-brand-label-fg: var(--kui-brand-orszagszerte-label-fg);

    .branding-article-list {
      [mno-article-card]::ng-deep {
        margin-bottom: 0;

        .article-label {
          border-radius: 0;
          padding: 0;
          text-transform: none;
        }

        .article-title {
          color: var(--kui-slate-950);
          font-family: var(--kui-font-primary);
          font-size: 16px;
          font-style: normal;
          font-weight: 700;
          line-height: 21px; /* 131.25% */
        }

        .article-thumbnail {
          border-radius: 2px;
        }
      }
    }
  }

  padding: 12px 0;
  display: block;
  background-color: var(--kui-brand-widget-bg);
}

.wrapper {
  max-width: $layout-max-width;
  //   margin: 0 auto;
  //  max-width: calc(100% - 2 * $side-padding);
  //  @include media-breakpoint-down(md) {
  //    max-width: calc(100vw - 2 * $mobile-side-padding);
  //  }

  display: flex;
  flex-direction: column;
  gap: 24px;
}

.branding {
  &-content-container {
    display: flex;
    gap: 24px;
    flex-direction: column;
  }

  &-article-list {
    display: grid;
    gap: 24px;
    grid-template-columns: 1fr 1fr 1fr 1fr;

    @include media-breakpoint-down(md) {
      grid-template-columns: 1fr;
    }

    [mno-article-card]::ng-deep {
      .article-label {
        color: var(--kui-brand-orszagszerte-label-fg);
      }

      .article-title {
        font-size: 16px;
        font-weight: 700;
        line-height: 21px;
      }
    }
  }
}

.btn-ghost-branding {
  background-color: var(--kui-brand-button-bg);
  color: var(--kui-brand-button-fg);
  border-color: var(--kui-brand-button-bg);
  @include transition;

  &:hover {
    @include GhostButtonHighlight(var(--kui-brand-button-fg), var(--kui-brand-button-bg));
  }

  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0.01em;
  text-align: center;
}
