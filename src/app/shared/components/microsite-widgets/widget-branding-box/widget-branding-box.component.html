<div class="wrapper">
  <app-whead-automotor *ngIf="brand === 'automotor' || brand === 'auto-motor'"></app-whead-automotor>
  <app-whead-lakaskultura *ngIf="brand === 'lakaskultura'"></app-whead-lakaskultura>
  <app-whead-orszagszerte *ngIf="brand === 'orszagszerte'"></app-whead-orszagszerte>
  <app-whead-mindmegette *ngIf="brand === 'mindmegette'"></app-whead-mindmegette>
  <app-whead-unilife *ngIf="brand === 'unilife'"></app-whead-unilife>

  <div class="branding-content-container">
    <section class="branding-article-list">
      <ng-container *ngFor="let cardData of selectedBrandingBoxes; let i = index">
        <article [data]="cardData" [showArticleLabels]="showLabels" [styleID]="ArticleCardType.Img4TopTitleLeadBadgeLarge" mno-article-card></article>
      </ng-container>
    </section>
    <a *ngIf="brand === 'mindmegette'" [routerLink]="['/', brand]" class="btn btn-ghost btn-ghost-branding">Irány a magazin</a>
  </div>
</div>
