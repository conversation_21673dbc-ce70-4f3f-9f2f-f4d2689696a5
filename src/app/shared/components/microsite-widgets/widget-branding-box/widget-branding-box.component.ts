import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>orOf, NgIf } from '@angular/common';
import { Component, HostBinding, Input, OnInit } from '@angular/core';
import { ArticleCard, backendDateToDate, LayoutElementContentConfigurationBrandingBox } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../../article-card/article-card.component';
import { RouterLink } from '@angular/router';
import { WheadUnilifeComponent } from '../whead-unilife/whead-unilife.component';
import { WheadMindmegetteComponent } from '../whead-mindmegette/whead-mindmegette.component';
import { WheadOrszagszerteComponent } from '../whead-orszagszerte/whead-orszagszerte.component';
import { WheadLakaskulturaComponent } from '../whead-lakaskultura/whead-lakaskultura.component';
import { WheadAutomotorComponent } from '../whead-automotor/whead-automotor.component';
import { ArticleCardType } from 'src/app/shared/definitions';

@Component({
  selector: 'app-widget-branding-box',
  templateUrl: './widget-branding-box.component.html',
  styleUrls: ['./widget-branding-box.component.scss'],
  imports: [
    ArticleCardComponent,
    NgIf,
    RouterLink,
    WheadUnilifeComponent,
    WheadMindmegetteComponent,
    WheadOrszagszerteComponent,
    WheadLakaskulturaComponent,
    WheadAutomotorComponent,
    NgFor,
    NgForOf,
  ],
})
export class WidgetBrandingBoxComponent implements OnInit {
  @Input() widgetData: LayoutElementContentConfigurationBrandingBox;
  @Input() @HostBinding('class') brand: string;
  @Input() showLabels = true;

  public selectedBrandingBoxes: any[];

  readonly ArticleCardType = ArticleCardType;

  ngOnInit(): void {
    this.selectedBrandingBoxes = (this.widgetData?.selectedBrandingBoxes ?? []).slice(0, 4).map((article) => {
      const box: Record<keyof ArticleCard, any> = { ...article } as any;
      if (box?.thumbnail && typeof box.thumbnail === 'string') {
        box.thumbnail = { url: box.thumbnail } as any;
      }
      box.lead = undefined;
      box.publishDate = backendDateToDate(box.publishDate) as any;
      box.columnSlug = box.brand ?? box.columnSlug ?? this.brand;
      box.brand = this.brand;
      (box as any).tags = [...((box as any)?.tags || []), { slug: this.brand, title: this.brand }];
      if (!(box as any).publicAuthor && this.brand === 'mindmegette') {
        (box as any).publicAuthor = 'Mindmegette';
        (box as any).avatar = '/images/mindmegette-logo-mini-color.svg';
      }
      return box;
    });
  }
}
