import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { NewsBrandingBoxComponent } from './news-branding-box/news-branding-box.component';
import { BrandingBoxBrand } from '../../../definitions';

@Component({
  selector: 'app-widget-branding-box-ex',
  templateUrl: './widget-branding-box-ex.component.html',
  styleUrls: ['./widget-branding-box-ex.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NewsBrandingBoxComponent],
})
export class WidgetBrandingBoxExComponent {
  @Input() @HostBinding('class') brand: BrandingBoxBrand;
  @Input() showLabels = true;
}
