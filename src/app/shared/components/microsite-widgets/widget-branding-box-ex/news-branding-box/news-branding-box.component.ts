import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { ApiResult, NewsBrandingBoxComponent as KesmaNewsBrandingBoxComponent } from '@trendency/kesma-ui';
import { AsyncPipe, NgIf } from '@angular/common';
import { BackendExternalFeedData, BrandingBoxArticle } from '../../../../definitions';

@Component({
  selector: 'app-news-branding-box',
  templateUrl: 'news-branding-box.component.html',
  styleUrls: ['news-branding-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, NgIf, KesmaNewsBrandingBoxComponent],
})
export class NewsBrandingBoxComponent {
  private readonly dataSubject = new BehaviorSubject<BrandingBoxArticle[]>([]);
  private dataRequested = false;

  constructor(private readonly reqService: ReqService) {}

  public get data$(): Observable<BrandingBoxArticle[]> {
    if (!this.dataRequested) {
      this.dataRequested = true;
      this.fetchExternalFeedData('https://hirado.hu/mobil/export/hirado/magyarnemzet.xml');
    }
    return this.dataSubject;
  }

  private fetchExternalFeedData(externalUrl: string): void {
    this.reqService
      .get<ApiResult<BackendExternalFeedData>>('external-rss-feed', {
        params: {
          url: externalUrl,
        },
        headers: {
          portal: 'magyar_nemzet',
        },
      } as IHttpOptions)
      .pipe(
        map(({ data: { items } }): BrandingBoxArticle[] =>
          items
            ? items
                .map(
                  (item) =>
                    ({
                      ...item,
                      lead: item?.description,
                      url: item?.link,
                    }) as unknown as BrandingBoxArticle
                )
                .slice(0, 7)
            : []
        )
      )
      .subscribe((data) => this.dataSubject.next(data));
  }
}
