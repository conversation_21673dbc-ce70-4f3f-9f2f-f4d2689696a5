import { Component, Input } from '@angular/core';
import { ArticleComponentType, BrandingBoxArticle } from '../../../../../definitions';
import { NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'app-vg-branding-box-article',
  templateUrl: './vg-branding-box-article.component.html',
  styleUrls: ['./vg-branding-box-article.component.scss'],
  imports: [NgIf, NgClass],
})
export class VgBrandingBoxArticleComponent {
  @Input() type: ArticleComponentType;
  @Input() article: BrandingBoxArticle;
}
