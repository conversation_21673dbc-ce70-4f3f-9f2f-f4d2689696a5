@use 'shared' as *;

:host {
  $hover-color: #439582;

  .link-wrapper {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;

    &:hover {
      .title {
        color: $hover-color;
      }

      .desc {
        color: $hover-color;
      }

      .image {
        filter: grayscale(45%);
      }
    }

    .image {
      display: block;
      width: 100%;
      transition: all 200ms;
    }

    .title {
      font-family: 'Roboto', sans-serif;
      font-size: 17px;
      font-weight: 400;
      line-height: 24px;
      color: black;

      &.main-sized {
        font-size: 29px;
        line-height: 35px;
      }

      @include media-breakpoint-down(sm) {
        // specificity hack
        &.title {
          font-size: 23px;
          line-height: 30px;
        }
      }
    }

    .desc {
      font-family: 'Roboto', sans-serif;
      font-size: 16px;
      font-weight: 300;
      line-height: 21px;
      color: black;
    }
  }
}
