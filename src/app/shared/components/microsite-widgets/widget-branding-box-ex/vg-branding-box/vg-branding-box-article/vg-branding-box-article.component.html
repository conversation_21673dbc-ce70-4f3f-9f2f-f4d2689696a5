<a *ngIf="article" [href]="article.url" class="link-wrapper" target="_blank">
  <img *ngIf="type !== 'rest-in-group' && article.thumbnail" [alt]="article.title || ''" [src]="article.thumbnail" class="image" loading="lazy" />

  <div [ngClass]="{ 'main-sized': type === 'main' }" class="title">
    {{ article.title }}
  </div>

  <div *ngIf="type === 'main' && article.lead" class="desc">
    {{ article.lead }}
  </div>
</a>
