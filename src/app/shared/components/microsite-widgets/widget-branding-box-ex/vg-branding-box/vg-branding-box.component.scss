@use 'shared' as *;

:host {
  padding: 32px 0;
  width: 100%;
  display: block;
  background-color: var(--kui-slate-100);
}

.wrapper {
  max-width: $layout-max-width;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.bb {
  &-content-container {
    display: flex;
    gap: 24px;
    @include media-breakpoint-down(md) {
      flex-direction: column;
    }
  }

  &-article-list {
    display: grid;
    gap: 24px;
    grid-template-columns: 1fr 1fr 1fr 1fr;

    @include media-breakpoint-down(md) {
      grid-template-columns: 1fr;
    }
  }

  &-header {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
  }

  &-link {
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
    color: var(--kui-slate-950);
    @include transition;

    &:hover {
      color: var(--kui-blue-500);
      @include transition;
    }
  }
}
