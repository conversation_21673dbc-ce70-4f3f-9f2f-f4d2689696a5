@use 'shared' as *;

:host {
  padding: 32px 0;
  width: 100%;
  display: block;
  background-color: var(--kui-brand-widget-bg);
}

.wrapper {
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.brand {
  &-header {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
  }

  &-link {
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
    color: var(--kui-slate-950);
    @include transition;

    &:hover {
      color: var(--kui-blue-500);
      @include transition;
    }
  }
}
