@use 'shared' as *;

:host {
  padding: 0;
  width: 100%;
  display: block;
  // background-color: var(--kui-brand-widget-bg);
}

.wrapper {
  height: 52px;
  width: 100%;
  max-width: 100%;

  display: flex;
  gap: 24px;
  // background-color: var(--kui-brand-bg);
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  padding: 0 12px;
  align-items: center;
}

.brand {
  &-header {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
  }

  &-link {
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
    color: var(--kui-slate-950);
  }
}
