import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostBinding, Input, OnInit } from '@angular/core';
import { ArticleCard, backendDateToDate, ProgramRecommendationItem } from '@trendency/kesma-ui';
import { forkJoin } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { KulturnemzetService } from '../../../../feature/kulturnemzet/kulturnemzet.service';
import { ArticleCardComponent } from '../../article-card/article-card.component';
import { NgFor, NgIf } from '@angular/common';
import { KulturnemzetSidebarComponent } from '../kulturnemzet-sidebar/kulturnemzet-sidebar.component';
import { RouterLink } from '@angular/router';
import { IconComponent } from '../../icon/icon.component';
import { WheadKulturnemzetComponent } from '../whead-kulturnemzet/whead-kulturnemzet.component';
import { ArticleCardType, KulturnemzetWidget } from '../../../definitions';

@Component({
  selector: 'app-widget-kulturnemzet',
  templateUrl: './widget-kulturnemzet.component.html',
  styleUrls: ['./widget-kulturnemzet.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleCardComponent, NgIf, NgFor, RouterLink, KulturnemzetSidebarComponent, IconComponent, WheadKulturnemzetComponent],
})
export class WidgetKulturnemzetComponent implements OnInit {
  @Input() @HostBinding('class.is-vertical') inSidebar = false;
  @Input() showLabels = true;

  public isWidgetLoading = true;
  widgetData?: KulturnemzetWidget;
  programCards: ArticleCard[] = [];
  readonly ArticleCardType = ArticleCardType;
  private readonly programLimit = '6';

  constructor(
    private readonly kulturnemzetService: KulturnemzetService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    // Fetching data from BE
    forkJoin([
      this.kulturnemzetService.getProgramTypes(),
      this.kulturnemzetService.getProgramRecommendedArticles(),
      this.kulturnemzetService.getHighlightedProgramRecommendations(this.programLimit),
    ])
      .pipe(
        map(([programTypes, recommendedArticles, recommendedPrograms]) => ({
          programTypes,
          recommendedArticles,
          recommendedPrograms,
        }))
      )
      .pipe(take(1))
      .subscribe((data: KulturnemzetWidget) => {
        this.widgetData = this.addListedLocations(data);
        this.programCards = (this.widgetData?.recommendedPrograms?.data ?? []).map(
          (program) =>
            ({
              id: program.id,
              title: program.title,
              slug: program?.slug,
              thumbnail: { url: program.image },
              labelText: program?.types?.[0]?.title,
              tags: program.tags,
              lead: program.lead,
              thumbnailFocusedImages: (program as ProgramRecommendationItem)?.imageFocusedImages,
              publishDate: program.startDate ? backendDateToDate(program.startDate as string) : '',
            }) as ArticleCard
        );
        this.isWidgetLoading = false;
        this.changeRef.detectChanges();
      });
  }

  private addListedLocations(response: KulturnemzetWidget): KulturnemzetWidget | undefined {
    if (!response) {
      return;
    }
    let mappedRecommendedPrograms: ProgramRecommendationItem[] = [];
    if (response?.recommendedPrograms?.data) {
      mappedRecommendedPrograms = response?.recommendedPrograms?.data;
    }
    return {
      ...response,
      recommendedPrograms: {
        ...response?.recommendedPrograms,
        data: mappedRecommendedPrograms,
      },
    };
  }
}
