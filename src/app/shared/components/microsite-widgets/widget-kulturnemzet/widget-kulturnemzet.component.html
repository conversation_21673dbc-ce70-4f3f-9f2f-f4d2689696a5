<app-whead-kulturnemzet [inSidebar]="inSidebar" [programTypes]="widgetData?.programTypes"></app-whead-kulturnemzet>
<ng-container *ngIf="!isWidgetLoading">
  <div class="wrapper">
    <div class="kultur-content-container">
      <section [class.full-width]="!widgetData?.recommendedArticles?.length" class="kultur-program-list">
        <ng-container *ngFor="let elem of programCards">
          <article
            [asProgram]="true"
            [data]="elem"
            [showArticleLabels]="showLabels"
            [styleID]="inSidebar ? ArticleCardType.ImgRightTagsTitleLead50 : ArticleCardType.Img1TopTagsTitleLeadBadge"
            mno-article-card
          ></article>
        </ng-container>
      </section>

      <ng-container *ngIf="widgetData?.recommendedArticles?.length">
        <app-kulturnemzet-sidebar [maxItems]="5" [recommendedArticles]="widgetData?.recommendedArticles ?? []"></app-kulturnemzet-sidebar>
      </ng-container>
    </div>

    <a [routerLink]="['/', 'kulturnemzet']" class="btn btn-ghost btn-ghost-dark-transparent">
      További programok
      <i mno-icon="chevron-right"></i>
    </a>
  </div>
</ng-container>
