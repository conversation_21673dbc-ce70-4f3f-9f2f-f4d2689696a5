@use 'shared' as *;

:host {
  padding: 32px 0;
  display: block;
  background-color: var(--kui-slate-200);
  margin-bottom: -20px;

  &:not(.is-vertical) {
    @include media-breakpoint-up(md) {
      @include layoutFullWidth;
    }
  }
  @include media-breakpoint-down(sm) {
    padding: 16px;
  }

  &.is-vertical {
    padding: 16px;

    .wrapper {
      max-width: 100%;
      width: 100%;
      flex-direction: column;
    }

    .kultur {
      &-program-list {
        grid-template-columns: 1fr;
        flex: 1;
      }

      &-content-container {
        gap: 24px;
        flex-direction: column;
      }
    }
  }
}

.wrapper {
  margin: 0 auto;
  width: calc(100% - 2 * $side-padding);
  max-width: $layout-max-width;
  display: flex;
  flex-direction: column;
  gap: 24px;

  @include media-breakpoint-down(sm) {
    max-width: 100%;
    width: 100%;
    flex-direction: column;
  }
}

app-kulturnemzet-sidebar {
  flex: 1;
}

.kultur {
  &-content-container {
    display: flex;
    gap: 24px;
    @include media-breakpoint-down(md) {
      flex-direction: column;
    }
  }

  &-program-list {
    display: grid;
    gap: 24px;
    grid-template-columns: 1fr 1fr 1fr;
    flex: 3;
    @include media-breakpoint-down(md) {
      grid-template-columns: 1fr;
      flex: 1;
    }
  }
}

[mno-article-card] {
  border-radius: 2px;
  border: 1px solid var(--kui-slate-300);
}
