@use 'shared' as *;

:host {
  padding: 32px 0;
  width: 100%;
  display: block;
  background-color: var(--kui-slate-100);
}

.wrapper {
  margin: 0 auto;
  max-width: calc(100% - 2 * $side-padding);
  display: flex;
  flex-direction: column;
  gap: 24px;

  @include media-breakpoint-down(sm) {
    max-width: calc(100vw - 2 * $mobile-side-padding);
  }
}

.orszag {
  &-content-container {
    display: flex;
    gap: 24px;
    @include media-breakpoint-down(md) {
      flex-direction: column;
    }
  }

  &-article-list {
    display: grid;
    gap: 24px;
    grid-template-columns: 1fr 1fr 1fr 1fr;

    @include media-breakpoint-down(md) {
      grid-template-columns: 1fr;
    }
  }
}
