import { Component, Input } from '@angular/core';
import { Tag, VisegradPostArticle } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../../article-card/article-card.component';
import { WheadOrszagszerteComponent } from '../whead-orszagszerte/whead-orszagszerte.component';
import { ArticleCardType } from 'src/app/shared/definitions';
import { NgForOf } from '@angular/common';

@Component({
  selector: 'app-widget-orszagszerte',
  templateUrl: './widget-orszagszerte.component.html',
  styleUrls: ['./widget-orszagszerte.component.scss'],
  imports: [ArticleCardComponent, WheadOrszagszerteComponent, NgForOf],
})
export class WidgetOrszagszerteComponent {
  @Input() widgetData: (VisegradPostArticle & { tag?: Tag; sourceTag?: Tag })[];
  readonly ArticleCardType = ArticleCardType;
}
