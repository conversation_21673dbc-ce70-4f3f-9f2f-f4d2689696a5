<div class="micro-lakaskultura">
  <section class="micro-widget">
    <div class="wrapper">
      <app-whead-lakaskultura></app-whead-lakaskultura>
      <div class="article-list">
        <div class="col-article col-12 col-lg-6">
          <a class="lakaskultura-article-card lakaskultura-article-big-card" *ngIf="widgetData[0]">
            <div
              trImageLazyLoad
              [title]="widgetData[0]?.title"
              class="image"
              *ngIf="widgetData[0].img"
              [style.background-image]="'url(' + widgetData[0].img + ')'"
            >
              <div class="title-container">
                <div class="title desktop-only">{{ widgetData[0]?.title }}</div>
              </div>
            </div>
            <div class="title mobile-only">{{ widgetData[0]?.title }}</div>
            <div class="desc" *ngIf="widgetData[0].desc">{{ widgetData[0]?.desc }}</div>
          </a>
        </div>

        <div class="col-article col-12 col-md-6 col-lg-3">
          <ng-container *ngFor="let cardData of widgetData; let i = index">
            <a class="lakaskultura-article-card" *ngIf="i > 0 && i < 4">
              <div class="image" *ngIf="cardData.img" trImageLazyLoad [title]="cardData?.title" [style.background-image]="'url(' + cardData.img + ')'"></div>
              <div class="title">{{ cardData.title }}</div>
              <div class="desc" *ngIf="cardData.desc">{{ cardData.desc }}</div>
            </a>
          </ng-container>
        </div>

        <div class="col-article col-12 col-md-6 col-lg-3">
          <ng-container *ngFor="let cardData of widgetData; let i = index">
            <a class="lakaskultura-article-card" *ngIf="i >= 4">
              <div class="image" *ngIf="cardData.img" trImageLazyLoad [title]="cardData?.title" [style.background-image]="'url(' + cardData.img + ')'"></div>
              <div class="title">{{ cardData.title }}</div>
              <div class="desc" *ngIf="cardData.desc">{{ cardData.desc }}</div>
            </a>
          </ng-container>
        </div>
      </div>
    </div>
  </section>
</div>
