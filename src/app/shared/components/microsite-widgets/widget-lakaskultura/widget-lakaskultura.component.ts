import { Component, Input } from '@angular/core';
import { WheadLakaskulturaComponent } from '../whead-lakaskultura/whead-lakaskultura.component';
import { ImageLazyLoadDirective } from '@trendency/kesma-core';
import { NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'app-widget-lakaskultura',
  templateUrl: './widget-lakaskultura.component.html',
  styleUrls: ['./widget-lakaskultura.component.scss'],
  imports: [WheadLakaskulturaComponent, ImageLazyLoadDirective, NgFor, NgIf],
})
export class WidgetLakaskulturaComponent {
  @Input() widgetData: any;
}
