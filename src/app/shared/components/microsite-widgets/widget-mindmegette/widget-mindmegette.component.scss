@use 'shared' as *;
@use 'microsites/micro-lakaskultura' as *;
@use 'microsites/micro-mindmegette' as *;

.micro-mindmegette {
  margin: 30px 0;
  @include media-breakpoint-down(xs) {
    margin-left: -15px;
    margin-right: -15px;
    width: calc(100% + 30px);
  }

  .wrapper {
    background-color: $micro-mindmegette-grey-5;
    border-top: 2px solid $micro-mindmegette-green;
  }

  .article-list {
    display: flex;
    flex-wrap: wrap;
    padding: 20px 15px;
    @include media-breakpoint-down(sm) {
      padding: 20px 5px;
    }

    .col-article {
      width: 50%;
      flex-wrap: wrap;
      display: flex;
      align-items: flex-start;
      flex-direction: column;

      .mindmegette-article-card {
        position: relative;
        margin-bottom: 50px;
        width: 100%;

        &:after {
          content: ' ';
          display: block;
          width: 60px;
          height: 3px;
          background-color: $micro-mindmegette-green;
          position: absolute;
          bottom: -22px;
          left: 0;
        }

        &:last-child {
          @include media-breakpoint-up(md) {
            margin-bottom: 0;
            &:after {
              display: none;
            }
          }
        }

        .desktop-only {
          @include media-breakpoint-down(sm) {
            display: none;
          }
        }

        .mobile-only {
          @include media-breakpoint-up(md) {
            display: none;
          }
        }

        .image {
          width: 100%;
          margin-bottom: 10px;
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;

          @include imgRatio(270%, 140%);
          @include media-breakpoint-down(sm) {
            @include imgRatio(335%, 173%);
          }
        }

        .title {
          font-family: 'Dosis', sans-serif;
          color: $micro-mindmegette-green;
          font-size: 20px;
          font-weight: 600;
          line-height: 26px;
          margin-bottom: 10px;
        }

        .desc {
          color: $micro-mindmegette-grey;
          font-size: 16px;
          font-weight: 300;
          line-height: 24px;
        }

        &.mindmegette-article-big-card {
          @include media-breakpoint-up(md) {
            width: 100%;
            position: relative;
            @include media-breakpoint-down(sm) {
              margin-bottom: 50px;
              &:after {
                content: ' ';
                display: block;
                width: 60px;
                height: 3px;
                background-color: $micro-mindmegette-grey;
                position: absolute;
                bottom: -22px;
                left: 0;
              }
            }
            .image {
              width: 100%;
              @include imgRatio(580%, 410%);
              position: relative;
              background-size: cover;
              background-position: center;
              background-repeat: no-repeat;

              .title-container {
                padding: 15px 25px;
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;

                .title {
                  font-size: 34px;
                  font-weight: 700;
                  font-style: normal;
                  letter-spacing: normal;
                  line-height: 42px;
                  color: $micro-mindmegette-white;
                  display: inline;
                  box-shadow:
                    10px 0 0 $micro-mindmegette-green,
                    -1px 0 0 $micro-mindmegette-green,
                    -10px 0 0 $micro-mindmegette-green,
                    1px 0 0 $micro-mindmegette-green;
                  padding: 0;
                  background-color: $micro-mindmegette-green;
                  -webkit-box-decoration-break: clone;
                  -o-box-decoration-break: clone;
                  box-decoration-break: clone;
                  position: relative;
                  z-index: 2;
                }
              }
            }

            .desc {
              color: $micro-mindmegette-grey;
              font-size: 20px;
              font-weight: 300;
              font-style: normal;
              letter-spacing: normal;
              line-height: 30px;
              padding-top: 10px;
            }
          }
        }
      }
    }
  }
}
