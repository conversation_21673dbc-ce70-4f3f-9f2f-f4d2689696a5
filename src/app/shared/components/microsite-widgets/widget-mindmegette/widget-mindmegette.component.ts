import { Component, Input } from '@angular/core';
import { WheadMindmegetteComponent } from '../whead-mindmegette/whead-mindmegette.component';
import { <PERSON><PERSON><PERSON>, NgIf, NgStyle } from '@angular/common';
import { ImageLazyLoadDirective } from '@trendency/kesma-core';

@Component({
  selector: 'app-widget-mindmegette',
  templateUrl: './widget-mindmegette.component.html',
  styleUrls: ['./widget-mindmegette.component.scss'],
  imports: [WheadMindmegetteComponent, NgIf, NgFor, ImageLazyLoadDirective, NgStyle],
})
export class WidgetMindmegetteComponent {
  @Input() widgetData: any;
}
