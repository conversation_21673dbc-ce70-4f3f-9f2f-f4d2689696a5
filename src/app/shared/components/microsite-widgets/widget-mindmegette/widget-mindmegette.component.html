<div class="micro-mindmegette">
  <section class="micro-widget">
    <div class="wrapper">
      <app-whead-mindmegette></app-whead-mindmegette>
      <div class="article-list">
        <div class="col-article col-12 col-lg-6">
          <a *ngIf="widgetData[0]" class="mindmegette-article-card mindmegette-article-big-card">
            <div
              *ngIf="widgetData[0]?.img"
              [ngStyle]="{ 'background-image': 'url(' + widgetData[0]?.img + ')' }"
              [title]="widgetData[0]?.title"
              class="image"
              trImageLazyLoad
            >
              <div class="title-container">
                <div class="title desktop-only">{{ widgetData[0]?.title }}</div>
              </div>
            </div>
            <div class="title mobile-only">{{ widgetData[0]?.title }}</div>
            <div *ngIf="widgetData[0]?.desc" class="desc">{{ widgetData[0]?.desc }}</div>
          </a>
        </div>

        <div class="col-article col-12 col-md-6 col-lg-3">
          <ng-container *ngFor="let cardData of widgetData; let i = index">
            <a *ngIf="i > 0 && i < 4" class="mindmegette-article-card">
              <div
                *ngIf="cardData?.img"
                [ngStyle]="{ 'background-image': 'url(' + cardData?.img + ')' }"
                [title]="cardData?.title ?? widgetData[0]?.title"
                class="image"
                trImageLazyLoad
              ></div>
              <div class="title">{{ cardData?.title }}</div>
              <div *ngIf="cardData?.desc" class="desc">{{ cardData?.desc }}</div>
            </a>
          </ng-container>
        </div>

        <div class="col-article col-12 col-md-6 col-lg-3">
          <ng-container *ngFor="let cardData of widgetData; let i = index">
            <a *ngIf="i >= 4" class="mindmegette-article-card">
              <div
                *ngIf="cardData?.img"
                [ngStyle]="{ 'background-image': 'url(' + cardData?.img + ')' }"
                [title]="cardData?.title"
                class="image"
                trImageLazyLoad
              ></div>
              <div class="title">{{ cardData?.title }}</div>
              <div *ngIf="cardData?.desc" class="desc">{{ cardData?.desc }}</div>
            </a>
          </ng-container>
        </div>
      </div>
    </div>
  </section>
</div>
