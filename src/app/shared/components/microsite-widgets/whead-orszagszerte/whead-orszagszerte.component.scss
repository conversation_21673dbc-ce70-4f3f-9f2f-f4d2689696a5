@use 'shared' as *;

:host {
  padding: 24px 0 16px;
  width: 100%;
  display: block;
  background-color: var(--kui-slate-100);
}

.wrapper {
  width: 100%;
  max-width: 100%;

  display: flex;
  flex-direction: column;
  gap: 24px;
}

.orszag {
  &-header {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
  }

  &-link {
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
    color: var(--kui-slate-950);
    @include transition;

    &:hover {
      color: var(--kui-blue-500);
      @include transition;
    }
  }
}
