import { Component, Input, OnInit } from '@angular/core';
import { ArticleCard, BackendArticleSearchResult } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../../article-card/article-card.component';
import { NgFor } from '@angular/common';
import { ArticleCardType } from 'src/app/shared/definitions';

@Component({
  selector: 'app-kulturnemzet-sidebar',
  templateUrl: './kulturnemzet-sidebar.component.html',
  styleUrls: ['./kulturnemzet-sidebar.component.scss'],
  imports: [ArticleCardComponent, NgFor],
})
export class KulturnemzetSidebarComponent implements OnInit {
  @Input() maxItems = 3;
  @Input() recommendedArticles: BackendArticleSearchResult[];
  slicedArticles: ArticleCard[];
  ArticleCardType = ArticleCardType;

  ngOnInit(): void {
    this.slicedArticles = this.recommendedArticles.slice(0, this.maxItems).map((article) => ({
      ...article,
      thumbnail: {
        url: article.thumbnail,
      },
    })) as any as ArticleCard[];
  }
}
