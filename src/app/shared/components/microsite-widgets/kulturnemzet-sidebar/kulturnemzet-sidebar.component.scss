@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  gap: 24px;

  [mno-article-card] {
    border: 1px solid var(--kui-slate-300);

    &::ng-deep {
      padding-left: 4px;
      padding-bottom: 4px;
      padding-top: 4px;

      .article-title {
        font-size: 16px;
        line-height: 21px;
        letter-spacing: 0em;
      }

      .article-thumbnail-figure {
        border-top-right-radius: 2px;
        margin-top: -4px;
        margin-right: -1px;
        min-width: 141px;
        width: 141px;
        max-width: 141px;
      }
    }
  }
}
