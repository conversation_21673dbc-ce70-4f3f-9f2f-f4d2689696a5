@if (data) {
  @switch (styleId) {
    @case (WhereTheBallWillBeCardTypes.ColumnTitleTagLeadAuthor) {
      <article class="where-the-ball-will-be">
        <div class="background"></div>
        <div class="where-the-ball-will-be-container">
          <div class="main-container">
            <a class="column" [routerLink]="['/', 'rovat', 'ahol-a-labda-lesz']">Ahol a labda lesz</a>
            <div class="divider"></div>
            <a class="title" [routerLink]="articleLink">
              <img
                class="title-quote-left"
                [color]="'lightgray'"
                [showLabel]="false"
                [size]="36"
                [width]="36"
                alt="Nagy idézőjel ikon"
                loading="lazy"
                mno-icon="idezojelek"
              />
              {{ data?.title }}
              <img
                class="title-quote-right"
                [color]="'lightgray'"
                [showLabel]="false"
                [size]="36"
                [width]="36"
                alt="Nagy idézőjel ikon"
                loading="lazy"
                mno-icon="idezojelek"
              />
            </a>
            <a class="lead" [routerLink]="articleLink">{{ data?.lead }}</a>
          </div>
          @if (data.author?.name) {
            <a class="author" [routerLink]="['/', 'szerzo', data?.author?.slug]">
              <img class="author-image" [src]="data.author?.avatarUrl || 'assets/images/placeholder.svg'" />
              <span>{{ data.author?.name }}</span>
            </a>
          }
        </div>
      </article>
    }
  }
}
