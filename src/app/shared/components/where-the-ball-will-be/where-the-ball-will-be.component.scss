@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  color: var(--kui-white);

  @include media-breakpoint-down(md) {
    width: calc(100% + 32px);
    margin-inline: -16px;
  }

  .background {
    background-image: url('/assets/images/where-the-ball-will-be.jpg');
    background-repeat: no-repeat;
    background-size: cover;
    grid-area: stack;
    height: 100%;
    width: 100%;
  }

  .where-the-ball-will-be {
    display: grid;
    overflow: hidden;
    min-height: 420px;
    container-type: inline-size;
    color: var(--kui-white);
    grid-template-areas: 'stack';

    @include media-breakpoint-up(lg) {
      &:hover {
        .where-the-ball-will-be-container {
          transform: translateY(30%);
          transition: all 0.15s ease-in-out;

          @container (max-width: 410px) {
            transform: translateY(40%);
          }
        }

        .main-container {
          justify-content: flex-start;
        }

        .author,
        .lead {
          opacity: 0;
          visibility: hidden;
        }
      }
    }

    &-container {
      display: grid;
      justify-content: space-between;
      flex-direction: column;
      grid-area: stack;
      background-color: rgba(14, 42, 67);
      opacity: 0.8;
      text-align: center;
      width: 100%;
    }

    .main-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 32px;
      row-gap: 16px;

      @container (max-width: 410px) {
        padding: 32px 17px;
      }

      .column {
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        line-height: 32px;
        letter-spacing: 0.24px;
        color: var(--kui-white);
      }

      .divider {
        width: 230px;
        height: 1px;
        background: var(--kui-white);
      }

      .title {
        font-family: var(--kui-font-secondary);
        font-size: 32px;
        font-style: normal;
        font-weight: 700;
        line-height: 36px;
        color: var(--kui-white);

        @container (max-width: 410px) {
          font-size: 24px;
          line-height: 30px;
        }

        &-quote-left {
          margin-right: -12px;
          margin-top: -22px;
        }

        &-quote-right {
          margin-left: -12px;
          margin-bottom: -12px;
          transform: rotate(180deg);
        }
      }

      .lead {
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 26px;
        letter-spacing: 0.3px;
        color: var(--kui-white);
      }
    }

    .author {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      padding-bottom: 18px;
      column-gap: 8px;
      width: 100%;
      color: var(--kui-white);

      &-image {
        width: 64px;
        height: 64px;
        border-radius: 50%;
      }
    }
  }
}
