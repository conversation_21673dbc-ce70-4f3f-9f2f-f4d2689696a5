import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ArticleCard, BaseComponent, buildArticleUrl } from '@trendency/kesma-ui';
import { WhereTheBallWillBeCardTypes } from '../../definitions/where-the-ball-will-be.definitions';
import { RouterLink } from '@angular/router';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'app-where-the-ball-will-be',
  imports: [IconComponent, RouterLink],
  templateUrl: './where-the-ball-will-be.component.html',
  styleUrl: './where-the-ball-will-be.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WhereTheBallWillBeComponent extends BaseComponent<ArticleCard> {
  @HostBinding('class') hostClass: string;

  protected readonly WhereTheBallWillBeCardTypes = WhereTheBallWillBeCardTypes;

  _styleId: WhereTheBallWillBeCardTypes;
  get styleId(): WhereTheBallWillBeCardTypes {
    return this._styleId;
  }

  @Input() set styleId(styleId: WhereTheBallWillBeCardTypes) {
    this._styleId = styleId;
    this.hostClass = `style-${styleId}`;
  }

  articleLink: string[] | string = [];

  override setProperties(): void {
    super.setProperties();
    if (!this.data) {
      return;
    }
    this.articleLink = buildArticleUrl(this.data, undefined, true);
  }
}
