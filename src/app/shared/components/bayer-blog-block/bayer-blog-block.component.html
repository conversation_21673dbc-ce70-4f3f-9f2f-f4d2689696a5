@switch (styleID) {
  @case (BayerBlogBlockType.Block) {
    <mno-opinion-card
      [data]="data?.[0] ?? {}"
      [styleID]="OpinionCardType.AuthorOpinionHeader"
      [authorOverride]="author"
      [showOpinionLabels]="showOpinionLabels"
    ></mno-opinion-card>
    <div class="opinion-list">
      @for (opinion of data?.slice(1); track opinion?.title) {
        <mno-opinion-card [data]="opinion" [styleID]="OpinionCardType.SmallImgRightBorderBlack" [showOpinionLabels]="showOpinionLabels"></mno-opinion-card>
      }
    </div>
  }
  @case (BayerBlogBlockType.Row3Cols) {
    <ng-container [ngTemplateOutlet]="simpleHeader"></ng-container>
    <div class="opinion-list">
      @for (opinion of data ?? []; track opinion?.title) {
        <mno-opinion-card [data]="opinion" [styleID]="OpinionCardType.SmallImgRightSplitBorderBlack" [showOpinionLabels]="showOpinionLabels"></mno-opinion-card>
      }
    </div>
  }
  @case (BayerBlogBlockType.SmallColumnLeadUnder) {
    <ng-container [ngTemplateOutlet]="simpleHeader"></ng-container>
    <div class="opinion-list">
      @for (opinion of data ?? []; track opinion?.title) {
        <mno-opinion-card
          [class.in-sidebar]="isSidebar"
          [data]="opinion"
          [styleID]="OpinionCardType.SmallImgRightBorderBlack"
          [showOpinionLabels]="showOpinionLabels"
        ></mno-opinion-card>
      }
    </div>
  }
  @case (BayerBlogBlockType.SmallColumnSplit) {
    <ng-container [ngTemplateOutlet]="simpleHeader"></ng-container>
    <div class="opinion-list">
      @for (opinion of data ?? []; track opinion?.title) {
        <mno-opinion-card [data]="opinion" [styleID]="OpinionCardType.SmallImgRightSplitBorderBlack" [showOpinionLabels]="showOpinionLabels"></mno-opinion-card>
      }
    </div>
  }
}
<a [routerLink]="['/', 'rovat', 'bayer-zsolt-blogja']" class="btn btn-ghost btn-ghost-blue-transparent">Tovább a blog összes cikkéhez</a>

<ng-template #simpleHeader>
  <h3 class="blog-header d-flex">
    <hr />
    <mno-opinion-author [data]="author" [showName]="false" size="larger"></mno-opinion-author>
    {{ BlogTitle }}
    <hr />
  </h3>
</ng-template>
