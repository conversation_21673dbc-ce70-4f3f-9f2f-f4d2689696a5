@use 'shared' as *;

:host {
  display: block;

  .opinion {
    &-list {
      mno-opinion-card.opinion-card::ng-deep {
        padding-bottom: 16px;

        .opinion {
          &-author-name {
            display: none;
          }

          &-thumbnail {
            aspect-ratio: 1/1;
            max-width: 108px;
            max-height: 108px;
            object-fit: cover;
          }

          &-labels {
            gap: 0;

            .icon {
              width: 12px;
              height: 12px;
              min-width: 12px;
            }
          }

          &-title {
            font-size: 20px !important;
            font-weight: 400 !important;
            line-height: 26px !important; /* 130% */
          }

          &-lead {
            font-size: 16px;
            line-height: 20px; /* 125% */
          }
        }
      }
    }
  }

  &.style-SmallColumnLeadUnder {
    max-width: 300px;
    width: 100%;

    .opinion {
      &-list {
        display: flex;
        flex-direction: column;

        mno-opinion-card.in-sidebar.style-SmallImgRightBorderBlack::ng-deep {
          .opinion-label {
            white-space: unset;
          }
        }

        mno-opinion-card.opinion-card {
          &:last-of-type {
            border-bottom: 0 !important;
            margin-bottom: 0;
          }

          &::ng-deep {
            @include media-breakpoint-down(sm) {
              max-width: 300px;
            }
          }
        }
      }
    }
  }

  &.style-SmallColumnSplit {
    max-width: 624px;
    width: 100%;

    .opinion {
      &-list {
        display: flex;
        flex-direction: column;

        mno-opinion-card.opinion-card {
          &:last-of-type {
            border-bottom: 0 !important;
            margin-bottom: 0;
          }

          &::ng-deep {
            @include media-breakpoint-down(sm) {
              max-width: 624px;
            }
          }
        }
      }
    }
  }

  &.style-Block {
    .opinion-card.style-AuthorOpinionHeader::ng-deep {
      padding-top: 0;
      padding-bottom: 0;

      .opinion-thumbnail {
        margin-bottom: 0;
      }
    }

    mno-opinion-card.style-SmallImgRightBorderBlack::ng-deep {
      .opinion-container-top {
        @include media-breakpoint-between(md, lg) {
          flex-direction: column;
        }
      }
    }

    .opinion-list {
      grid-template-columns: 1fr 1fr 1fr 1fr;
      @include media-breakpoint-down(md) {
        grid-template-columns: 1fr 1fr;
      }
      @include media-breakpoint-down(sm) {
        display: flex;
        flex-direction: column;
      }

      mno-opinion-card.opinion-card {
        margin-bottom: 0;
        @include media-breakpoint-up(md) {
          border-bottom: 0 !important;
        }
      }
    }
  }

  &.style-Row3Cols {
    .opinion-list {
      grid-template-columns: 1fr 1fr 1fr;
      @include media-breakpoint-down(md) {
        grid-template-columns: 1fr 1fr;
      }
      @include media-breakpoint-down(sm) {
        display: flex;
        flex-direction: column;
      }

      mno-opinion-card.opinion-card {
        margin-bottom: 0;
        @include media-breakpoint-up(md) {
          border-bottom: 0 !important;
        }
      }
    }
  }

  &.style-SmallColumnLeadUnder,
  &.style-SmallColumnSplit {
    @include media-breakpoint-down(sm) {
      max-width: 100%;
      .opinion-list {
        mno-opinion-card::ng-deep {
          max-width: calc(100% + 32px) !important;
        }
      }
    }
  }

  &.narrow-view {
    mno-opinion-card.style-SmallImgRightBorderBlack::ng-deep {
      .opinion-container-top {
        @media screen and (min-width: 993px) and (max-width: 1245px) {
          flex-direction: column;
        }
      }
    }
  }
}

.opinion-list {
  display: grid;
  grid-row-gap: 16px;

  .opinion-card::ng-deep {
    padding-top: 0;
    border-right: 1px solid var(--kui-blue-500) !important;
    border-bottom: 1px solid var(--kui-blue-500) !important;
  }

  mno-opinion-card::ng-deep {
    @include media-breakpoint-down(sm) {
      width: 100%;
      margin-inline: 0;
    }
  }
}

.blog-header {
  color: var(--kui-blue-500);

  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: 32px; /* 133.333% */
  letter-spacing: 0.24px;
  align-items: center;
  justify-content: stretch;
  padding: 8px;
  gap: 16px;
  margin-bottom: 16px;

  @include media-breakpoint-down(md) {
    padding: 8px 0;
  }

  hr {
    flex: 1;
    height: 0;
    border-top: 1px solid var(--kui-blue-500);
    border-bottom: 0;
  }
}

.btn {
  font-size: 16px;
  font-weight: 700;
  line-height: 21px; /* 131.25% */
  padding: 10px;
  width: 100%;

  @include media-breakpoint-down(sm) {
    :root.style-SmallColumnLeadUnder &,
    :root.style-SmallColumnSplit & {
      margin-left: 0;
      margin-right: 0;
      width: 100%;
    }
    margin-top: -1px;
  }
}

.opinion-card.style-AuthorOpinionHeader::ng-deep {
  @include media-breakpoint-down(sm) {
    margin-bottom: 8px;
    padding-bottom: 0;
  }
}
