import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostBinding, inject, Input } from '@angular/core';
import { ArticleCard, Author, BaseComponent } from '@trendency/kesma-ui';
import { BAYER_BLOG_TITLE, BAYER_ZSOLT_AUTHOR_SLUG, BayerBlogBlockType, OpinionCardType } from '../../definitions';
import { OpinionAuthorComponent } from '../opinion-author/opinion-author.component';
import { RouterLink } from '@angular/router';
import { NgTemplateOutlet } from '@angular/common';
import { OpinionCardComponent } from '../opinion-card/opinion-card.component';

@Component({
  selector: 'mno-bayer-blog-block',
  templateUrl: './bayer-blog-block.component.html',
  styleUrl: './bayer-blog-block.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [OpinionCardComponent, NgTemplateOutlet, RouterLink, OpinionAuthorComponent],
})
export class BayerBlogBlockComponent extends BaseComponent<ArticleCard[]> {
  @Input() isSidebar = false;
  @Input() desktopWidth = 12;
  @Input() authorData?: Author;
  @HostBinding('class') hostClass = 'bayer-blog-block';
  @Input() showOpinionLabels = true;
  readonly BayerBlogBlockType = BayerBlogBlockType;
  readonly OpinionCardType = OpinionCardType;
  readonly BlogTitle = BAYER_BLOG_TITLE;
  author?: Author;
  readonly cd = inject(ChangeDetectorRef);
  #isMobile: boolean | null = false;
  #styleID?: BayerBlogBlockType;

  get styleID(): BayerBlogBlockType {
    return this.#styleID as BayerBlogBlockType;
  }

  @Input()
  set styleID(styleID: BayerBlogBlockType) {
    this.#styleID = styleID;
    this.setProperties();
  }

  get isMobile(): boolean {
    return !!this.#isMobile;
  }

  @Input() set isMobile(isMobile: boolean | null) {
    this.#isMobile = isMobile;
    this.setProperties();
  }

  get narrowView(): boolean {
    return this.isSidebar || this.desktopWidth < 5;
  }

  override setProperties(): void {
    super.setProperties();

    if (this.data?.length) {
      this.author = this.data.filter((article) => article?.author).find(({ author }) => author?.slug === BAYER_ZSOLT_AUTHOR_SLUG)?.author;
      if (!this.author?.slug) {
        this.author = { ...this.authorData } as Author;
      }
    }

    if (!this.data) {
      this.setData([]);
      return;
    }

    const data = this.data
      .filter((o) => !!o?.title)
      .map((opinion) => ({
        ...opinion,
        tags: opinion?.tags?.slice(0, 1) ?? [],
      }));
    if (data.length) {
      data[0] = { ...(data[0] ?? {}), columnTitle: this.BlogTitle };
    }
    this.setData(data);
    this.setHostClass();
  }

  private setHostClass(): void {
    this.hostClass = ['bayer-blog-block', `style-${BayerBlogBlockType[this.styleID]}`, this.narrowView && 'narrow-view'].join(' ');
  }
}
