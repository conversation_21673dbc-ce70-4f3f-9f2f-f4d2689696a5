import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { BlockTitleRowComponent as KesmaBlockTitleRowComponent } from '@trendency/kesma-ui';
import { BlockTitleRowType } from '../../definitions';
import { IconComponent } from '../icon/icon.component';
import { RouterLink } from '@angular/router';
import { NgIf, NgSwitch, NgSwitchCase, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'mno-block-title-row',
  templateUrl: './block-title-row.component.html',
  styleUrls: ['./block-title-row.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgTemplateOutlet, RouterLink, IconComponent, NgSwitch, NgSwitchCase],
})
export class BlockTitleRowComponent extends KesmaBlockTitleRowComponent {
  /**
   * Heading level to use for the title.
   * Ranges from 1 to 4.
   * @default 2
   * @type {number}
   */
  @Input() headingLevel = 2;

  @Input() isOnlyUrl = false;
  @Input() override isExternal = false;
  @Input()
  @HostBinding('class.is-sidebar')
  isSidebar = false;
  @Input()
  @HostBinding('class.is-opinion')
  isOpinion = false;
  @Input()
  @HostBinding('class.is-column')
  isColumn = false;
  @Input()
  @HostBinding('class.is-layout')
  isLayout = false;
  @Input() showLink = true;

  @Input() set styleID(value: BlockTitleRowType) {
    this.#styleID = value;
    this.#updateHostClass();
  }

  get styleID(): BlockTitleRowType {
    return this.#styleID;
  }

  #styleID: BlockTitleRowType = BlockTitleRowType.Default;

  @HostBinding('class') hostClass = '';

  override setProperties(): void {
    super.setProperties();
  }

  #updateHostClass(): void {
    this.hostClass = `style-${BlockTitleRowType[this.#styleID]}`;
  }
}
