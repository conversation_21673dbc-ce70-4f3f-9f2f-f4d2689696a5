<ng-container *ngIf="!data?.url; else content">
  <ng-container *ngTemplateOutlet="heading"></ng-container>
</ng-container>

<ng-template #content>
  <ng-container *ngIf="data?.urlName && showLink; else linkWithoutUrlName">
    <ng-container *ngTemplateOutlet="linkWithUrlName"></ng-container>
  </ng-container>

  <ng-template #linkWithoutUrlName>
    <ng-container *ngTemplateOutlet="withUrl; context: { $implicit: true }"></ng-container>
  </ng-template>
</ng-template>

<ng-template #linkWithUrlName>
  <div [class.only-url]="isOnlyUrl" class="block-title-container">
    <ng-container *ngTemplateOutlet="heading"></ng-container>
    <ng-container *ngIf="isOnlyUrl; else withUrl">
      <ng-container *ngTemplateOutlet="withOnlyUrl; context: { $implicit: false }"></ng-container>
    </ng-container>
  </div>
</ng-template>

<ng-template #withUrl let-isTitle>
  <a *ngIf="!isExternal; else isExternalTemplate" [routerLink]="data?.url" class="block-title-link">
    <ng-container *ngTemplateOutlet="linkContent; context: { $implicit: isTitle }"></ng-container>
  </a>

  <ng-template #isExternalTemplate>
    <ng-container *ngTemplateOutlet="externalLink; context: { $implicit: isTitle }"></ng-container>
  </ng-template>
</ng-template>

<ng-template #externalLink let-isTitle>
  <a [href]="data?.url" class="block-title-link" target="_blank">
    <ng-container *ngTemplateOutlet="linkContent; context: { $implicit: isTitle }"></ng-container>
  </a>
</ng-template>

<ng-template #linkContent let-isTitle>
  <ng-container *ngIf="isTitle; else urlName">
    <ng-container *ngTemplateOutlet="heading"></ng-container>
  </ng-container>
  <ng-template #urlName> {{ data?.urlName }} <img alt="Nyíl ikon" mno-icon="chevron-right" /></ng-template>
</ng-template>

<ng-template #heading>
  <ng-container [ngSwitch]="headingLevel">
    <h1 *ngSwitchCase="1" class="block-title">
      {{ data?.text }}
    </h1>
    <h2 *ngSwitchCase="2" class="block-title">
      {{ data?.text }}
    </h2>
    <h3 *ngSwitchCase="3" class="block-title">
      {{ data?.text }}
    </h3>
    <h4 *ngSwitchCase="4" class="block-title">
      {{ data?.text }}
    </h4>
  </ng-container>
</ng-template>

<!-- Only URL after components -->

<ng-template #withOnlyUrl let-isTitle>
  <a *ngIf="!isExternal; else isExternalTemplateOnlyUrl" [routerLink]="data?.url" class="block-title-link-only-url">
    <ng-container *ngTemplateOutlet="linkContentOnlyUrl; context: { $implicit: isTitle }"></ng-container>
  </a>

  <ng-template #isExternalTemplateOnlyUrl>
    <ng-container *ngTemplateOutlet="externalLinkOnlyUrl; context: { $implicit: isTitle }"></ng-container>
  </ng-template>
</ng-template>

<ng-template #externalLinkOnlyUrl let-isTitle>
  <a [href]="data?.url" class="block-title-link-only-url" target="_blank">
    <ng-container *ngTemplateOutlet="linkContentOnlyUrl; context: { $implicit: isTitle }"></ng-container>
  </a>
</ng-template>

<ng-template #linkContentOnlyUrl let-isTitle> {{ data?.urlName }}<img alt="Nyíl ikon" color="dark" mno-icon="chevron-right" /></ng-template>
