@use 'shared' as *;

:host {
  display: flex;
  justify-content: stretch;
  align-items: center;
  width: 100%;

  &.is-opinion {
    .block-title {
      color: var(--kui-blue-500);

      &-link {
        color: var(--kui-blue-500);

        &:hover {
          color: var(--kui-blue-900);
        }
      }
    }
  }

  &.is-column {
    .block-title {
      font-size: 20px;
      line-height: 26px;
      letter-spacing: 0.015em;
      color: var(--kui-blue-900);
    }
  }

  &.is-layout {
    .block-title {
      font-size: 24px;
      line-height: 28px;
      letter-spacing: 0.015em;
      color: var(--kui-blue-900);
      margin-bottom: 24px;
    }

    &.is-sidebar {
      margin-bottom: 0;
      font-size: 20px;
      font-weight: 700;
      line-height: 26px; /* 130% */
      letter-spacing: 0.3px;
    }
  }

  &.style-Opinion {
    .block-title,
    .block-title-link {
      color: var(--kui-blue-500);
      @include transition;
    }

    .block-title-link {
      &:hover {
        color: var(--kui-blue-700);
        @include transition;
      }
    }
  }
}

.block-title {
  font-size: 16px;
  font-weight: 700;
  line-height: 21px;

  &-container {
    display: flex;
    flex: 1;
    justify-content: space-between;
    align-items: center;
  }

  &-link {
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
    color: var(--kui-slate-950);

    &:hover {
      color: var(--kui-blue-700);
    }
  }

  &-link-only-url {
  }
}
