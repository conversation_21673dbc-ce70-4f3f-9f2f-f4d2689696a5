@use 'shared' as *;

@mixin narrow {
  .poll {
    &-header {
      &-title {
        text-align: center !important;
        flex-direction: column;

        &-text {
          order: 2;
        }
      }
    }

    &-sponsor-logo {
      order: 1;
      margin: -10px 0 -4px;
      min-height: 52px;
      max-height: 52px;
    }
  }
}

:host {
  display: block;
  width: 100%;

  &.poll-sponsored {
    .poll {
      &-header {
        &-title {
          text-align: left;
          display: flex;
          justify-content: space-between;
        }
      }
    }
  }

  &.in-sidebar {
    @include narrow;
  }

  @include media-breakpoint-down(sm) {
    @include narrow;
  }
}

.poll {
  display: flex;
  flex-direction: column;
  background: var(--kui-white);

  &-sponsor-logo {
    max-height: 44px;
    width: 100%;
    height: 100%;
    object-fit: contain;
    min-height: 44px;
    margin-top: -12px;
    margin-bottom: -12px;
  }

  &-header {
    &-title {
      color: var(--kui-white);
      background-color: var(--kui-blue-700);
      font-weight: 700;
      font-size: 16px;
      line-height: 21px;
      text-align: center;
      padding: 9px 16px;
    }

    &-question {
      color: var(--kui-blue-800);
      font-size: 16px;
      font-weight: 700;
      line-height: 21px;
      padding: 16px 24px 8px;
    }
  }

  &-form {
    &-radio {
      position: relative;
      cursor: pointer;
      user-select: none;
      padding: 8px 24px 8px 48px;

      &:hover:not(.results),
      &.active:not(.results) {
        cursor: pointer;
        background: var(--kui-blue-500);

        .poll-form-radio-label {
          color: var(--kui-white);
        }
      }

      &-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      &-label {
        cursor: pointer;
        display: inline-block;
        font-size: 16px;
        font-weight: 700;
        line-height: 21px;
        color: var(--kui-slate-900-o80);
      }

      &-input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
      }

      &-progress-bar {
        height: 3px;
        background-color: var(--kui-blue-700);
        &-wrapper {
          width: 100%;
          background: var(--kui-blue-100);
          height: 4px;
          border-radius: 2px;
          margin: 10px 0 12px;
        }
      }

      &.results {
        padding: 8px 24px;

        &,
        &-result,
        .poll-form-radio-label {
          color: var(--kui-shark-950);
          font-weight: 700;
          font-size: 16px;
          line-height: 21px;
        }
      }
    }

    &-checkmark {
      position: absolute;
      top: calc(50% - 4px);
      height: 8px;
      width: 8px;
      border: 2px solid var(--kui-blue-500);
      left: 28px;
      cursor: pointer;

      &:after {
        content: '';
        position: absolute;
        display: none;
        cursor: pointer;
      }
    }

    &-radio:hover input ~ .poll-form-checkmark {
      background-color: var(--kui-white);
    }

    &-radio input:checked ~ .poll-form-checkmark:after {
      display: block;
    }

    &-radio .poll-form-checkmark:after {
      top: calc(50% - 4px);
      left: -2px;
      width: 8px;
      height: 8px;
      border: 2px solid var(--kui-blue-500);
      background-color: var(--kui-white);
    }

    &-buttons {
      margin-top: 10px;
      display: flex;
      flex-direction: column;

      &-submit {
        padding: 10px 20px;
        font-size: 16px;
        font-weight: 700;
        line-height: 21px;
        text-align: center;

        &:hover {
        }

        &.disabled {
          cursor: not-allowed;
          opacity: 0.8;
        }

        &.voted {
          cursor: not-allowed;
          @include GhostButtonHighlight(var(--kui-blue-500), var(--kui-white));
          opacity: 0.7;
        }
      }
    }
  }
}
