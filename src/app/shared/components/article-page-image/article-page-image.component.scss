:host {
  display: block;
}

.article-page-image {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: stretch;
  justify-content: flex-start;

  &-caption {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0;
    color: var(--kui-blue-900);
    border-left: 1px solid var(--kui-blue-900);
    padding-left: 8px;
  }

  img {
    display: block;
    object-fit: contain;
    width: 100%;
    max-width: 100%;
  }
}
