import { Component, ChangeDetectionStrategy, Input } from '@angular/core';
import { ArticlePageImageComponent as KesmaArticlePageImageComponent, FocusPointDirective } from '@trendency/kesma-ui';
import { NgIf } from '@angular/common';

@Component({
  selector: 'mno-article-page-image',
  templateUrl: 'article-page-image.component.html',
  styleUrls: ['./article-page-image.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FocusPointDirective, NgIf],
})
export class ArticlePageImageComponent extends KesmaArticlePageImageComponent {
  @Input() showCaption = true;
}
