<figure class="article-page-image">
  <img
    withFocusPoint
    [data]="data?.thumbnailFocusedImages"
    [alt]="data?.thumbnailInfo?.altText ?? ''"
    [displayedAspectRatio]="{ desktop: '16:9' }"
    [displayedUrl]="data?.thumbnail"
    loading="lazy"
  />
  <figcaption *ngIf="showCaption" class="article-page-image-caption">
    {{ data?.thumbnailInfo?.caption }}
    <ng-container *ngIf="data?.thumbnailInfo?.photographer"> Fotó: {{ data?.thumbnailInfo?.photographer }}</ng-container>
    <ng-container *ngIf="data?.thumbnailInfo?.source"> Forrás: {{ data?.thumbnailInfo?.source }}</ng-container>
  </figcaption>
</figure>
