@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  padding: 42px 0;
}

h1 {
  font-family: var(--kui-font-secondary);
  font-size: 40px;
  font-weight: 700;
  line-height: 52px;
  letter-spacing: 0;
  text-align: center;
}

h2 {
  font-family: var(--kui-font-secondary);
  font-size: 24px;
  font-weight: 700;
  line-height: 30px;
  letter-spacing: 0;
  text-align: center;

  & + p {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    letter-spacing: 0;
    text-align: center;
  }
}

.wrapper {
  gap: 32px;
}

mno-block-title {
  margin: 24px;
}

.error-bottom {
  max-width: 840px;
  width: 100%;
  margin: 0 auto;

  .article-list {
    margin-top: 24px;
    display: grid;
    gap: 24px;
    grid-template-columns: 1fr 1fr 1fr 1fr;

    [mno-article-card] {
      width: calc(25% - 12px);
    }

    @include media-breakpoint-down(md) {
      grid-template-columns: 1fr 1fr;
      [mno-article-card] {
        width: calc(50% - 12px);
      }
    }

    @include media-breakpoint-down(sm) {
      grid-template-columns: 1fr;
      [mno-article-card] {
        width: 100%;
      }
    }
  }
}
