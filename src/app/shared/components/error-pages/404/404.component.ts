import { ChangeDetectionStrategy, Component, Inject, OnInit, Optional } from '@angular/core';
import { RESPONSE, SeoService, UtilService } from '@trendency/kesma-core';
import { ArticleCard, BlockTitle } from '@trendency/kesma-ui';
import { Subject } from 'rxjs';
import { defaultMetaInfo } from '../../../constants';
import type { Response } from 'express';
import { ArticleService } from 'src/app/shared/services/article.service';
import { BlockTitleRowComponent } from '../../block-title-row/block-title-row.component';
import { ArticleCardComponent } from '../../article-card/article-card.component';
import { AsyncPipe, NgFor, NgIf } from '@angular/common';
import { ArticleCardType } from 'src/app/shared/definitions';

@Component({
  selector: 'app-404',
  templateUrl: './404.component.html',
  styleUrls: ['./404.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleCardComponent, BlockTitleRowComponent, NgIf, NgFor, AsyncPipe],
})
export class Error404Component implements OnInit {
  readonly ArticleCardType = ArticleCardType;

  titleData: BlockTitle = {
    text: 'Címoldalról ajánljuk',
    url: '/',
    urlName: 'Tovább a címoldalra',
  };
  articles$ = new Subject<ArticleCard[]>();

  constructor(
    private readonly seo: SeoService,
    private readonly utilsService: UtilService,
    private readonly articleService: ArticleService,
    @Inject(RESPONSE) @Optional() private readonly response: Response
  ) {}

  ngOnInit(): void {
    this.articleService.getFreshNews('', undefined, undefined, undefined, ['article'], undefined, 0, 4).subscribe(({ data: articles }) => {
      this.articles$.next(
        (articles ?? []).map(
          (article) =>
            ({
              ...article,
              thumbnail: { url: article.thumbnail },
            }) as unknown as ArticleCard
        )
      );
    });

    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: `404 - ${defaultMetaInfo.ogTitle}`,
      ogTitle: `404 - ${defaultMetaInfo.ogTitle}`,
      robots: 'noindex',
    });
    if (!this.utilsService.isBrowser() && this.response) {
      this.response.status(404);
    }
  }
}
