import { NgIf } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, ViewChild } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { UtilService } from '@trendency/kesma-core';

@Component({
  selector: 'app-nativterelo',
  templateUrl: './nativterelo.component.html',
  styleUrls: ['./nativterelo.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf],
})
export class NativtereloComponent implements AfterViewInit {
  tereloUrl: SafeResourceUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
    'https://terelo.mediaworks.hu/nativterelo/nativterelo.html?utmSource=magyarnemzet.hu' +
      '&traffickingPlatforms=Magyarnemzet%20Nat%C3%ADv' +
      '&domain=Magyar%20Nemzet'
  );
  tereloUrlNemNativ: SafeResourceUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
    'https://terelo.mediaworks.hu/nativterelo/nativterelo.html?utmSource=magyarnemzet.hu' + '&traffickingPlatforms=Magyar%20Nemzet' + '&domain=Magyar%20Nemzet'
  );

  @ViewChild('nativterelo') readonly nativterelo: ElementRef<HTMLIFrameElement>;

  constructor(
    private readonly sanitizer: DomSanitizer,
    private readonly utilsService: UtilService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  public showIframe: boolean = true;

  ngAfterViewInit(): void {
    if (this.utilsService.isBrowser()) {
      window.addEventListener('message', (event) => {
        if (event.origin.indexOf('terelo.mediaworks.hu') !== -1 && event?.data?.nativTereloFailed === true) {
          this.showIframe = false;
          this.changeRef.markForCheck();
        }
      });
    }
  }

  isBrowser(): boolean {
    return this.utilsService.isBrowser();
  }
}
