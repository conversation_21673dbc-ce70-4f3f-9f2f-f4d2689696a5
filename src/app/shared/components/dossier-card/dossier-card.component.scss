@use 'shared' as *;

:host {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  gap: 16px;
  border-left: 1px solid var(--kui-slate-950);
  border-right: 1px solid var(--kui-slate-950);
  border-bottom: 1px solid var(--kui-slate-950);

  @include media-breakpoint-down(sm) {
    border-left: 0;
    border-right: 0;
    .dossier-highlighted,
    .dossier-rest {
      width: 100%;
      flex-direction: column;
      margin-inline: 0;
    }
  }

  > a {
    width: calc(100% + 2px);
    margin: 0 -1px -1px -1px;
  }

  &.style-articleRecommendationDossier {
    .dossier {
      &-rest {
        display: grid;
        flex-direction: row;
        grid-template-columns: 1fr 1fr 1fr;
        margin: 0 36px 8px;
        gap: 24px;

        [mno-article-card] {
          border-bottom: 0;
        }

        @include media-breakpoint-down(sm) {
          flex-direction: column;
          grid-template-columns: 1fr;
        }
      }
    }
  }

  &.style-recommendationDossier {
    flex-direction: column;
    border-left: 0;
    padding: 8px 18px 8px 0;
    @include transition;
    @include media-breakpoint-down(sm) {
      border-right: 1px solid var(--kui-slate-950);
    }

    &:hover {
      .dossier {
        &-title,
        &-lead {
          color: var(--kui-blue-700);
          @include transition;
        }

        &-content-badge {
          @include transition;
          background: var(--kui-blue-500-o80);
        }

        &-thumbnail {
          transform: scale(110%);
          @include transition;
        }
      }

      padding: 8px 17px 7px 0;
      border-right: 2px solid var(--kui-blue-700);
      border-bottom: 2px solid var(--kui-blue-700);
    }

    .dossier {
      &-container {
        position: relative;
        @include media-breakpoint-down(sm) {
          flex-direction: column;
        }
      }

      &-link-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;
      }

      &-title {
        @include transition;
        color: var(--kui-slate-950);
        font-size: 16px;
        font-weight: 700;
        line-height: 21px;
        text-align: left;
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 8px;

        &-first {
          border-bottom: 0;
          margin: 0 0 16px;
          font-size: 18px;
          line-height: 24px;
        }
      }

      &-lead {
        @include transition;
        margin-bottom: 16px;
      }

      &-article-list {
        display: flex;
        flex-direction: column;
        gap: 16px;

        > a {
          display: flex;
          flex-direction: column;
          align-items: stretch;
          justify-content: flex-start;
          gap: 16px;
          @include media-breakpoint-down(sm) {
            margin-bottom: 16px;
          }
        }
      }

      &-thumbnail {
        transform: scale(100%);
        object-fit: cover;
        width: 100%;
        @include transition;

        &-container {
          height: 175px;
          width: 312px;
          min-height: 175px;
          min-width: 312px;
          overflow: hidden;
          @include media-breakpoint-down(sm) {
            min-width: 100%;
            width: 100%;
          }
        }
      }

      &-content-badge {
        @include transition;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;
        background: var(--kui-blue-950-o80);
        position: absolute;
        right: 0;
        top: 131px;
        padding: 10px 12px;
        color: var(--kui-white);
      }
    }
  }

  &.style-leadDossierCard {
    padding-left: 0;
    padding-top: 0;
    padding-right: 16px;
    border-right: 1px solid var(--kui-slate-950);
    border-bottom: 1px solid var(--kui-slate-950);
    border-left: 0;
    margin-bottom: 24px;

    > a > [mno-article-card],
    > [mno-article-card] {
      width: 100%;

      &::ng-deep {
        .article {
          width: 100%;

          &-thumbnail-figure {
            margin-bottom: 16px;
          }

          &-labels {
            margin-bottom: 2px;
          }

          &-title {
            font-family: var(--kui-font-secondary);
            font-size: 36px;
            font-weight: 700;
            line-height: 44px; /* 122.222% */

            @include media-breakpoint-down(xs) {
              font-size: 26px;
            }
          }

          &-lead {
            display: none;
          }
        }
      }
    }

    .dossier {
      &-article-list {
        margin-top: -8px;

        li {
          list-style-position: inside;
          margin: 12px 0;
          background: url('/assets/images/icons/square-small-dark.svg') no-repeat left top;
          padding: 0 0 0 32px;
          list-style-type: none;

          [mno-article-card]::ng-deep {
            .article-title {
              font-size: 20px;
              font-weight: 500;
              line-height: 26px;
              letter-spacing: 0.015em;
            }
          }
        }
      }
    }
  }

  &.style-newsFeed {
    padding: 16px;
    border: 0;

    margin-bottom: 24px;
    background-color: var(--kui-slate-100);

    > a {
      & > [mno-article-card]::ng-deep {
        .article {
          &-thumbnail-figure {
            margin-bottom: 16px;
          }

          &-labels {
            margin-bottom: 2px;
          }

          &-title {
            font-family: var(--kui-font-secondary);
            font-size: 36px;
            font-weight: 700;
            line-height: 44px; /* 122.222% */

            @include media-breakpoint-down(xs) {
              font-size: 26px;
            }
          }

          &-lead {
            font-size: 20px;
            font-weight: 400;
            line-height: 24px; /* 120% */
            margin-bottom: 16px;
          }
        }
      }

      &:hover {
        [mno-article-card]::ng-deep {
          .article-title,
          .article-lead {
            color: var(--kui-blue-700) !important;
            @include transition;
          }
        }
      }
    }

    .dossier {
      &-article-list {
        margin-top: -8px;

        li {
          list-style-position: inside;
          margin: 12px 0;
          background: url('/assets/images/icons/square-small-blue.svg') no-repeat left top;
          padding: 0 0 0 32px;
          list-style-type: none;

          [mno-article-card]::ng-deep {
            .article-title {
              font-size: 20px;
              font-weight: 700;
              line-height: 26px; /* 130% */
              letter-spacing: 0.3px;
            }
          }
        }
      }
    }
  }

  &.style-minuteByMinute {
    padding: 16px;
    border: 0;

    margin-bottom: 24px;
    background-color: var(--kui-blue-950);

    > a {
      & > [mno-article-card]::ng-deep {
        .article {
          &-thumbnail-figure {
            margin-bottom: 16px;
          }

          &-labels {
            margin-bottom: 16px;
          }

          &-label {
            color: var(--kui-white);
          }

          &-title {
            color: var(--kui-white);
            font-family: var(--kui-font-secondary);
            font-size: 36px;
            font-weight: 700;
            line-height: 44px; /* 122.222% */

            @include media-breakpoint-down(xs) {
              font-size: 26px;
            }
          }

          &-lead {
            color: var(--kui-white);
            font-size: 20px;
            font-weight: 400;
            line-height: 24px; /* 120% */
            margin-bottom: 16px;
          }

          &-content-counter {
            color: var(--kui-blue-500) !important;
          }
        }
      }

      &:hover {
        [mno-article-card]::ng-deep {
          .article-title,
          .article-lead {
            color: var(--kui-blue-500) !important;
            @include transition;
          }
        }
      }
    }

    .dossier {
      &-article-list {
        margin-top: -8px;

        li {
          list-style-position: inside;
          margin: 12px 0;
          background: url('/assets/images/icons/square-small-blue.svg') no-repeat left top;
          padding: 0 0 0 32px;
          list-style-type: none;
        }
      }
    }
  }
}

h3 {
  flex-basis: 100%;
  width: 100%;
  min-width: 100%;
  flex-shrink: 0;
  margin-bottom: 24px;
  text-align: center;
  font-size: 24px;
  line-height: 36px;
  margin-top: -24px;
  border-bottom: 1px solid var(--kui-slate-950);
}

.dossier {
  &-title-label {
    display: inline-block;
    background-color: var(--kui-white);
    top: 18px;
    position: relative;
    padding: 0 16px;
    flex: 1 0 auto;
  }

  &-highlighted,
  &-rest {
    width: calc(50% - 72px);
    // flex-basis: 50%;
  }

  &-highlighted {
    margin: 0 0 12px 36px;
  }

  &-rest {
    margin: 0 36px 12px 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
    flex-grow: 1;
    justify-content: flex-start;

    article {
      border-bottom: 1px dotted var(--kui-slate-300);
      padding-bottom: 16px;

      &:last-child {
        border-bottom: 0;
        padding-bottom: 0;
      }
    }
  }
}
