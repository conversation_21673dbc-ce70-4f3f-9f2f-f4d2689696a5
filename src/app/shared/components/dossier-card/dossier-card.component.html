@switch (styleID) {
  @case (DossierCardTypes.mainArticleDossier) {
    <h3>
      <span class="dossier-title-label">{{ dossierData?.title }}</span>
    </h3>
    <div class="dossier-highlighted">
      <article [data]="dossierData?.mainArticle" [styleID]="ArticleCardType.Img4TopTitleLeadBadgeLarge" mno-article-card></article>
    </div>
    <div class="dossier-rest">
      @for (article of articles | slice: 0 : maxArticleCount; track article.id || article.title) {
        <article
          [data]="article"
          [showThumbnail]="$index < 2"
          [styleID]="ArticleCardType.ImgRightTagsTitleLeadWide"
          [showArticleLabels]="showArticleLabels"
          mno-article-card
        ></article>
      }
    </div>
    <a [routerLink]="['/', 'dosszie', data?.slug]" class="btn btn-ghost btn-ghost-dark-transparent">
      Tovább a dosszié összes cikkéhez <i class="icon-default" mno-icon="chevron-right"></i>
    </a>
  }
  @case (DossierCardTypes.articleRecommendationDossier) {
    <h3>
      <span class="dossier-title-label">{{ dossierData?.title }}</span>
    </h3>
    <div class="dossier-rest">
      <article [data]="dossierData?.mainArticle" [styleID]="ArticleCardType.Img16TopTagsTitleLeadBadge" mno-article-card></article>
      @for (article of articles | slice: 0 : 2; track article.id || article.title) {
        <article [data]="article" [styleID]="ArticleCardType.Img16TopTagsTitleLeadBadge" [showArticleLabels]="showArticleLabels" mno-article-card></article>
      }
    </div>
    <a [routerLink]="['/', 'dosszie', data?.slug]" class="btn btn-ghost btn-ghost-dark-transparent">
      Tovább a dosszié összes cikkéhez <i class="icon-default" mno-icon="chevron-right"></i>
    </a>
  }
  @case (DossierCardTypes.recommendationDossier) {
    <div class="dossier-link-wrapper">
      <a [routerLink]="['/', 'dosszie', dossierData?.slug]">
        <h2 class="dossier-title"><img [size]="16" alt="Dosszié" mno-icon="add-square" /> {{ dossierData?.title ?? dossierData?.mainArticle?.title }}</h2>
      </a>
      <div class="dossier-container d-flex align-items-stretch justify-content-between">
        <div class="dossier-article-list">
          @if (articles[0]?.slug && !hideMainArticle) {
            <a [routerLink]="firstArticleLink">
              <h3 class="dossier-title dossier-title-first">
                {{ articles[0].title }}
              </h3>
            </a>
          }
          @for (article of articles | slice: (hideMainArticle ? 0 : 1) : maxArticleCount; track article.id || article.title) {
            <article
              [data]="article"
              [showThumbnail]="false"
              [styleID]="ArticleCardType.NoImgTitleBadge"
              [showArticleLabels]="showArticleLabels"
              mno-article-card
            ></article>
          }
        </div>
        <a [routerLink]="['/', 'dosszie', dossierData?.slug]" class="dossier-thumbnail-container">
          <img
            withFocusPoint
            [data]="dossierData?.coverImgThumbnailUrlFocusedImages"
            [alt]="dossierData?.title || ''"
            [displayedAspectRatio]="{ desktop: '16:9' }"
            [displayedUrl]="basicDossier?.thumbnailUrl || basicDossier?.thumbnail || dossierData?.headerImage || defaultThumbnail"
            class="dossier-thumbnail"
            loading="lazy"
          />
        </a>
        @if (articlesCount || calculateArticleCount) {
          <a [routerLink]="['/', 'dosszie', dossierData?.slug]" class="dossier-content-badge">
            {{ articlesCount ?? $any(basicDossier).articleCount ?? articles.length }} cikk
            <img [size]="20" color="white" mno-icon="chevron-right" alt="" />
          </a>
        }
      </div>
    </div>
  }
  @case (DossierCardTypes.leadDossierCard) {
    <article [data]="coverArticle" [dossier]="dossierData" [hasLink]="false" [styleID]="ArticleCardType.Img16TopTagsTitleLeadBadge" mno-article-card></article>
    <ul class="dossier-article-list">
      @for (article of articles | slice: 0 : maxArticleCount; track article.id || article.title) {
        <li>
          <article
            [data]="article"
            [showThumbnail]="false"
            [styleID]="ArticleCardType.NoImgTitleBadge"
            [showArticleLabels]="showArticleLabels"
            mno-article-card
          ></article>
        </li>
      }
    </ul>
  }
  @case (DossierCardTypes.newsFeed) {
    <a [routerLink]="['/', 'hirfolyam', dossierData?.slug]" class="dossier-link-wrapper">
      <article [data]="articles[0]" [hasLink]="false" [isLive]="true" [styleID]="ArticleCardType.Img16TopTagsTitleLeadBadge" mno-article-card></article>
      <ul class="dossier-article-list">
        @for (article of articles | slice: 1 : 4; track article.id || article.title) {
          <li>
            <article
              [data]="article"
              [hasLink]="false"
              [showThumbnail]="false"
              [styleID]="ArticleCardType.NoImgTitleBadge"
              [showArticleLabels]="showArticleLabels"
              mno-article-card
            ></article>
          </li>
        }
      </ul>
    </a>
  }
  @case (DossierCardTypes.minuteByMinute) {
    <a [routerLink]="buildArticleUrl(minuteArticle)" class="dossier-link-wrapper">
      <article [data]="minuteArticle" [hasLink]="false" [isLive]="true" [styleID]="ArticleCardType.Img16TopTagsTitleLeadBadge" mno-article-card></article>
    </a>
  }
}
