import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostBinding, Input } from '@angular/core';
import {
  ArticleCard,
  BaseComponent,
  BasicDossier,
  basicDossierToDossierData,
  buildArticleUrl,
  DossierCard,
  DossierData,
  FocusPointDirective,
  newsFeedToDossierData,
} from '@trendency/kesma-ui';
import { ArticleCardType, DossierCardStyleTypes } from '../../definitions';
import { IconComponent } from '../icon/icon.component';
import { RouterLink } from '@angular/router';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { SlicePipe } from '@angular/common';
import { DEFAULT_THUMBNAIL } from '../../constants';

const DEFAULT_STYLE = DossierCardStyleTypes.mainArticleDossier;

@Component({
  selector: 'mno-dossier-card',
  templateUrl: './dossier-card.component.html',
  styleUrls: ['./dossier-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleCardComponent, RouterLink, IconComponent, FocusPointDirective, SlicePipe],
})
export class DossierCardComponent extends BaseComponent<DossierData<string | Date> | BasicDossier<string | Date> | ArticleCard | DossierCard> {
  articleLink: string[] | string = '';
  firstArticleLink: string[] | string = '';
  @Input() hideMainArticle = false;
  @Input() calculateArticleCount = false;
  @Input() articlesCount?: number;
  @Input() maxArticleCount = 5;
  @HostBinding('class') hostClass = `style-${DossierCardStyleTypes[DEFAULT_STYLE]}`;
  @Input() isMobile: boolean | null = false;
  @Input() showArticleLabels = true;
  tagLimit = 10;
  readonly ArticleCardType = ArticleCardType;
  readonly DossierCardTypes = DossierCardStyleTypes;
  dossierArticle?: ArticleCard;
  coverArticle?: ArticleCard;
  defaultThumbnail = DEFAULT_THUMBNAIL;
  #styleID: DossierCardStyleTypes = DossierCardStyleTypes.mainArticleDossier;

  readonly buildArticleUrl = buildArticleUrl;

  constructor(private readonly cd: ChangeDetectorRef) {
    super();
  }

  get styleID(): DossierCardStyleTypes {
    return this.#styleID;
  }

  @Input() set styleID(styleID: DossierCardStyleTypes) {
    this.#styleID = styleID;
    this.hostClass = `style-${DossierCardStyleTypes[this.#styleID]}`;
  }

  get dossierData(): DossierData<Date> {
    return this.data as DossierData<Date>;
  }

  get basicDossier(): BasicDossier {
    return this.data as BasicDossier;
  }

  get articles(): ArticleCard[] {
    return (this.dossierData?.secondaryArticles ?? (this.dossierData as BasicDossier)?.articles ?? []) as unknown as ArticleCard[];
  }

  get minuteArticle(): ArticleCard {
    return this.data as ArticleCard;
  }

  protected override setProperties(): void {
    const dossierData = this.data as DossierData;
    if (!dossierData.mainArticle || (dossierData.secondaryArticles && dossierData.secondaryArticles.some((dossier) => !dossier))) {
      return;
    }
    if (this.styleID === DossierCardStyleTypes.newsFeed) {
      this.setData(newsFeedToDossierData(this.data as DossierData));
    }
    if (this.dossierData && !this.dossierData.mainArticle) {
      this.setData(basicDossierToDossierData(this.basicDossier));
    }

    this.dossierArticle = {
      ...(this.articles?.[0] ?? {}),
      title: this.dossierData?.title ?? this.dossierData?.mainArticle?.title,
      lead: this.dossierData?.lead ?? this.dossierData?.mainArticle?.lead,
      tags: this.dossierData?.tag ? [this.dossierData.tag] : [],
      thumbnail: {
        url: this.dossierData?.headerImage ?? this.dossierData?.mainArticle?.thumbnailUrl,
      },
    };

    this.coverArticle = {
      ...(this.dossierData?.mainArticle ?? this.articles?.[0] ?? {}),
      title: this.dossierData?.mainArticle?.title ?? this.dossierData?.title,
      lead: this.dossierData?.mainArticle?.lead ?? this.dossierData?.lead,
      tags: [],
      thumbnail: {
        url: this.dossierData?.mainArticle?.thumbnailUrl ?? this.dossierData?.headerImage,
      },
    };
    this.firstArticleLink = this.dossierData?.mainArticle?.slug ? buildArticleUrl(this.dossierData?.mainArticle) : [];

    this.tagLimit = this.isMobile ? 1 : 10;

    this.cd.detectChanges();
  }
}
