import { ChangeDetectionStrategy, Component, EventEmitter, HostBinding, Input, OnInit, Output } from '@angular/core';
import { StaticMenuItem } from '../../definitions';
import { buildSocialMenu, DEFAULT_RSS_LINK, DEFAULT_SUBSCRIBE_LINK } from '../../utils';
import { SocialBlockComponent } from '../social-block/social-block.component';

@Component({
  selector: 'mno-promo-block',
  templateUrl: './promo-block.component.html',
  styleUrls: ['./promo-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SocialBlockComponent],
})
export class PromoBlockComponent implements OnInit {
  @Output() subscriptionClick = new EventEmitter<MouseEvent>();

  @Input() facebookLink = '';
  @Input() instagramLink = '';
  @Input() twitterLink = '';
  @Input() videaLink = '';
  @Input() youtubeLink = '';
  @Input() rssLink = DEFAULT_RSS_LINK;
  @Input()
  @HostBinding('class.is-wide')
  isWide = false;

  socialIcons: StaticMenuItem[] = [];
  subscribeLink = DEFAULT_SUBSCRIBE_LINK;

  ngOnInit(): void {
    this.socialIcons = buildSocialMenu(
      this.facebookLink,
      this.instagramLink,
      this.twitterLink,
      this.videaLink,
      this.youtubeLink,
      true,
      DEFAULT_RSS_LINK,
      DEFAULT_SUBSCRIBE_LINK,
      (event) => this.subscriptionClick.emit(event)
    );
  }
}
