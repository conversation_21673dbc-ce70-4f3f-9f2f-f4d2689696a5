@use 'shared' as *;

:host {
  display: block;
  max-width: 300px;

  gap: 8px;
  border: 1px solid var(--kui-blue-500);
  padding: 16px;
  border-radius: 2px;
  font-size: 14px;
  line-height: 18px;
  align-items: center;

  &.is-wide {
    max-width: 100%;
    display: flex;
  }

  @include media-breakpoint-down(sm) {
    max-width: 100%;
    min-width: 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: stretch;
  }
}

.promo-block-left {
  display: flex;
  flex-direction: column;
}

h3 {
  letter-spacing: 0;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 18px;
}

ul {
  margin-bottom: 12px;

  :host.is-wide & {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0 28px;
    margin-bottom: 0;
  }

  li {
    margin-bottom: 8px;
  }
}

mno-social-block {
  :host.is-wide & {
    width: 104px;
    flex-shrink: 0;
    @include media-breakpoint-down(sm) {
      width: auto;
      flex-shrink: 1;
    }
  }
}
