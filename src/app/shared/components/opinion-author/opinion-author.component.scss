@use 'shared' as *;

:host {
  overflow: hidden;

  &,
  .author-link {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
  }

  .avatar-wrapper {
    overflow: hidden;
    max-width: 40px;
    max-height: 40px;
    border-radius: 50%;
  }

  &:hover {
    .author-avatar {
      @include transition;
      transform: scale(110%);
      margin-bottom: 0;
      overflow: hidden;
      border-radius: 50%;
    }
  }

  &.is-medium {
    .avatar-wrapper {
      max-width: 40px !important;
      max-height: 40px !important;
    }

    .author-avatar {
      max-width: 40px !important;
    }

    .author-name {
      font-size: 24px !important;
      line-height: 32px !important;
      letter-spacing: 0.01em;
    }
  }

  &.is-larger {
    .avatar-wrapper {
      max-width: 72px !important;
      max-height: 72px !important;
    }

    .author-avatar {
      max-width: 72px !important;
    }

    .author-name {
      font-size: 24px !important;
      line-height: 32px !important;
      letter-spacing: 0.01em;
    }
  }

  &.is-large {
    .avatar-wrapper {
      max-width: 108px !important;
      max-height: 108px !important;
    }

    .author-avatar {
      max-width: 108px !important;
    }

    .author-name {
      font-size: 24px !important;
      line-height: 32px !important;
      letter-spacing: 0.01em;
    }
  }

  &.is-square {
    .author-avatar {
      border-radius: 2px;
    }
  }

  &.color-white {
    .author-name {
      color: var(--kui-white);
    }
  }
}

.author {
  &-avatar {
    border-radius: 50%;
    object-fit: cover;
    width: 100%;
    max-width: 32px;
    aspect-ratio: 1/1;
    background-color: var(--kui-gray-500);

    &-default {
      border: 1px solid var(--kui-slate-50-o65);
    }

    @include transition;
  }

  &-name {
    color: var(--kui-blue-500);
    font-size: 20px;
    font-weight: 500;
    line-height: 26px;
    letter-spacing: 0.015em;
  }
}
