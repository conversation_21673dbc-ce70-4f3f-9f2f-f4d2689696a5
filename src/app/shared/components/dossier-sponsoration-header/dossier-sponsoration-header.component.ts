import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterLink } from '@angular/router';
import { BaseComponent, IconComponent, DossierData } from '@trendency/kesma-ui';

@Component({
  selector: 'app-dossier-sponsoration-header',
  templateUrl: './dossier-sponsoration-header.component.html',
  styleUrls: ['./dossier-sponsoration-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent, RouterLink],
})
export class DossierSponsorationHeaderComponent extends BaseComponent<DossierData> {
  get moreButtonLabel(): string {
    return this.data?.overwriteMoreButtonLabel?.length ? this.data?.overwriteMoreButtonLabel : 'A dosszié további hírei';
  }
}
