@use 'shared' as *;

:host {
  --kui-recommender-border-color: var(--kui-white);
  --kui-recommender-bg-color: var(--kui-slate-950);
  --kui-recommender-fg-color: var(--kui-white);

  display: flex;

  background: var(--kui-recommender-bg-color);

  @include media-breakpoint-down(sm) {
    @include layoutFullWidth;
  }

  .articles {
    display: flex;
    flex-direction: column;
    gap: 24px;

    @include media-breakpoint-down(sm) {
      gap: 16px;
    }
  }

  .wrapper {
    display: flex;
    flex-wrap: wrap;
    max-width: $layout-max-width;
    width: calc(100% - $side-padding);

    gap: 24px;

    > a {
      width: calc(100% + 2px);
      margin: 0 -1px -1px -1px;
    }

    padding: 50px 0 40px;
  }

  @include media-breakpoint-down(sm) {
    .recommender-highlighted,
    .recommender-rest {
      width: 100%;
    }
    .wrapper {
      gap: 16px;
      padding: 24px 16px;
      width: calc(100% - 32px);
    }
  }

  [mno-article-card]::ng-deep {
  }

  &.style-Video {
    .recommender-rest {
      justify-content: stretch;

      [mno-article-card] {
        padding-bottom: 0;
        flex-grow: 1;
      }
    }
  }

  &.style-Gallery {
    --kui-recommender-bg-color: var(--kui-slate-800);

    [mno-article-card] {
      border: 1px solid var(--kui-slate-500);
      border-radius: 2px;
    }

    .recommender-rest {
      gap: 24px;
      flex-wrap: wrap;
      flex-direction: row;
      align-items: stretch;
      justify-content: stretch;
      margin: 0;

      [mno-article-card] {
        padding-bottom: 0;
        flex-grow: 1;
        @include media-breakpoint-up(lg) {
          width: calc(50% - 12px);
          height: calc(50% - 46px);
        }
      }

      .btn {
        width: 100%;
        height: 40px;
        flex-grow: 0;
      }
    }

    .recommender-highlighted {
      margin: 0;
    }
  }

  &.style-Podcast {
    --kui-recommender-bg-color: var(--kui-slate-700);

    [mno-article-card] {
      border: 1px solid var(--kui-slate-500);
      border-radius: 2px;
    }

    .recommender {
      &-rest {
        flex-direction: row;
        align-items: stretch;
        justify-content: space-evenly;
        gap: 16px;
        margin-bottom: 0;

        [mno-article-card] {
          flex: 1;
        }
      }
    }
  }

  &.full-width {
    @include layoutFullWidth;

    .articles {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 24px;

      @include media-breakpoint-down(sm) {
        gap: 16px;
      }

      .recommender-highlighted,
      .recommender-rest {
        width: calc(50% - 12px);
      }

      @include media-breakpoint-down(sm) {
        .recommender-highlighted,
        .recommender-rest {
          width: 100%;
        }
        .wrapper {
          gap: 16px;
          padding: 24px 16px;
          width: calc(100% - 32px);
        }
      }
    }
  }

  &.sidebar {
    .wrapper {
      width: 100%;
      padding: 24px 16px;
    }
  }
}

h3 {
  color: var(--kui-recommender-fg-color);
  flex-basis: 100%;
  width: 100%;
  min-width: 100%;
  flex-shrink: 0;
  margin-bottom: 24px;
  text-align: center;
  font-size: 32px;
  line-height: 40px;
  margin-top: -24px;
  font-weight: 700;
  border-bottom: 1px solid var(--kui-recommender-border-color);
}

.recommender {
  &-title-label {
    display: inline-block;
    background-color: var(--kui-recommender-bg-color);
    top: 18px;
    position: relative;
    padding: 0 16px;
  }

  &-highlighted {
    margin: 0 0 12px;
  }

  &-rest {
    margin: 0 0 12px 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
    flex-grow: 1;
    justify-content: flex-start;

    article {
      padding-bottom: 16px;

      &:last-child {
        border-bottom: 0;
        padding-bottom: 0;
      }
    }
  }
}
