<div class="wrapper">
  <ng-container [ngSwitch]="styleID" *ngIf="articles?.length">
    <ng-container *ngSwitchCase="RecommenderBlockType.Video">
      <h3>
        <span class="recommender-title-label">{{ title || 'Videók' }}</span>
      </h3>
      <div class="articles">
        <div class="recommender-highlighted">
          <article mno-article-card [data]="articles?.[0]" [styleID]="ArticleCardType.Img4TopTitleLeadBadgeLarge" [asVideo]="true"></article>
        </div>
        <div class="recommender-rest">
          <article
            mno-article-card
            *ngFor="let article of articles | slice: 1 : maxArticleCount; let index = index"
            [data]="article"
            [styleID]="ArticleCardType.ImgRightTagsTitleBadgeSmall"
            [asVideo]="true"
          ></article>
        </div>
      </div>
      <a class="btn btn-ghost btn-ghost-white-transparent" [routerLink]="buttonUrl || ['/', 'video']">
        Tov<PERSON><PERSON><PERSON> v<PERSON> <i mno-icon="chevron-right" class="icon-default" color="white"></i>
      </a>
    </ng-container>

    <ng-container *ngSwitchCase="RecommenderBlockType.Gallery">
      <h3>
        <span class="recommender-title-label">{{ title || 'Galériák' }}</span>
      </h3>
      <div class="articles">
        <div class="recommender-highlighted">
          <article mno-article-card [data]="articles?.[0]" [styleID]="ArticleCardType.Img4TopTitleLeadBadgeLarge" [asGallery]="true"></article>
        </div>
        <div class="recommender-rest">
          <article
            mno-article-card
            *ngFor="let article of articles | slice: 1 : 5; let index = index"
            [data]="article"
            [styleID]="ArticleCardType.Gallery"
            [asGallery]="true"
          ></article>
        </div>
      </div>
      <a class="btn btn-ghost btn-ghost-white-transparent" [routerLink]="buttonUrl || ['/', 'galeriak']">
        Tovább a galériákhoz <i mno-icon="chevron-right" class="icon-default" color="white"></i>
      </a>
    </ng-container>

    <ng-container *ngSwitchCase="RecommenderBlockType.Podcast">
      <h3>
        <span class="recommender-title-label">{{ title || 'Podcastok' }}</span>
      </h3>
      <div class="articles">
        <div class="recommender-rest">
          <article
            mno-article-card
            *ngFor="let article of articles | slice: 0 : 7; let index = index"
            [data]="article"
            [styleID]="ArticleCardType.Img4TopTitleLeadBadgeLarge"
            [asPodcast]="true"
          ></article>
        </div>
      </div>
      <a class="btn btn-ghost btn-ghost-white-transparent" [routerLink]="buttonUrl || ['/', 'podcastok']">
        További podcastok <i mno-icon="chevron-right" class="icon-default" color="white"></i>
      </a>
    </ng-container>
  </ng-container>
</div>
