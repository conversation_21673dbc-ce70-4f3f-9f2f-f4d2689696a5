import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ArticleCard, backendDateToDate, BaseComponent, DossierCardTypes, GalleryData } from '@trendency/kesma-ui';
import { ArticleCardType, RecommenderBlockType } from '../../definitions';
import { IconComponent } from '../icon/icon.component';
import { RouterLink } from '@angular/router';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { NgFor, NgIf, NgS<PERSON>, NgSwitchCase, SlicePipe } from '@angular/common';

const DEFAULT_STYLE = RecommenderBlockType.Video;

@Component({
  selector: 'mno-recommender-block',
  templateUrl: './recommender-block.component.html',
  styleUrls: ['./recommender-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgI<PERSON>, Ng<PERSON><PERSON>, Ng<PERSON><PERSON><PERSON>ase, ArticleCardComponent, NgFor, RouterLink, IconComponent, SlicePipe],
})
export class RecommenderBlockComponent extends BaseComponent<(ArticleCard | GalleryData)[]> {
  @Input() set styleID(styleID: RecommenderBlockType) {
    this.#styleID = styleID;
    this.hostClass = `style-${RecommenderBlockType[this.#styleID]}`;
    this.setProperties();
  }

  get styleID(): RecommenderBlockType {
    return this.#styleID;
  }

  @Input() maxArticleCount = 5;
  @Input() title = '';
  @HostBinding('class') hostClass = `style-${DossierCardTypes[DEFAULT_STYLE]}`;
  @Input() buttonUrl?: string | string[];
  @Input() set desktopWidth(desktopWidth: number) {
    switch (desktopWidth) {
      case 12:
        this.hostClass += ' full-width';
        break;
      case 1:
        this.hostClass += ' sidebar';
        break;
    }
    this._desktopWidth = desktopWidth;
  }

  private _desktopWidth = 12;

  readonly ArticleCardType = ArticleCardType;
  readonly RecommenderBlockType = RecommenderBlockType;

  #styleID: RecommenderBlockType = RecommenderBlockType.Video;

  get articles(): ArticleCard[] {
    return this.data as [] as unknown as ArticleCard[];
  }

  get desktopWidth(): number {
    return this._desktopWidth;
  }

  protected override setProperties(): void {
    super.setProperties();
    if (this.#styleID === RecommenderBlockType.Gallery) {
      this.setData(
        ((this.data ?? []) as (GalleryData & { date: string; isAdultsOnly?: boolean })[]).map(
          ({ title, images, slug, date, tags, highlightedImageUrl, photographer, count, isAdultsOnly }) =>
            ({
              title,
              slug,
              length: count || images?.length,
              publishDate: (typeof date as any) === 'string' ? backendDateToDate(date as any as string) : date,
              tags,
              isAdultsOnly,
              thumbnail: { url: highlightedImageUrl },
              articleSource: photographer,
            }) as ArticleCard
        )
      );
    } else {
      this.setData(
        (this.data ?? []).map(
          (articleCard) =>
            ({
              ...articleCard,
              publishDate: typeof articleCard.publishDate === 'string' ? backendDateToDate(articleCard.publishDate) : articleCard.publishDate,
            }) as ArticleCard
        )
      );
    }
  }
}
