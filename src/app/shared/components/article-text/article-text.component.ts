import { <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { AfterViewInit, Component, ElementRef, Input, OnInit, QueryList, ViewChildren, ViewEncapsulation } from '@angular/core';
import { EmbeddingService, RunScriptsDirective, UtilService } from '@trendency/kesma-core';
import { BypassPipe } from '@trendency/kesma-ui';

@Component({
  selector: 'app-article-text',
  templateUrl: './article-text.component.html',
  styleUrls: ['./article-text.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [BypassPipe, NgIf, NgFor, RunScriptsDirective],
})
export class ArticleTextComponent implements OnInit, AfterViewInit {
  @Input() data: any; // WysiwygMain;
  @Input() hasInitial = false;

  @ViewChildren('wysiwygelement', { read: ElementRef }) wsElements: QueryList<ElementRef>;

  htmlBlocks: string[] = [];

  constructor(
    private readonly embeddingService: EmbeddingService,
    private readonly utilsService: UtilService
  ) {}

  ngOnInit(): void {
    this.htmlBlocks = (this.data?.details ?? [])
      .map((d: any) => this.addLazyLoadingToImages(d.value ?? ''))
      .map((s: any) => this.embeddingService.deferIframeLoad(s))
      .map((s: any) => this.findVidea(s))
      .map((s: any) => this.findYoutube(s));
  }

  ngAfterViewInit(): void {
    this.wsElements.forEach((element: ElementRef<HTMLElement>) => {
      const oembedElements = element.nativeElement.getElementsByTagName('oembed');
      if (oembedElements.length > 0 && this.utilsService.isBrowser()) {
        this.embeddingService.createAnchorForEmbedly(Array.from(oembedElements));
      }
    });

    this.embeddingService.loadEmbedMedia(this.wsElements.map(({ nativeElement }) => nativeElement).filter((e) => !!e));
  }

  private addLazyLoadingToImages(html: string): string {
    return html?.replace(/<img(?![^>]*loading=)/g, '<img loading="lazy"');
  }

  private findVidea(html: string): string {
    return html?.replace(
      /<iframe width=".{0,6}" height=".{0,6}" src="" data-src="\/\/videa.hu/g,
      '<iframe style="aspect-ratio: 16/9; width: 100%; height: auto;" data-src="//videa.hu'
    );
  }
  findYoutube(html: string): string {
    return html?.replace(
      /<iframe width=".{0,6}" height=".{0,6}" src="" data-src="https:\/\/www.youtube.com/g,
      '<iframe style="aspect-ratio: 16/9; width: 100%; height: auto;" data-src="https://www.youtube.com'
    );
  }
}
