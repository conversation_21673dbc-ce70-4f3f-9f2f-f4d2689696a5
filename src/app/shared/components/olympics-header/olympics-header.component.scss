@use 'shared' as *;

:host {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 140px;
  padding: 10px;
  background-color: #3b2676;
  @include media-breakpoint-up(md) {
    height: 160px;
  }

  .olympics-header {
    &:hover {
      .olympics-header-logo {
        opacity: 0.8;
      }
    }

    &-mobile,
    &-desktop {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;

      img {
        object-fit: cover;
        width: 100%;
        height: 100%;
      }
    }

    &-desktop {
      display: none;
    }

    @include media-breakpoint-up(sm) {
      &-desktop {
        display: block;
      }
      &-mobile {
        display: none;
      }
    }

    &-shape {
      display: none;
      position: absolute;
      bottom: 0;
      left: 0;
      width: 200%;
      @include media-breakpoint-up(sm) {
        display: block;
      }
    }

    &-logo {
      width: 180px;
      height: 28px;
      position: relative;
      z-index: 10;
      transition: opacity 0.3s;
      @include media-breakpoint-up(md) {
        width: 100%;
        max-width: 310px;
        height: auto;
      }
    }
  }
}
