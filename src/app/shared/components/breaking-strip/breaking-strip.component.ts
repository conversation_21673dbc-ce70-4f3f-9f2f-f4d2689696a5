import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, HostBinding, Input, Output } from '@angular/core';
import { BaseComponent, BreakingBlock, buildUrl } from '@trendency/kesma-ui';
import { LabelComponent } from '../label/label.component';
import { RouterLink } from '@angular/router';
import { NgClass, NgIf } from '@angular/common';

const DEFAULT_COLOR = 'red-500';
const DEFAULT_COLOR_ICON = DEFAULT_COLOR.split('-')[0];

@Component({
  selector: '[mno-breaking-strip]',
  templateUrl: './breaking-strip.component.html',
  styleUrls: ['./breaking-strip.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, LabelComponent, NgClass],
})
export class BreakingStripComponent extends BaseComponent<BreakingBlock> {
  @Output() onClose = new EventEmitter<boolean>();

  @Input() labelText = 'Rendkívüli';

  @Input() set color(color: string) {
    if (color.indexOf('-') === -1) color = color += '-500';
    this.#color = color;
    this.iconColor = color.split('-')[0];
    this.badgeColor = `var(--kui-${this.#color})`;
  }

  constructor(private readonly cdr: ChangeDetectorRef) {
    super();
  }

  ngAfterViewInit(): void {
    this.cdr.detectChanges();
  }

  get color(): string {
    return this.#color;
  }

  @HostBinding('style.--kui-breaking-color') badgeColor = `var(--kui-${DEFAULT_COLOR})`;
  @HostBinding('class') hostClass = 'breaking-strip';

  #color = DEFAULT_COLOR;
  iconColor = DEFAULT_COLOR_ICON;

  get link(): string | string[] {
    return this.data ? buildUrl('breaking', this.data) : '';
  }

  onCloseClick(): void {
    this.onClose.emit(true);
  }
}
