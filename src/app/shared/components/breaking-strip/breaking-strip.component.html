<ng-container *ngIf="data as article">
  <div class="breaking">
    <div #breakingWrapper class="breaking-wrapper">
      <a [class.animation]="breakingWrapper.offsetWidth < row.offsetWidth" [routerLink]="link" class="breaking-link">
        <div #row class="breaking-row">
          <mno-label [color]="color">{{ labelText }}</mno-label>
          <h2 class="breaking-title">{{ article?.title }}</h2>
          <i class="icon desktop-only" [ngClass]="'icon-chevron-right-' + iconColor"></i>
        </div>
      </a>
    </div>
  </div>
  <div [style.width.px]="breakingWrapper.offsetWidth" class="icon-wrapper mobile-only">
    <i class="icon" [ngClass]="'icon-chevron-right-' + iconColor"></i>
  </div>
  <button class="btn-icon icon" [ngClass]="'icon-dismiss-circle-' + iconColor" (click)="this.onCloseClick()"></button>
</ng-container>
