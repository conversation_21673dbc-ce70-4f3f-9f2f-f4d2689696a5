@use 'shared' as *;

:host {
  --kui-breaking-color: var(--kui-red-500);

  display: flex;
  position: relative;
  height: 36px;
  border-bottom: 1px solid var(--kui-breaking-color);

  width: calc(100% - 168px);
  max-width: 1272px;

  @include media-breakpoint-down(md) {
    width: 100%;
  }
}

.breaking {
  @extend %flex;
  padding: 16px;
  gap: 4px;
  @include media-breakpoint-up(md) {
    width: 100%;
  }

  @include media-breakpoint-down(md) {
    justify-content: flex-start !important;
  }

  &-row {
    @extend %flex;
    gap: 12px;
  }

  &-wrapper {
    margin: 0 auto;
    white-space: nowrap;
    overflow: hidden;
    position: absolute;
    max-width: 80%;

    .icon {
      width: 20px;
      height: 20px;
      flex-shrink: 0;
    }

    @include media-breakpoint-down(sm) {
      max-width: 75%;
    }
  }

  &-link {
    white-space: nowrap;
    color: var(--kui-breaking-color);
    display: inline-block;

    @include media-breakpoint-down(md) {
      position: relative;
      z-index: 2;
    }

    &.animation {
      animation: loop 20s linear infinite;
      padding-left: 100%;

      @include media-breakpoint-down(md) {
        animation-duration: 8s;
      }
    }
  }

  &-title {
    font-size: 16px;
    font-weight: 700;
    line-height: 21px; /* 131.25% */
    display: inline-block;
    @include media-breakpoint-down(md) {
      font-size: 14px;
      line-height: 18px; /* 128.571% */
    }
  }
}

.btn-icon {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  flex-basis: 16px;
  margin-left: auto;

  @include media-breakpoint-down(sm) {
    width: 14px;
    height: 14px;
    flex-basis: 14px;
  }
}

.icon-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  max-width: 80%;
  margin: 0;
  justify-content: flex-end;

  @include media-breakpoint-down(sm) {
    margin-left: 10px;
  }

  .icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }
}

%flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

@keyframes loop {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(-100%, 0);
  }
}
