<ng-container *ngIf="recommendations && recommendations.length > 0">
  <div class="row foundation-recommendation">
    <div class="col-12 {{ sideRecommendations.length > 0 ? 'col-md-8' : 'col-md-12' }}">
      <h4>Tov<PERSON><PERSON><PERSON> cikkek a témában</h4>
      <div class="row">
        <div class="col-12 foundation-recommendation-main">
          <article [data]="mainRecommendation" [styleID]="ArticleCardType.ImgRightTagsTitleLeadWideBorder" mno-article-card></article>
        </div>
        <div *ngFor="let recommendation of subRecommendations" class="col-12 col-md-6 foundation-recommendation-sub">
          <article
            [data]="recommendation"
            [styleID]="(isMobile$ | async) ? ArticleCardType.ImgRightTagsTitleLeadWideBorder : ArticleCardType.NoImgBorderBottomRightDateTitleBadge"
            mno-article-card
          ></article>
        </div>
      </div>
    </div>
    <ng-container *ngIf="sideRecommendations.length > 0">
      <div class="col-12 col-md-4">
        <h4 class="side">További hírek a témában</h4>
        <div class="foundation-recommendation-side">
          <article
            *ngFor="let recommendation of sideRecommendations"
            [data]="recommendation"
            [styleID]="ArticleCardType.NoImgTitleBadge"
            mno-article-card
          ></article>
        </div>
      </div>
    </ng-container>
  </div>

  <div class="more-recommendation">
    <a [routerLink]="['/', 'cimke', foundationTagSlug]" class="link"> Még több cikk <img alt="Nyíl ikon" color="dark" mno-icon="chevron-right" /> </a>
  </div>
</ng-container>
