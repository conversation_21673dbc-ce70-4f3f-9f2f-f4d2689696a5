@use 'shared' as *;

.foundation-recommendation {
  h4 {
    font-weight: 700;
    margin-bottom: 24px;

    &.side {
      @include media-breakpoint-down(sm) {
        margin-top: 24px;
      }
    }
  }

  &-sub {
    padding-top: var(--bs-gutter-x);

    article {
      height: 100%;
    }
  }

  &-side {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
}

.more-recommendation {
  margin-top: 24px;
  text-align: center;

  a {
    color: var(--kui-slate-950);
    transition: color 0.3s;
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
    border-bottom: initial;
    display: inline-flex;
    align-items: center;
    gap: 8px;

    &:hover {
      color: var(--kui-blue-700);
      border-bottom: initial;
    }
  }
}
