import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ApiService, ConfigService } from '../../services';
import uniqBy from 'lodash-es/uniqBy';
import { ArticleCard, searchResultToArticleCard, Tag } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { AsyncPipe, NgFor, NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';
import { IconComponent } from '../icon/icon.component';
import { ArticleCardType } from '../../definitions';

@Component({
  selector: 'app-foundation-recommendation',
  templateUrl: './foundation-recommendation.component.html',
  styleUrls: ['./foundation-recommendation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleCardComponent, AsyncPipe, RouterLink, <PERSON>F<PERSON>, NgIf, IconComponent],
})
export class FoundationRecommendationComponent implements OnInit {
  @Input() foundationTagSlug: string;
  @Input() tags: Tag[];
  @Input() articleSlug: string;
  @Input() isSidebar = false;

  isMobile$: Observable<boolean>;

  recommendations: ArticleCard[] = [];
  ArticleCardType = ArticleCardType;

  constructor(
    private readonly apiService: ApiService,
    private readonly cdr: ChangeDetectorRef,
    private readonly configService: ConfigService
  ) {}

  get mainRecommendation(): ArticleCard | undefined {
    return this.recommendations.length > 0 ? this.recommendations[0] : undefined;
  }

  get subRecommendations(): ArticleCard[] {
    return this.recommendations.length > 1 ? this.recommendations.slice(1, 5) : [];
  }

  get sideRecommendations(): ArticleCard[] {
    return this.recommendations.length > 5 ? this.recommendations.slice(5, 11) : [];
  }

  ngOnInit(): void {
    this.isMobile$ = this.configService.isMobile$;
    this.getArticlesByFoundationTagSlug();
  }

  getArticlesByFoundationTagSlug(): void {
    this.apiService.searchArticleByTags([this.foundationTagSlug], 0, 12).subscribe((res) => {
      this.recommendations = res.data.filter((a) => a?.slug !== this.articleSlug).map((searchResult) => searchResultToArticleCard(searchResult));

      if (this.recommendations.length < 11) {
        this.getMoreArticlesByTags();
      } else {
        this.cdr.detectChanges();
      }
    });
  }

  getMoreArticlesByTags(): void {
    this.apiService
      .searchArticleByTags(
        this.tags.map((tag) => tag?.slug),
        0,
        12
      )
      .subscribe((res2) => {
        this.recommendations = uniqBy(
          this.recommendations.concat(res2.data.filter((a) => a?.slug !== this.articleSlug).map((searchResult) => searchResultToArticleCard(searchResult))),
          'id'
        );
        this.cdr.detectChanges();
      });
  }
}
