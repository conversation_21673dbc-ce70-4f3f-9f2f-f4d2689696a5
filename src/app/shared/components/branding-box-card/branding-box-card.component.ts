import { Component, Input } from '@angular/core';
import { ArticleCardRecommended, ArticleCardTypes } from 'src/app/shared/definitions/app.definitions';
import { NgClass, NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormatDatePipe } from '@trendency/kesma-core';

@Component({
  selector: 'app-branding-box-card',
  templateUrl: './branding-box-card.component.html',
  styleUrls: ['./branding-box-card.component.scss'],
  imports: [NgIf, RouterLink, FormatDatePipe, NgClass],
})
export class BrandingBoxCardComponent {
  @Input() styleID: number;
  @Input() isTagVisible: boolean;
  @Input() data: any;
  @Input() recommended: ArticleCardRecommended;
  @Input() isSidebar: boolean;
  readonly cardType = ArticleCardTypes;

  get publishDate(): Date {
    return this.data?.publishDate as Date;
  }
}
