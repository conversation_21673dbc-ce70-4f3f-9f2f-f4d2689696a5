<article *ngIf="data" class="article-card style-{{ styleID }}" [ngClass]="{ sidebar: isSidebar }">
  <!-- STYLE 5-->
  <ng-container *ngIf="styleID === cardType.THUMBNAIL_LEFT">
    <div class="article-left" *ngIf="data?.thumbnailUrl">
      <ng-container>
        <a
          *ngIf="data?.thumbnailUrl"
          class="article-link article-image"
          [title]="data?.title || data?.slug"
          [routerLink]="['/', 'brand', data?.brandingType, publishDate | formatDate: 'year', publishDate | formatDate: 'month', data?.slug]"
        >
          <img [src]="data?.thumbnailUrl" loading="lazy" [alt]="data?.title ?? ''" />
        </a>
      </ng-container>
    </div>
    <div class="article-right">
      <ng-container>
        <a
          class="article-link"
          [routerLink]="['/', 'brand', data?.brandingType || data?.columnSlug, publishDate | formatDate: 'year', publishDate | formatDate: 'month', data?.slug]"
        >
          <h2 class="article-title" [ngClass]="{ 'leading-article': data?.leadingArticle }">{{ data?.title }}</h2>
        </a>
      </ng-container>
      <div class="article-date">{{ publishDate | formatDate: 'y-l-m-d-h-m' }}</div>
      <p class="article-description">{{ data?.lead || data?.excerpt }}</p>
    </div>
  </ng-container>

  <!-- STYLE 6-->
  <ng-container *ngIf="styleID === cardType.NO_THUMBNAIL">
    <ng-container>
      <a
        class="article-link"
        [routerLink]="['/', 'brand', data?.brandingType, publishDate | formatDate: 'year', publishDate | formatDate: 'month', data?.slug]"
      >
        <h2 class="article-title" [ngClass]="{ 'leading-article': data?.leadingArticle }">{{ data?.title }}</h2>
      </a>
    </ng-container>
    <div class="article-date">{{ publishDate | formatDate: 'y-l-m-d-h-m' }}</div>
    <p class="article-description">{{ data?.lead }}</p>
  </ng-container>
</article>
