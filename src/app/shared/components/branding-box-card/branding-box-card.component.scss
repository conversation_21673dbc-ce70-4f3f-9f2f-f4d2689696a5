@use 'shared' as *;

$white-6: #e6e6e6;
$grey-3: #3e3e3e;
$black-4: #131313;

.article-card {
  margin-bottom: 25px;
  max-width: 100%;

  &.sidebar {
    margin-bottom: 40px;
    position: relative;

    &::after {
      content: '';
      display: block;
      position: absolute;
      left: 0;
      bottom: -20px;
      width: 30px;
      height: 2px;
      background-color: var(--kui-red);
    }
  }

  .article-image {
    margin-bottom: 12px;
    width: 100%;
    cursor: pointer;
    // padding-top: 56.25%;
    position: relative;
    overflow: hidden;

    &:hover {
      img {
        transform: scale(1.2);
      }
    }

    img {
      // position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      transform: scale(1);
      transition: all 0.15s ease-in-out;
    }
  }

  .article-tag {
    color: var(--kui-blue);
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    position: relative;
    padding-left: 8px;
    margin-bottom: 10px;

    &:before {
      position: absolute;
      content: '';
      width: 3px;
      height: 18px;
      background-color: $white-6;
      left: 0;
      top: 35%;
      transform: translateY(-50%);
    }
  }

  .article-link {
    display: table;
  }

  .article-title {
    margin-bottom: 10px;
    font-weight: 500;
    font-family: var(--kui-font-secondary);
    color: var(--kui-slate-950);
  }

  .article-description {
    color: $grey-3;
    font-weight: 300;
    font-size: 16px;
    line-height: 24px;
    word-break: keep-all;
  }

  // &.style-1,
  // &.style-2,
  // &.style-3 {
  //   .article-title {
  //     font-size: 16px;
  //     line-height: 28px;
  //     font-weight: 400;
  //     &.leading-article {
  //       font-size: 32px;
  //     }
  //   }
  //   .article-description {
  //     font-size: 12px;
  //   }
  // }
  &.style-1,
  &.style-2,
  &.style-3,
  &.style-4,
  &.style-5,
  &.style-6,
  &.style-7 {
    .article-title {
      font-size: 20px;
      line-height: 28px;

      &.leading-article {
        font-size: 32px;
      }
    }
  }

  &.style-4 {
    .article-title {
      position: relative;
      padding-left: 15px;
      text-transform: uppercase;
      line-height: 26px;

      &.leading-article {
        font-size: 32px;
      }

      &:before {
        position: absolute;
        content: '';
        height: 100%;
        width: 4px;
        left: 0;
        background-color: var(--kui-red);
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }

  .article-left {
    flex: 1 0 276px;
    margin-right: 30px;
    max-width: 276px;

    @include media-breakpoint-down(sm) {
      max-width: 276px;
      margin-bottom: 12px;
    }
  }

  .article-right {
    .recommended-tag {
      width: 50px;
      color: #818181;
      font-size: 14px;
      text-align: left;
      text-transform: uppercase;
      font-family: Roboto;
      font-weight: 400;
      font-style: normal;
      letter-spacing: normal;
      line-height: normal;
    }
  }

  .article-date {
    font-size: 12px;
    font-weight: 500;
    color: var(--kui-blue);
    margin: 5px 0 20px;
  }

  // &.style-4 {
  //   display: flex;

  //   @include media-breakpoint-down(md) {
  //     display: block;
  //   }

  //   .article-image {
  //     margin-bottom: 0;
  //   }
  // }

  &.style-5 {
    display: flex;

    @include media-breakpoint-down(sm) {
      display: block;
    }

    .article-image {
      margin-bottom: 0;
    }

    .article-left {
      @include media-breakpoint-down(sm) {
        max-width: none;
        margin-bottom: 12px;
        margin-right: 0;
      }
    }
  }
}
