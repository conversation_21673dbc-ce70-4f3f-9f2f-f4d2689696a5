import { ChangeDetectionStrategy, Component, ElementRef, HostBinding, Input, OnInit } from '@angular/core';

export type IconColor = 'blue' | 'red' | 'dark' | 'white' | 'slightblue' | 'darkblue' | 'lightblue' | 'lightgray';

const DEFAULT_ICON_COLOR: IconColor = 'dark';
const ICON_COLORMAP: Record<IconColor, string> = {
  blue: 'blue-500',
  red: 'red-500',
  dark: 'slate-950',
  white: 'slate-50',
  slightblue: 'slate-50',
  darkblue: 'blue-900',
  lightblue: 'blue-500',
  lightgray: 'slate-500',
};

@Component({
  selector: '[mno-icon]',
  template: '',
  styleUrls: ['./icon.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IconComponent implements OnInit {
  get icon(): string {
    return this.#icon;
  }

  @Input('mno-icon')
  set icon(value: string) {
    this.#icon = value;
    this.setPath();
  }

  get color(): string {
    return this.#color;
  }

  @Input() set color(value: string) {
    this.#color = value;
    this.setPath();
    this.setColor();
  }

  get size(): number {
    return this.#size;
  }

  @Input() set size(value: number) {
    this.#size = value;
  }

  @Input()
  @HostBinding('title')
  label = '';
  @Input()
  @HostBinding('class.has-label')
  showLabel = true;

  @HostBinding('style.background-image') bgImagepath?: string;
  @HostBinding('class') hostClass = 'icon';
  @HostBinding('style.--kui-icon-label-color') labelColor = `var(--kui-${ICON_COLORMAP[DEFAULT_ICON_COLOR]})`;

  @HostBinding('loading')
  @Input()
  loading = 'lazy';

  #size = 16;
  #color: IconColor | string = DEFAULT_ICON_COLOR;
  #icon = '';

  constructor(private readonly el: ElementRef<HTMLElement>) {}

  ngOnInit(): void {
    if (!this.label) {
      this.showLabel = false;
    }
    this.setPath();
    this.hostClass = 'icon icon-' + this.#size;
  }

  setPath(): void {
    const imgSrc = '/assets/images/icons/' + this.#icon + '-' + this.#color.replace('#', '') + '.svg';
    if (this.el.nativeElement.tagName === 'IMG') {
      (this.el.nativeElement as HTMLImageElement).src = imgSrc;
      (this.el.nativeElement as HTMLImageElement).alt = this.#icon;
    } else {
      this.bgImagepath = `url(${imgSrc})`;
    }
  }

  private setColor(): void {
    this.labelColor = this.#color in ICON_COLORMAP ? `var(--kui-${ICON_COLORMAP[this.#color as IconColor]})` : this.#color;
  }
}
