$iconSizes: 8, 12, 14, 16, 20, 22, 24, 28, 32, 36, 42, 45, 62;

:host {
  display: inline-block;
  font-style: normal;
  background-position: left center !important;

  @each $size in $iconSizes {
    &.icon-#{$size} {
      width: #{$size}px;
      height: #{$size}px;
      min-width: #{$size}px;
      flex-shrink: 0;

      &.has-label {
        padding-left: #{$size}px;

        &::after {
          margin-left: #{calc($size / 6)}px;
        }

        &:not(img) {
          width: auto;
        }
      }
    }
  }

  &.icon-8 {
    &::after {
      font-size: 8px;
      line-height: 10px;
    }
  }

  &.icon-16 {
    &::after {
      font-size: 10px;
      line-height: 14px;
    }
  }

  &.icon-20 {
    &::after {
      font-size: 12px;
      line-height: 16px;
    }
  }

  &.icon-24 {
    &::after {
      font-size: 14px;
      line-height: 18px;
    }
  }

  &.has-label {
    content: ' ';

    &::after {
      content: attr(title);
      color: var(--kui-icon-label-color);
      display: flex;
      height: 100%;
      align-items: center;
      justify-content: flex-start;
    }
  }
}
