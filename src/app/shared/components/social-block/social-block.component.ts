import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { BaseComponent } from '@trendency/kesma-ui';
import { StaticMenuItem } from '../../definitions';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';

@Component({
  selector: 'mno-social-block',
  templateUrl: './social-block.component.html',
  styleUrls: ['./social-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, NgClass],
})
export class SocialBlockComponent extends BaseComponent<StaticMenuItem[]> {
  @Input() title?: string;
  @Input()
  @HostBinding('class.with-border')
  withBorder = false;
  @Input()
  @HostBinding('class.auto-border')
  autoBorder = false;
  @Input()
  @HostBinding('class.auto-text-align')
  autoAlign = false;

  @Input() set iconSize(size: number) {
    this.icon24 = size === 24;
  }

  @HostBinding('class.icon-24') icon24 = false;
  @Input()
  @HostBinding('class.is-square')
  isSquare = false;
}
