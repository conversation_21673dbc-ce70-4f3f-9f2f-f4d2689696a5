@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: center;

  &.with-border,
  &.auto-border {
    border: 1px solid var(--kui-blue-500);
    border-radius: 2px;
    padding: 12px 4px;
  }

  &.auto-border {
    @include media-breakpoint-up(lg) {
      border: 0;
    }
  }

  h3 {
    font-size: 14px;
    line-height: 18px;
    font-weight: 700;
    color: var(--kui-slate-50);
    margin-bottom: 8px;
    text-align: center;
  }

  &.auto-text-align {
    padding: 0;
    @include media-breakpoint-down(md) {
      padding: 16px 24px;
      flex-direction: row;
      justify-content: center;
    }
    @include media-breakpoint-down(xs) {
      flex-direction: column;
    }

    h3 {
      width: 100%;
      @include media-breakpoint-up(lg) {
        display: none;
      }
      @include media-breakpoint-between(sm, md) {
        width: 50%;
        text-align: right;
      }
    }

    .social-icon-group {
      @include media-breakpoint-between(sm, md) {
        width: 50%;
      }
    }
  }

  &.icon-24 {
    .social-icon {
      width: 24px;
      height: 24px;

      &-container {
        width: 24px;
        height: 24px;
      }
    }
  }
}

.social {
  &-icon {
    display: inline-block;
    position: absolute;
    top: 0;
    left: 0;
    width: 32px;
    height: 32px;
    opacity: 1;
    aspect-ratio: 1/1;
    background-size: contain;
    &-group {
      display: flex;
      align-items: center;
      justify-content: space-evenly;

      :host.is-square & {
        flex-wrap: wrap;
        gap: 4px 16px;
        justify-content: flex-end;
        height: 100%;
        @include media-breakpoint-down(sm) {
          justify-content: space-evenly;
        }
      }
    }

    &-container {
      display: inline-block;
      position: relative;
      width: 32px;
      height: 32px;
      cursor: pointer;
      border: 0;

      :host.is-square &:first-child {
        margin-left: 70px;
        @include media-breakpoint-down(sm) {
          margin-left: 0;
        }
      }
    }
    @include transition;

    &:hover {
      @include transition;
      opacity: 0;
    }

    &.icon-mail {
      @include icon('icons/mail-dark.svg');
      border: 0.5px solid var(--kui-slate-300);
      border-radius: 50%;
      background-size: 67%;
      background-position: center center;
    }

    &.icon-facebook {
      @include icon('icons/facebook-circle-bg-white-dark.svg');
    }

    &.icon-instagram {
      @include icon('icons/instagram-circle-mono.svg');
    }

    &.icon-twitter {
      @include icon('icons/twitter-circle-mono.svg');
    }

    &.icon-videa {
      @include icon('icons/videa-circle-mono.svg');
    }

    &.icon-youtube {
      @include icon('icons/youtube-circle-mono.svg');
    }

    &.icon-rss {
      @include icon('icons/rss-circle-mono.svg');
    }

    &.hover {
      @include transition;
      opacity: 0;

      &:hover {
        opacity: 1;
        @include transition;
      }

      &.icon-mail {
        @include icon('icons/mail-white.svg');
        background-color: var(--kui-slate-950);
        border-color: var(--kui-slate-950);
      }

      &.icon-facebook {
        @include icon('icons/facebook-color.svg');
      }

      &.icon-instagram {
        @include icon('icons/instagram-color.svg');
      }

      &.icon-twitter {
        @include icon('icons/twitter-color.svg');
      }

      &.icon-videa {
        @include icon('icons/videa-color.svg');
      }

      &.icon-youtube {
        @include icon('icons/youtube-color.svg');
      }

      &.icon-rss {
        @include icon('icons/rss-color.svg');
      }
    }
  }
}
