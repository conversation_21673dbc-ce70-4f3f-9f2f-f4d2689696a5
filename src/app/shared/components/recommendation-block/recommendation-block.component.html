<mno-block-title-row [data]="title" [showLink]="!isMobile"></mno-block-title-row>
<div *ngIf="articles?.length" class="recommendation">
  <ng-container *ngFor="let articleCard of articles | slice: 0 : maxArticles">
    <article [data]="articleCard" [styleID]="isMobile ? mobileStyle : desktopStyle" mno-article-card></article>
  </ng-container>
</div>
<ng-container *ngIf="isMobile">
  <ng-container *ngIf="isExternal; else internalLink">
    <a [href]="title.url" class="btn btn-ghost btn-ghost-dark-transparent">
      {{ title.urlName }}<img alt="Nyíl ikon" color="dark" mno-icon="chevron-right" />
    </a>
  </ng-container>
  <ng-template #internalLink>
    <a [routerLink]="title.url" class="btn btn-ghost btn-ghost-dark-transparent">
      {{ title.urlName }}<img alt="Nyíl ikon" color="dark" mno-icon="chevron-right" />
    </a>
  </ng-template>
</ng-container>
