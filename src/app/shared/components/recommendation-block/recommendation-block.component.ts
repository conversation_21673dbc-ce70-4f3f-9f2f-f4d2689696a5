import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { ArticleCard, BlockTitle } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { Ng<PERSON><PERSON>, NgIf, SlicePipe } from '@angular/common';
import { BlockTitleRowComponent } from '../block-title-row/block-title-row.component';
import { RouterLink } from '@angular/router';
import { IconComponent } from '../icon/icon.component';
import { ArticleCardType } from '../../definitions';

@Component({
  selector: 'app-recommendation-block',
  templateUrl: './recommendation-block.component.html',
  styleUrls: ['./recommendation-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleCardComponent, NgIf, NgFor, BlockTitleRowComponent, RouterLink, SlicePipe, IconComponent],
})
export class RecommendationBlockComponent implements OnInit {
  @Input() articles: ArticleCard[] = [];
  @Input() mobileStyle = ArticleCardType.ImgRightTagsTitleLeadWideBorder;
  @Input() desktopStyle = ArticleCardType.Img4TopTagsTitleLeadSmallBorder;
  @Input() isMobile: boolean | null = false;
  @Input() maxArticles = 4;
  @Input() title: BlockTitle = {
    text: '',
    url: undefined,
    urlName: undefined,
  };
  readonly ArticleCardType = ArticleCardType;
  isExternal = false;

  ngOnInit(): void {
    this.isExternal = !!this.title?.url?.match(/^http|\/\//);
  }
}
