import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, HostBinding, Input, TemplateRef } from '@angular/core';
import { SwiperBaseComponent } from '@trendency/kesma-ui';
import { As<PERSON><PERSON>ipe, Ng<PERSON>or, NgIf, NgTemplateOutlet } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'mno-card-slider',
  templateUrl: './card-slider.component.html',
  styleUrls: ['./card-slider.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent, <PERSON><PERSON><PERSON>, NgIf, NgTemplateOutlet, AsyncPipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CardSliderComponent<T> extends SwiperBaseComponent<T[]> {
  @HostBinding('class') hostClass = '';
  @Input() template?: TemplateRef<{ $implicit: T; index: number }>;

  @Input() title: string = 'Legfrissebb';

  swiperBreakpoints = {
    1440: { slidesPerView: 6 },
    1024: { slidesPerView: 4 },
    640: { slidesPerView: 3 },
    300: { slidesPerView: 2 },
  };

  slidesPerView = 6;

  override ngOnInit(): void {
    super.ngOnInit();
    this.setSlidesPerView();
  }

  // It can work around the resize host listener in this way
  setSlidesPerView(): void {
    const swiperRef = this.swiperRef();
    if (swiperRef?.params?.breakpoints) {
      const currentBreakPoint = swiperRef.params?.breakpoints[this.swiperRef()?.currentBreakpoint];
      this.slidesPerView = currentBreakPoint.slidesPerView as number;
    }
  }
}
