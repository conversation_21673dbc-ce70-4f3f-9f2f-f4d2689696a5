@use 'shared' as *;

:host {
  display: block;
  max-width: 100%;
}

.card-slider {
  display: flex;
  flex-direction: column;
  gap: 24px;

  &-header {
    display: flex;
    justify-content: space-between;

    &-title {
      color: var(--kui-blue-900);
      font-size: 24px;
      font-weight: 700;
      line-height: 28px;
      letter-spacing: 0.015em;
      text-align: left;
    }

    &-navigation {
      display: flex;
      gap: 24px;

      .nav-btn {
        width: 32px;
        height: 32px;
        background: var(--kui-white);
        border: 1px solid var(--kui-blue-50);
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;

        &.disabled {
          opacity: 0.5;
          pointer-events: none;
        }

        .icon {
          min-height: 14px;
        }
      }
    }
  }

  &-item {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}
