<div class="card-slider">
  <div class="card-slider-header">
    <h1 class="card-slider-header-title">{{ title }}</h1>
    <div class="card-slider-header-navigation">
      <button (click)="swipePrev()" [class.disabled]="isBeginning$ | async" class="nav-btn">
        <img [size]="24" alt="Nyíl ikon" mno-icon="chevron-left" />
      </button>
      <button (click)="swipeNext()" [class.disabled]="isEnd$ | async" class="nav-btn">
        <img [size]="24" alt="Nyíl ikon" mno-icon="chevron-right" />
      </button>
    </div>
  </div>
  <div class="card-slider-wrapper">
    <swiper-container #swiper [slidesPerView]="slidesPerView" space-between="40" [breakpoints]="swiperBreakpoints">
      <swiper-slide *ngFor="let item of data; let i = index">
        <div class="card-slider-item">
          <ng-container *ngIf="template" [ngTemplateOutletContext]="{ $implicit: item, index: i }" [ngTemplateOutlet]="template"></ng-container>
        </div>
      </swiper-slide>
    </swiper-container>
  </div>
</div>
