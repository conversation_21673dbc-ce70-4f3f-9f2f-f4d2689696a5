import { ChangeDetectionStrategy, Component, EventEmitter, HostBinding, Input, Output, inject } from '@angular/core';
import {
  Article,
  ArticleCard,
  Author,
  backendDateToDate,
  BaseComponent,
  buildArticleUrl,
  FakeBool,
  FocusPointDirective,
  Tag,
  ThumbnailInfo,
} from '@trendency/kesma-ui';
import { ArticleCardType, OpinionCardType, PALCEHOLDER_IMG } from '../../definitions';
import { AuthorAvatarSize, OpinionAuthorComponent } from '../opinion-author/opinion-author.component';
import { SocialShareComponent } from '../social-share/social-share.component';
import { BadgeDirective } from '../../directives';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { IconComponent } from '../icon/icon.component';
import { Router, RouterLink } from '@angular/router';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON><PERSON>ase, NgTemplateOutlet, SlicePipe } from '@angular/common';
import { DateFnsModule } from 'ngx-date-fns';
import { isArray } from 'lodash-es';

@Component({
  selector: 'mno-opinion-card',
  templateUrl: './opinion-card.component.html',
  styleUrls: ['./opinion-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    RouterLink,
    NgTemplateOutlet,
    IconComponent,
    OpinionAuthorComponent,
    NgSwitch,
    NgSwitchCase,
    ArticleCardComponent,
    NgFor,
    FocusPointDirective,
    BadgeDirective,
    SocialShareComponent,
    SlicePipe,
    DateFnsModule,
  ],
})
export class OpinionCardComponent extends BaseComponent<ArticleCard & { avatarLarge: string }> {
  @Input()
  @HostBinding('class.layout-opinion')
  inLayout = false;
  @HostBinding('class') hostClass = 'article-card';
  @HostBinding('style.background-image') backgroundImage = '';

  @Input()
  @HostBinding('class.sidebar')
  isSidebar = false;

  @Input() isTargetBlank = false;
  @Input() authorOverride?: Author;
  @Input() tags: Tag[] = [];
  @Input() hasGallery: boolean | FakeBool = false;
  @Input() isAdultsOnly: boolean | FakeBool = false;
  @Input() hasVideo = false;
  @Input() showOpinionLabels = true;
  @Output() minuteBlockClick = new EventEmitter<{ index: number; event: MouseEvent }>();
  displayedThumbnailUrl?: string;
  displayed43ThumbnailUrl?: string;
  articleLink: string[] | string = [];
  author?: Author;
  labels: { title: string; link?: string[] }[] = [];
  publishDate?: Date;
  readonly OpinionCardType = OpinionCardType;
  readonly ArticleCardType = ArticleCardType;
  iconSize = 62;
  authorSize: AuthorAvatarSize = 'small';
  withContainer = true;
  tagLimit = 10;
  isInterview = false;
  #isMobile: boolean | null = false;
  #styleID?: OpinionCardType;
  private readonly router = inject(Router);

  get styleID(): OpinionCardType {
    return this.#styleID as OpinionCardType;
  }

  @Input()
  set styleID(styleID: OpinionCardType) {
    this.#styleID = styleID;
    this.setProperties();
  }

  get isMobile(): boolean {
    return !!this.#isMobile;
  }

  @Input() set isMobile(isMobile: boolean | null) {
    this.#isMobile = isMobile;
    this.setProperties();
  }

  get shareLink(): string[] {
    return typeof this.articleLink === 'string' ? [this.articleLink] : this.articleLink;
  }

  get articleCard(): ArticleCard & { thumbnailInfo: ThumbnailInfo; avatarLarge: string } {
    return this.data as ArticleCard & { thumbnailInfo: ThumbnailInfo; avatarLarge: string };
  }

  get article(): Article {
    return this.data as Article & { avatarLarge: string };
  }

  override setProperties(): void {
    super.setProperties();
    if (!this.data) return;

    this.inLayout = !!this.data.hasLenia;
    this.isInterview = this.styleID === OpinionCardType.InterviewHeader;
    this.articleLink = buildArticleUrl(this.data, undefined, true);
    this.displayed43ThumbnailUrl =
      this.articleCard?.thumbnailUrl43 ||
      this.articleCard?.thumbnail?.url43AspectRatio ||
      this.articleCard?.thumbnail?.url ||
      this.articleCard?.thumbnailUrl ||
      'assets/images/' + PALCEHOLDER_IMG;
    this.displayedThumbnailUrl = this.articleCard?.thumbnailUrl || this.articleCard?.thumbnail?.url || 'assets/images/' + PALCEHOLDER_IMG;

    this.labels =
      ([
        //{ title: this.articleCard.labelText },
        ...(this.articleCard?.tags ?? []).map(({ title, slug }) => ({
          title,
          link: slug ? ['/', 'cimke', slug] : undefined,
        })),
      ].filter((s) => !!s) as any[]) ?? [];
    if (
      [OpinionCardType.OpinionWaiter, OpinionCardType.AuthorOpinionHeader, OpinionCardType.AuthorLabelTitleBadge, OpinionCardType.LabelTitleBadge].includes(
        this.styleID
      )
    ) {
      this.labels = this.labels.slice(0, 1);
    }

    this.iconSize = this.styleID === OpinionCardType.Narrow ? 45 : 62;
    if (this.styleID === OpinionCardType.OpinionWaiter) {
      this.iconSize = 24;
    }

    if (this.isMobile) {
      this.iconSize = 42;
    }

    this.hasGallery = this.articleCard.hasGallery ?? false;
    this.isAdultsOnly = this.articleCard.isAdultsOnly ?? false;
    this.author =
      this.authorOverride ??
      ({
        ...(this.articleCard.author ?? {}),
        slug: this.articleCard.author?.slug || this.articleCard.publicAuthorSlug,
      } as Author);
    this.publishDate =
      typeof this.articleCard.publishDate === 'string' ? (backendDateToDate(this.articleCard.publishDate) as Date) : this.articleCard.publishDate;

    if (this.#styleID !== undefined) {
      this.withContainer = [
        OpinionCardType.Large,
        OpinionCardType.Narrow,
        OpinionCardType.OpinionWaiter,
        OpinionCardType.OpinionHeader,
        OpinionCardType.AuthorOpinionHeader,
        OpinionCardType.InterviewHeader,
        OpinionCardType.MinuteToMinuteHeader,
        OpinionCardType.SorozatvetoHeader,
        OpinionCardType.WhereTheBallWillBe,
      ].includes(this.#styleID);

      this.authorSize = this.isMobile ? 'medium' : 'small';
      if ([OpinionCardType.Large, OpinionCardType.Narrow, OpinionCardType.OpinionWaiter, OpinionCardType.OpinionHeader].includes(this.#styleID)) {
        this.authorSize = 'medium';
      } else if (
        [
          OpinionCardType.SmallAuthorRightBorderBlue,
          OpinionCardType.SmallImgRightBorderBlack,
          OpinionCardType.SmallImgRightSplitBorderBlack,
          OpinionCardType.SmallAuthorRight,
          OpinionCardType.LabelTitleBadge,
          OpinionCardType.AuthorLabelTitleBadge,
        ].includes(this.#styleID)
      ) {
        this.authorSize = 'large';
      } else if ([OpinionCardType.AuthorOpinionHeader].includes(this.#styleID)) {
        this.authorSize = 'larger';
      }
    }
    this.tagLimit = this.isMobile ? 1 : 10;
    this.setHostClass();
  }

  onBlockClick(index: number, event: MouseEvent): void {
    this.minuteBlockClick.emit({ index, event });
  }

  onLinkClick(articleLink: string | string[]): void {
    if (isArray(articleLink)) {
      this.router.navigate(articleLink);
    } else {
      this.router.navigate(articleLink.split('/'));
    }
  }

  private setHostClass(): void {
    this.hostClass = ['opinion-card', `style-${OpinionCardType[this.styleID]}`].filter((o) => !!o).join(' ');

    if (this.styleID === OpinionCardType.SorozatvetoHeader && this.displayedThumbnailUrl) {
      this.backgroundImage = `
      linear-gradient(0deg, rgba(15, 23, 42, 0.85) 0%, rgba(15, 23, 42, 0.85) 100%), url("${this.displayedThumbnailUrl}")
      `;
    }
  }
}
