import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';

const DEFAULT_COLOR = 'slate-950';

@Component({
  selector: 'mno-label',
  template: '<ng-content></ng-content>',
  styleUrls: ['./label.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LabelComponent {
  @Input() set color(color: string) {
    this.#color = color;
    this.labelColor = `var(--kui-${this.#color})`;
  }

  @HostBinding('style.--kui-label-color') labelColor = `var(--kui-${DEFAULT_COLOR})`;

  #color = DEFAULT_COLOR;
}
