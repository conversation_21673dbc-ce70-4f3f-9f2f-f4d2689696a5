@use 'shared' as *;

:host {
  display: block;
  color: var(--kui-slate-50);
}

.footer {
  &-top {
    background-color: var(--kui-blue-900);

    &-container {
      display: flex;
      justify-content: center;
      gap: 8px;
      padding: 24px;
      @include media-breakpoint-down(md) {
        flex-wrap: wrap;
        gap: 16px;
      }

      mno-social-block {
        min-width: 352px;
        @include media-breakpoint-down(md) {
          width: 100%;
          order: 1;
        }
      }

      .btn {
        height: 32px;

        .icon {
          width: 16px;
          height: 16px;
        }

        @include media-breakpoint-down(md) {
          order: 2;
          width: calc(50% - 8px);
        }
        @include media-breakpoint-down(xs) {
          width: 100%;
        }
      }
    }

    &-links {
      display: flex;
      justify-content: center;
      padding: 20px 0 28px;
      @include media-breakpoint-down(md) {
        display: none;
      }
    }

    &-link {
      font-family: var(--kui-font-primary);
      font-size: 16px;
      color: var(--kui-slate-50);
      border-left: 1px solid var(--kui-slate-50);
      padding: 0 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      @include transition;

      &:hover {
        text-decoration: underline;
        @include transition;
      }

      &:first-child {
        border-left: 0;
      }
    }
  }

  &-bottom {
    background-color: var(--kui-blue-950);
    display: flex;
    justify-content: center;
    padding: 19px 0;
    gap: 12px;
    align-items: center;
    letter-spacing: 0.08px;

    @include media-breakpoint-down(md) {
      font-weight: normal;
      font-size: 12px;
      line-height: 16px;
      gap: 4px;
    }

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      text-align: center;
      padding: 19px 72px;
    }

    &-copyright {
      font-weight: 600;
      font-size: 20px;
      line-height: 26px;
      letter-spacing: 0.3px;

      border-left: 1px solid var(--kui-slate-50);
      padding-left: 12px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      @include media-breakpoint-down(md) {
        gap: 4px;
        padding-left: 4px;
        font-weight: 500;
        font-size: 14px;
        line-height: 18px;
      }
      @include media-breakpoint-down(sm) {
        padding-left: 0;
        border: 0;
        margin-top: 8px;
      }
    }
  }

  &-logo {
    width: 110px;
    @include media-breakpoint-down(md) {
      width: 72px;
    }
  }
}
