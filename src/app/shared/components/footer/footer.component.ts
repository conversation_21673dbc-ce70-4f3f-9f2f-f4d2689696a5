import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { StaticMenuItem } from '../../definitions';
import { buildSocialMenu, DEFAULT_SUBSCRIBE_LINK } from '../../utils';
import { RouterLink } from '@angular/router';
import { SocialBlockComponent } from '../social-block/social-block.component';

@Component({
  selector: '[mno-footer]',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SocialBlockComponent, RouterLink],
})
export class FooterComponent implements OnInit, AfterViewInit {
  @Output() subscriptionClick = new EventEmitter<MouseEvent>();
  @Output() cookieSettingsClick = new EventEmitter<MouseEvent>();

  @Input() facebookLink = '';
  @Input() instagramLink = '';
  @Input() twitterLink = '';
  @Input() videaLink = '';
  @Input() youtubeLink = '';
  @Input() rssLink = '';

  subscribeLink = DEFAULT_SUBSCRIBE_LINK;
  socialIcons: StaticMenuItem[] = [];

  currentYear: number = new Date().getFullYear();

  ngOnInit(): void {
    this.socialIcons = buildSocialMenu(this.facebookLink, this.instagramLink, this.twitterLink, this.videaLink, this.youtubeLink);
  }

  ngAfterViewInit(): void {
    this.socialIcons = buildSocialMenu(this.facebookLink, this.instagramLink, this.twitterLink, this.videaLink, this.youtubeLink);
  }
}
