<ng-container *ngIf="!isSidebar; else inSidebar">
  <mno-card-slider [title]="title" [data]="data" [template]="template"></mno-card-slider>
  <ng-template #template let-item let-i="index">
    <ng-container *ngTemplateOutlet="mostViewedItem; context: { item, i }"></ng-container>
  </ng-template>
</ng-container>

<ng-template #inSidebar>
  <h1 class="most-read-title">{{ title }}</h1>
  <ng-container *ngFor="let item of data | slice: 0 : 10; let i = index">
    <ng-container *ngTemplateOutlet="mostViewedItem; context: { item, i }"></ng-container>
  </ng-container>
</ng-template>

<ng-template #mostViewedItem let-item="item" let-i="i">
  <div class="most-read-item" [class.in-sidebar]="isSidebar">
    <div class="most-read-item-date" *ngIf="isPublishDate; else serial">{{ getPublishDate(item) | formatDate: 'h-m' }}</div>
    <ng-template #serial>
      <div class="most-read-item-serial">{{ i + 1 }}</div>
    </ng-template>
    <article mno-article-card [styleID]="articleCardType.RelatedArticle" [isSidebar]="isSidebar" [data]="item"></article>
  </div>
</ng-template>
