@use 'shared' as *;

:host {
  display: block;
  max-width: 100%;

  &.in-sidebar {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin: 0 0 32px;
    padding-bottom: 16px;
  }

  &.with-publish-date {
    .most-read-item {
      &:hover {
        background-color: var(--kui-blue-950);

        [mno-article-card]::ng-deep {
          .article-title {
            color: var(--kui-white);
          }
        }
      }
    }
  }
}

.most-read {
  display: flex;
  flex-direction: column;
  gap: 24px;

  &-title {
    color: var(--kui-blue-900);
    font-size: 24px !important;
    font-style: normal;
    font-weight: 700;
    line-height: 28px;
    margin-bottom: 16px;
  }

  &-item {
    &:hover {
      background-color: var(--kui-blue-900);

      [mno-article-card]::ng-deep {
        .article-title {
          color: var(--kui-white);
        }
      }

      @include transition;

      .most-read-item-date,
      .most-read-item-serial {
        @include transition;
        color: var(--kui-white);
        border-color: var(--kui-white);

        [mno-article-card]::ng-deep {
          .article-title {
            color: var(--kui-white);
          }
        }
      }
    }

    padding-right: 8px;
    padding-left: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    border-bottom: 1px solid var(--kui-blue-900);
    border-right: 1px solid var(--kui-blue-900);

    &.in-sidebar {
      flex-direction: row;
      gap: 24px;
    }

    &-date {
      font-family: var(--kui-font-secondary);
      font-size: 20px;
      font-weight: 700;
      padding: 4px 8px;
      border: 1px solid var(--kui-blue-50);
      border-radius: 2px;
      color: var(--kui-blue-900);
      display: flex;
      width: max-content;
      align-self: flex-start;
      margin-top: 8px;
    }

    &-serial {
      margin-top: 8px;
      font-family: var(--kui-font-secondary);
      font-size: 32px;
      font-weight: 700;
      border: 1px solid var(--kui-blue-50);
      border-radius: 2px;
      color: var(--kui-blue-900);
      display: flex;
      justify-content: center;
      align-items: center;
      width: 44px;
      height: 44px;
      align-self: flex-start;
      flex-shrink: 0;
    }
  }
}

.card-slider-item::ng-deep {
  border: 0 !important;
}

:host {
  &.in-sidebar {
    .most-read-item {
      height: auto;
    }
  }
}

.most-read-item {
  height: 200px;

  [mno-article-card]::ng-deep {
    padding: 8px 0 16px;

    .article-title {
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 14px;
      line-height: 18px;
      font-weight: 700;
    }

    &:hover {
      .article-title {
        // color: var(--kui-blue-500) !important;
      }
    }
  }

  [mno-article-card]::ng-deep {
    background-color: transparent !important;
  }
}
