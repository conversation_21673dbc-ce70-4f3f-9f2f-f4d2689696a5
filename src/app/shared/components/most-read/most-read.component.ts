import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ArticleCard, BaseComponent, buildArticleUrl } from '@trendency/kesma-ui';
import { ArticleCardType, MostReadType } from '../../definitions';
import { backendDateToDate, FormatDatePipe } from '@trendency/kesma-core';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { CardSliderComponent } from '../card-slider/card-slider.component';
import { NgFor, NgIf, NgTemplateOutlet, SlicePipe } from '@angular/common';

@Component({
  selector: 'mno-most-read',
  templateUrl: './most-read.component.html',
  styleUrls: ['./most-read.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, CardSliderComponent, NgTemplateOutlet, NgFor, ArticleCardComponent, SlicePipe, FormatDatePipe],
})
export class MostReadComponent extends BaseComponent<ArticleCard[]> {
  @HostBinding('class') hostClass = '';

  @Input() title: string = 'Legfrissebb';
  @Input()
  @HostBinding('class.with-publish-date')
  isPublishDate: boolean = false;
  @Input() isSidebar = false;

  @Input() set styleID(styleID: MostReadType) {
    if (styleID === MostReadType.PublishDateHorizontal) {
      styleID = MostReadType.DefaultMostRead;
      this.isPublishDate = true;
    }
    if (styleID === MostReadType.PublishDateSidebar) {
      styleID = MostReadType.Sidebar;
      this.isSidebar = true;
      this.isPublishDate = true;
    }
    if (styleID === MostReadType.Sidebar) {
      this.isSidebar = true;
    }
    this.cardType = styleID;
    this.hostClass = `${this.isSidebar ? 'in-sidebar' : ''} style-${MostReadType[styleID]}`;
  }

  cardType: MostReadType = MostReadType.DefaultMostRead;
  articleCardType = ArticleCardType;

  getArticleLink(article: ArticleCard): string[] {
    return buildArticleUrl(article);
  }

  getPublishDate(article: ArticleCard): Date {
    return article.publishDate instanceof Date ? article.publishDate : backendDateToDate(article.publishDate as string)!;
  }
}
