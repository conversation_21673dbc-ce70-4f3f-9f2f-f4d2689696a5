import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { Observable, of } from 'rxjs';
import { EBPortalEnum, EbTeamsComponent as KesmaEbTeamsComponent, EBTeamsTable } from '@trendency/kesma-ui';
import { SportResultService } from '../../../services';
import { map } from 'rxjs/operators';
import { EbService } from 'src/app/shared/services';
import { AsyncPipe, NgIf } from '@angular/common';
import { CompetitionSummary } from '../../../definitions';

@Component({
  selector: 'app-eb-teams',
  templateUrl: './eb-teams.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, NgIf, KesmaEbTeamsComponent],
})
export class EbTeamsComponent implements OnInit {
  @Input() desktopWidth = 12;

  data$?: Observable<EBTeamsTable[]>;
  EBPortalEnum = EBPortalEnum;

  constructor(
    private readonly sportResultService: SportResultService,
    private readonly ebService: EbService
  ) {}

  ngOnInit(): void {
    const slug = this.ebService.getSlug();
    this.data$ = slug ? this.sportResultService.getCompetitionSummary(slug).pipe(map((data) => this.mapBackendSummaryToSummary(data))) : of([]);
  }

  private mapBackendSummaryToSummary(summaries: CompetitionSummary[]): EBTeamsTable[] {
    return summaries.map((summary) => ({
      groupName: summary?.phase?.name,
      teams: summary?.table,
      results: summary?.schedules,
    }));
  }
}
