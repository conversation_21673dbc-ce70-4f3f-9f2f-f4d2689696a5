@use 'shared' as *;

:host {
  width: 100%;
}

.eb {
  &-header-background {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-image: url('/assets/images/eb/bg_EB_v2.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 100%;
    position: relative;

    &-container {
      display: flex;
      align-items: center;
      flex-direction: column;
      position: relative;
    }
  }

  &-logo {
    padding: 50px 0 50px;
    width: 513px;

    @include media-breakpoint-down(sm) {
      width: 224px;
    }
  }

  &-shape {
    position: absolute;
    bottom: 0;
    background-image: url('/assets/images/eb/bg_EB_v2_shape.svg');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    @include media-breakpoint-down(sm) {
      width: 960px;
    }
  }

  &-ball {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 103px;

    @include media-breakpoint-down(sm) {
      width: 55px;
    }
  }
}
