import { Async<PERSON>ipe, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { backendDateToDate } from '@trendency/kesma-core';
import { ChampionshipSchedule, EbDailyProgramComponent as KesmaEbDailyProgramComponent, EBPortalEnum } from '@trendency/kesma-ui';
import { map, Observable, of } from 'rxjs';
import { EbService } from 'src/app/shared/services';
import { SportResultService } from 'src/app/shared/services/sport-result.service';

@Component({
  selector: 'app-eb-daily-program',
  templateUrl: './eb-daily-program.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, NgIf, KesmaEbDailyProgramComponent],
})
export class EbDailyProgramComponent implements OnInit {
  readonly EBPortalEnum = EBPortalEnum;
  dailyProgram$: Observable<ChampionshipSchedule[]>;

  constructor(
    private readonly ebService: EbService,
    private readonly sportResultService: SportResultService
  ) {}

  ngOnInit(): void {
    const slug = this.ebService.getSlug();
    this.dailyProgram$ = slug
      ? (this.sportResultService.getScheduleByCompetition(slug).pipe(
          map(({ data: { schedules } }) =>
            schedules?.map((schedule: ChampionshipSchedule) => ({
              ...schedule,
              scheduleDate: {
                ...schedule?.scheduleDate,
                date: backendDateToDate(schedule?.scheduleDate?.date.toString()),
              },
            }))
          )
        ) as Observable<ChampionshipSchedule[]>)
      : of([] as ChampionshipSchedule[]);
  }
}
