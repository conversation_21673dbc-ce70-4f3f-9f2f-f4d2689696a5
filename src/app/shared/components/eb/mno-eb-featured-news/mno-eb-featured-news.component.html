<ng-container *ngIf="data">
  <div class="eb-featured-news-header">
    <i class="icon eb-labdarugo"></i>
  </div>
  <div class="eb-featured-news">
    <div class="eb-featured-news-title">
      <div class="eb-featured-news-divider"></div>
      {{ title }}
      <div class="eb-featured-news-divider"></div>
    </div>
    <div class="eb-featured-news-content">
      <a [routerLink]="buildArticleUrl(article)" class="eb-featured-news-article" *ngFor="let article of data">
        <img
          class="eb-featured-news-article-thumbnail"
          [src]="article?.thumbnail?.url || './assets/placeholder.svg'"
          [alt]="article?.thumbnail?.alt || ''"
          loading="lazy"
        />
        <h2 class="eb-featured-news-article-title">{{ article?.title }}</h2>
      </a>
    </div>
  </div>
  <div class="eb-featured-news-bottom-shape"></div>
</ng-container>
