import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ArticleCard, BaseComponent, buildArticleUrl } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';

@Component({
  selector: 'mno-eb-featured-news',
  templateUrl: './mno-eb-featured-news.component.html',
  styleUrls: ['./mno-eb-featured-news.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, RouterLink],
})
export class MnoEbFeaturedNewsComponent extends BaseComponent<ArticleCard[]> {
  @Input() title: string = 'Kiemelt hírek';
  @Input() @HostBinding('class.is-mobile') isMobile: boolean = false;

  @Input() set desktopWidth(desktopWidth: number) {
    this.isMobile = desktopWidth <= 6;
  }

  buildArticleUrl = buildArticleUrl;
}
