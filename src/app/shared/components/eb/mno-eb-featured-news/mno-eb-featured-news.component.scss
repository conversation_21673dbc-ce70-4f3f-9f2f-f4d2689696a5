@use 'shared' as *;

:host {
  @extend %flex-column;
  background-color: #0d1e62;
  color: var(--kui-white);
  width: 100%;
  gap: 12px;

  .icon {
    width: 164px;
    height: 24px;
  }

  .eb-featured-news {
    @extend %flex-column;
    align-items: center;
    max-width: 1056px;
    margin: 0 auto;
    position: relative;
    width: 100%;
    gap: 16px;

    &:before,
    &:after {
      position: absolute;
      content: '';
      height: 100%;
    }

    &:before {
      border-left: 1px solid var(--kui-white);
      left: -1px;
    }

    &:after {
      border-right: 1px solid var(--kui-white);
      right: -1px;
    }

    &-header {
      background-color: #0e2a99;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 24px;
      height: 64px;
    }

    &-title {
      font-size: 32px;
      font-family: var(--kui-font-primary);
      line-height: 40px;
      font-weight: 700;
      white-space: nowrap;
      display: flex;
      align-items: center;
      width: 100%;
      gap: 16px;
    }

    &-divider {
      width: 100%;
      height: 1px;
      background-color: var(--kui-white);
    }

    &-content {
      display: flex;
      padding: 0 38px;
      gap: 40px;
    }

    &-article {
      @extend %flex-column;
      border-bottom: 1px solid #64748b;
      padding-bottom: 16px;
      gap: 8px;
    }

    &-article-thumbnail {
      border-radius: 2px;
      aspect-ratio: 4/3;
      object-fit: cover;
      flex-shrink: 0;
    }

    &-article-title {
      font-family: var(--kui-font-secondary);
      color: var(--kui-white);
      font-size: 24px;
      line-height: 30px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      height: 90px;
    }

    &-bottom-shape {
      background-image: url('/assets/eb-footer.svg');
      background-repeat: no-repeat;
      background-position: bottom;
      background-size: 100%;
      height: 80px;
    }
  }

  @mixin mobile() {
    gap: 16px;

    .icon {
      width: 123px;
      height: 18px;
    }

    .eb-featured-news {
      &-title {
        font-size: 24px;
        line-height: 32px;
        letter-spacing: 0.24px;
      }

      &-content {
        flex-direction: column;
        gap: 16px;
      }

      &-bottom-shape {
        background-position: right;
        background-size: cover;
        margin-top: -16px;
      }

      &-article-title {
        height: auto;
      }
    }
  }

  &.is-mobile {
    @include mobile();
  }

  @include media-breakpoint-down(sm) {
    @include mobile();
  }
}

%flex-column {
  display: flex;
  flex-direction: column;
}
