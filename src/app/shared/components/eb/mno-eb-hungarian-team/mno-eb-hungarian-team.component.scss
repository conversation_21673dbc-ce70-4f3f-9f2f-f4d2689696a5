@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  padding: 24px 16px 0 16px;
  color: var(--kui-white);

  background-color: #0d1e62; // eb color

  .eb-title {
    font-size: 16px;
    font-weight: 700;
    line-height: 21px;
    border-bottom: 1px solid var(--kui-white);
    padding-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;

    .eb-icon {
      border-radius: 50%;
      border: 1px solid var(--kui-white);
      width: 26px;
      height: 26px;
    }
  }

  .eb-wrapper {
    display: flex;
    flex-direction: column;
  }

  .eb-article {
    &-thumbnail {
      aspect-ratio: 16/9;
      object-fit: cover;
      @extend .full-width;
      max-width: unset;

      @include media-breakpoint-down(sm) {
        width: 100%;
        max-width: 100%;
        margin: 0 !important;
      }
    }

    &-title {
      font-size: 16px;
      line-height: 22px;
      font-weight: 500;
      letter-spacing: 0.08px;
      border-bottom: 1px solid var(--kui-white);
      padding: 12px 0;
      display: flex;
      align-items: center;
      gap: 10px;
      color: var(--kui-white);

      &.borderless {
        border-bottom: none;
      }

      &.highlighted {
        font-size: 20px;
        line-height: 26px;
        font-weight: 700;
        letter-spacing: 0.3px;
        padding: 8px 16px 16px 16px;
        @extend .full-width;
        color: var(--kui-white);
      }
    }
  }

  .eb-link {
    border: 1px solid var(--kui-white);
    color: var(--kui-white);
    border-radius: 2px;
    padding: 6px 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 8px;
  }

  .eb-bottom-shape {
    background-image: url('/assets/eb-footer.svg');
    background-repeat: no-repeat;
    background-position: bottom right;
    background-size: cover;
    height: 80px;
    @extend .full-width;
  }
}

.full-width {
  width: calc(100% + 32px);
  margin: 0 -16px;
}
