import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleCard, BaseComponent, buildArticleUrl } from '@trendency/kesma-ui';
import { IconComponent } from '../../icon/icon.component';
import { RouterLink } from '@angular/router';
import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';

@Component({
  selector: 'mno-eb-hungarian-team',
  templateUrl: './mno-eb-hungarian-team.component.html',
  styleUrls: ['./mno-eb-hungarian-team.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, RouterLink, IconComponent],
})
export class MnoEbHungarianTeamComponent extends BaseComponent<ArticleCard[]> {
  buildArticleUrl = buildArticleUrl;
}
