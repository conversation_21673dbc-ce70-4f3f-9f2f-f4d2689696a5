<ng-container *ngIf="data">
  <div class="eb-title">
    <img class="eb-icon" src="/assets/images/eb-flags-2024/HUN.svg" alt="" loading="lazy" />
    Magyar csapat
  </div>

  <div class="eb-wrapper">
    <article class="eb-article" *ngFor="let article of data; first as first; last as last; trackBy: trackByFn">
      <a [routerLink]="buildArticleUrl(article)">
        <ng-container *ngIf="first">
          <img
            class="eb-article-thumbnail"
            [src]="article?.thumbnail?.url || 'assets/images/magyar-nemzet.png'"
            [alt]="article?.thumbnail?.alt || ''"
            loading="lazy"
          />
        </ng-container>
        <h2 class="eb-article-title" [class.borderless]="last" [class.highlighted]="first">
          <i *ngIf="!first" [size]="20" color="white" mno-icon="plus"></i>
          {{ article.title }}
        </h2>
      </a>
    </article>
  </div>

  <a [routerLink]="['/', 'labdarugo-eb-2024']" class="eb-link"> Még több foci Eb <i [size]="16" color="white" mno-icon="chevron-right"></i> </a>

  <div class="eb-bottom-shape"></div>
</ng-container>
