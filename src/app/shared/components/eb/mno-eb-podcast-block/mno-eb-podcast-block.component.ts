import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleCard, BaseComponent, Tag } from '@trendency/kesma-ui';
import { ArticleCardType } from '../../../definitions';
import { RouterLink } from '@angular/router';
import { ArticleCardComponent } from '../../article-card/article-card.component';
import { NgF<PERSON>, NgIf } from '@angular/common';

@Component({
  selector: 'mno-eb-podcast-block',
  templateUrl: './mno-eb-podcast-block.component.html',
  styleUrls: ['./mno-eb-podcast-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [Ng<PERSON><PERSON>, Ng<PERSON><PERSON>, ArticleCardComponent, RouterLink],
})
export class MnoEbPodcastBlockComponent extends BaseComponent<ArticleCard[]> {
  podcastListUrl = 'podcast';
  readonly ArticleCardType = ArticleCardType;

  protected override setProperties(): void {
    super.setProperties();

    if (!this.data?.length) return;
    this.hasEbPodcastArticle(this.data);
  }

  hasEbPodcastArticle(articles: ArticleCard[]): void {
    this.podcastListUrl = articles?.some((article: ArticleCard) => article?.tags?.some((tag: Tag) => tag.slug === 'eb-podcast')) ? 'eb-podcast' : 'podcast';
  }
}
