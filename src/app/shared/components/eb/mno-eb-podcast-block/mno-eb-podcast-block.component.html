<ng-container *ngIf="data">
  <div class="eb-podcast-header">
    <div class="eb-podcast-title">Podcastok</div>
    <img class="eb-podcast-logo" src="/assets/images/eb-labda.svg" alt="" loading="lazy" />
  </div>

  <div class="eb-podcast-list">
    <article
      mno-article-card
      *ngFor="let article of data; trackBy: trackByFn"
      [data]="article"
      [asPodcast]="true"
      [styleID]="ArticleCardType.Podcast"
    ></article>
  </div>

  <a class="eb-podcast-link" [routerLink]="['/', 'cimke', podcastListUrl]"> Eb podcastok <i class="icon icon-chevron-right-white"></i> </a>
</ng-container>
