@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  color: var(--kui-white);
  background-color: var(--kui-brand-eb-blue);
  position: relative;
  padding: 24px 16px;

  .eb-podcast {
    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 20px;
      border-bottom: 1px solid var(--kui-white);
      padding-bottom: 16px;
    }

    &-list {
      display: flex;
      flex-direction: column;
      margin-top: 16px;
      gap: 16px;
    }

    &-title {
      font-size: 16px;
      font-weight: 700;
      line-height: 21px;
      letter-spacing: 0.16px;
    }

    &-logo {
      position: absolute;
      right: 0;
      top: 0;
      flex-shrink: 0;
    }

    &-link {
      color: var(--kui-white);
      padding: 6px 12px;
      display: flex;
      justify-content: center;
      gap: 8px;
      font-size: 14px;
      line-height: 18px;
      font-weight: 500;
      border-radius: 2px;
      border: 1px solid var(--kui-slate-50);
      margin-top: 16px;

      .icon-chevron-right-white {
        width: 16px;
        height: 16px;
      }
    }
  }

  ::ng-deep {
    .article-card {
      .article-label {
        display: block;
        white-space: normal;
        padding-right: 0 !important;
      }

      .article-link-wrapper {
        @include media-breakpoint-down(xl) {
          flex-direction: column;

          .article-thumbnail-figure {
            width: 100%;
            height: 100%;
          }
        }

        @include media-breakpoint-down(md) {
          flex-direction: row;

          .article-thumbnail-figure {
            width: 110px;
            height: 62px;
          }
        }
      }
    }
  }
}
