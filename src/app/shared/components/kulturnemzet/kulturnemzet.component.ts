import { Component, Input } from '@angular/core';
import { ArticleCard } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { RouterLink } from '@angular/router';
import { NgFor } from '@angular/common';
import { ArticleCardType } from '../../definitions';

@Component({
  selector: 'app-kulturnemzet',
  templateUrl: './kulturnemzet.component.html',
  styleUrls: ['./kulturnemzet.component.scss'],
  imports: [ArticleCardComponent, RouterLink, NgFor],
})
export class KulturnemzetComponent {
  @Input() set data(value: ArticleCard[]) {
    this.#data = value;
  }

  get data(): ArticleCard[] {
    return this.#data;
  }

  #data: ArticleCard[] = [];
  readonly cardType = ArticleCardType.ImgRightTagsTitleLead50;
}
