import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ApiService } from '../../../services';
import { combineLatest, Observable } from 'rxjs';
import { MedalTable } from '@trendency/kesma-ui/lib/components/olimpia/olimpia-medal-table/olimpia-medal-table.definitions';
import { map } from 'rxjs/operators';
import { OlimpiaMedalsNational, OlimpiaResultsComponent, OlimpiaResultsData, OlimpicPortalEnum } from '@trendency/kesma-ui';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-olimpia-results-block',
  templateUrl: './olimpia-results-block.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, NgIf, OlimpiaResultsComponent],
})
export class OlimpiaResultsBlockComponent {
  readonly portal: OlimpicPortalEnum = OlimpicPortalEnum.OlimpicMNO;
  countryMedals$: Observable<MedalTable[]> = this.apiService.getOlimpiaCountryMedals().pipe(map((res) => res.data ?? []));
  readonly nationalMedals$: Observable<OlimpiaMedalsNational[]> = this.apiService.getOlimpiaNationalMedals().pipe(
    map((res) => {
      if (res.data) {
        if (res.data.length > 5) {
          return res.data.slice(0, 5);
        } else {
          return res.data;
        }
      } else {
        return [];
      }
    })
  );

  readonly olimpiaResultsData$: Observable<OlimpiaResultsData> = combineLatest([this.countryMedals$, this.nationalMedals$]).pipe(
    map(([medalsTableData, nationalMedalsTableData]) => ({
      medalsTableData,
      nationalMedalsTableData,
    }))
  );

  constructor(private readonly apiService: ApiService) {}
}
