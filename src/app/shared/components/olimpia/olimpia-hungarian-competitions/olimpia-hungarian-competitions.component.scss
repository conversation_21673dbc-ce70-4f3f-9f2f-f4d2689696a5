@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  font-family: var(--kui-font-roboto-flex);

  .hungarian-competitions {
    &-header-container {
      display: flex;
      min-height: 64px;
      background-color: var(--kui-brand-olimpic-Indigo);
      padding: 0 20px 0 20px;
      border-bottom: 1px solid var(--kui-white);

      @include media-breakpoint-down(md) {
        padding: 24px 16px 0 16px;
        border-bottom: none;
      }
    }

    &-header {
      width: 100%;
      color: var(--kui-white);
      display: flex;
      justify-content: space-between;
      align-items: center;

      @include media-breakpoint-down(md) {
        border-bottom: 1px solid var(--kui-white);
        gap: 8px;
        padding-bottom: 16px;
      }

      &-title {
        font-weight: 700;
        font-size: 20px;
        line-height: 24px;
        letter-spacing: normal;

        @include media-breakpoint-down(md) {
          font-size: 16px;
          line-height: 21px;
          letter-spacing: 0.16px;
          max-width: 120px;
        }
      }

      &-logo {
        width: 155px;
        height: 24px;

        @include media-breakpoint-down(md) {
          width: 130px;
          height: 20px;
        }
      }
    }
  }

  .more-program {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    &-title {
      display: flex;
      width: 440px;
      min-height: 40px;
      align-items: center;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      color: var(--kui-brand-olimpic-turquoise);
      justify-content: center;
      line-height: 24px;
      border-radius: 2px;
      padding: 8px;
      border: 1px solid var(--kui-brand-olimpic-turquoise);
      gap: 8px;

      &:hover {
        background-color: var(--kui-brand-olimpic-turquoise);
        color: var(--kui-white);
      }
    }

    @include media-breakpoint-down(md) {
      padding: 24px 0;
    }
  }
}
