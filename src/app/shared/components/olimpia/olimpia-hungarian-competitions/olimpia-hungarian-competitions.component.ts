import { AsyncPipe, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterLink } from '@angular/router';
import {
  IconComponent,
  OlimpiaHungarianCompetitions,
  OlimpiaHungarianCompetitionsComponent as KesmaOlimpiaHungarianCompetitionsComponent,
  OlimpicPortalEnum,
} from '@trendency/kesma-ui';
import { formatDate } from 'date-fns';
import { map, Observable } from 'rxjs';
import { ApiService } from 'src/app/shared/services/api.service';

const DATE_FORMAT = 'yyyy-MM-dd';

export type CompetitionsByDay = {
  today: OlimpiaHungarianCompetitions[];
  upcoming: OlimpiaHungarianCompetitions[];
};

@Component({
  selector: 'app-olimpia-hungarian-competitions',
  templateUrl: './olimpia-hungarian-competitions.component.html',
  styleUrls: ['olimpia-hungarian-competitions.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, AsyncPipe, KesmaOlimpiaHungarianCompetitionsComponent, NgIf, IconComponent],
})
export class OlimpiaHungarianCompetitionsComponent {
  formattedTodayDate = formatDate(new Date(), DATE_FORMAT);
  upcomingDayDate: string = '';
  hungarianCompetitions$: Observable<CompetitionsByDay> = this.apiService
    .getOlimpiaCompetitions(this.formattedTodayDate)
    .pipe(map(({ data }) => this.filterTodayAndTomorrowEvents(data)));
  OlimpicPortalEnum = OlimpicPortalEnum;

  constructor(private readonly apiService: ApiService) {}

  filterTodayAndTomorrowEvents(events: OlimpiaHungarianCompetitions[]): CompetitionsByDay {
    if (!events?.length) {
      return { today: [], upcoming: [] };
    }

    const todayEvents = events.filter(
      (event: OlimpiaHungarianCompetitions) => formatDate(event?.startDate ?? new Date(), DATE_FORMAT) === this.formattedTodayDate
    );
    let nextEvents = events.filter(
      (event: OlimpiaHungarianCompetitions) => formatDate(event?.startDate ?? new Date(), DATE_FORMAT) !== this.formattedTodayDate
    );
    this.upcomingDayDate = formatDate(nextEvents[0]?.startDate ?? new Date(), DATE_FORMAT);
    if (this.upcomingDayDate !== this.formattedTodayDate) {
      nextEvents = events.filter((event: OlimpiaHungarianCompetitions) => formatDate(event?.startDate ?? new Date(), DATE_FORMAT) === this.upcomingDayDate);
    }
    return { today: todayEvents, upcoming: nextEvents };
  }
}
