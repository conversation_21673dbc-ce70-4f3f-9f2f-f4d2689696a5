<div class="result-bar">
  <hr />
  <h1>Podcastek</h1>
  <hr />
</div>

<div [class.flex-column]="(isMobile$ | async) || desktopWidth < 8" class="top-container d-flex">
  <ng-container *ngFor="let article of articles | slice: 0 : 3; let i = index">
    <article
      [asPodcast]="true"
      [data]="article"
      [showThumbnail]="!!article?.thumbnailUrl43 || !!article?.thumbnailUrl || !!article?.thumbnail?.url"
      [styleID]="styleID"
      mno-article-card
    ></article>
  </ng-container>
</div>
