import { ChangeDetectionStrategy, Component, HostBinding, inject, Input } from '@angular/core';
import { ArticleCard } from '@trendency/kesma-ui';
import { ArticleCardComponent } from '../article-card/article-card.component';
import { As<PERSON><PERSON><PERSON><PERSON>, Ng<PERSON>or, SlicePipe } from '@angular/common';
import { ArticleCardType } from '../../definitions';
import { map, Observable } from 'rxjs';
import { BreakpointObserver } from '@angular/cdk/layout';

const MOBILE_BREAKPOINT = '(max-width: 768px)';

@Component({
  selector: 'app-podcast-box',
  imports: [ArticleCardComponent, SlicePipe, NgFor, AsyncPipe],
  templateUrl: './podcast-box.component.html',
  styleUrls: ['./podcast-box.component.scss', '../module-heading/module-heading.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PodcastBoxComponent {
  readonly ArticleCardType = ArticleCardType;
  styleID = ArticleCardType.Img16TopTagsTitleLeadBadge;

  private _desktopWidth = 12;
  isMobile = false;
  @HostBinding('class') hostClass = 'black';

  @Input() articles: ArticleCard[] = [];
  @Input() set desktopWidth(desktopWidth: number) {
    if (desktopWidth === 12) {
      this.hostClass += ' full-width';
    }
    this._desktopWidth = desktopWidth;
  }

  get desktopWidth(): number {
    return this._desktopWidth;
  }
  breakpointObserver = inject(BreakpointObserver);

  isMobile$: Observable<boolean> = this.breakpointObserver.observe(MOBILE_BREAKPOINT).pipe(map((bs) => bs.matches));
}
