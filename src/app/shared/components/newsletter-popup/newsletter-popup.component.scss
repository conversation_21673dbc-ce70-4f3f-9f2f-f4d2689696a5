@use 'shared' as *;

.newsletter-box {
  display: none;
  position: fixed;
  bottom: 0;
  right: 110px;
  width: 300px;
  height: 240px;
  padding: 16px 16px 0;
  background-color: var(--kui-blue-800);
  color: var(--kui-white);
  text-align: center;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  overflow: hidden;

  @include media-breakpoint-down(sm) {
    width: 100vw;
    left: 0;
  }

  .description {
    font-family: var(--kui-font-primary);
    font-weight: 700;
    font-size: 16px;
    line-height: 21px;
    margin-bottom: 16px;
    color: var(--kui-white);
  }

  .signup-button {
    display: flex;
    padding: 14px;
    justify-content: center;
    align-content: center;
    font-weight: 700;
    font-size: 14px;
    line-height: 18px;
    height: 50px;
    width: 240px;
  }

  .close-button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 50px;
    font-family: var(--kui-font-primary);
    font-size: 14px;
    line-height: 18px;
    color: var(--kui-white);
    text-align: center;
    font-weight: 400;
    padding: 14px;
    width: 240px;
  }

  &.open {
    z-index: 10;
    display: flex;
    animation: 1s slide-up;
  }

  @keyframes slide-up {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }
}
