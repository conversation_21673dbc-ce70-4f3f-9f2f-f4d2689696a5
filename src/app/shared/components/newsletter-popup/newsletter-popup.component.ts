import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { StorageService, UtilService } from '@trendency/kesma-core';
import { AnalyticsService, NEWSLETTER_COMPONENT_TYPE } from '@trendency/kesma-ui';
import { IconComponent } from '../icon/icon.component';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-newsletter-popup',
  templateUrl: './newsletter-popup.component.html',
  styleUrls: ['./newsletter-popup.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent, NgClass],
})
export class NewsletterPopupComponent implements OnInit {
  isPopupOpen = false;

  constructor(
    private readonly storageService: StorageService,
    private readonly analyticsService: AnalyticsService,
    private readonly utilService: UtilService,
    private readonly cd: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    if (this.utilService.isBrowser()) {
      const newsletterClosedCookie = this.storageService.getCookie('newsletterClosed');
      if (!newsletterClosedCookie) {
        setTimeout(() => {
          this.isPopupOpen = true;
          this.analyticsService.newsLetterPopupVisible();
          this.cd.detectChanges();
        }, 2000);
      }
    }
  }

  onCloseClicked(): void {
    this.isPopupOpen = false;
    this.setNewsletterClosedCookie();
  }

  onSubscribeClicked(): void {
    this.setNewsletterClosedCookie();
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.POPOVER);
  }

  private setNewsletterClosedCookie(): void {
    const now: Date = new Date();
    const nextWeek = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 7);
    const expiresInSecond: number = (nextWeek.getTime() - now.getTime()) / 1000;
    this.storageService.setCookie('newsletterClosed', true, expiresInSecond);
  }
}
