import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  ElementRef,
  EventEmitter,
  HostBinding,
  Input,
  Output,
  Signal,
  ViewChild,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FullscreenDirective, UtilService } from '@trendency/kesma-core';
import {
  AdultOverlayComponent,
  ClickStopPropagationDirective,
  ExtendedSwiperContainer,
  SliderGalleryComponent as KesmaSliderGalleryComponent,
} from '@trendency/kesma-ui';
import { IconComponent } from '../icon/icon.component';
import { SocialShareComponent } from '../social-share/social-share.component';
import { AsyncPipe, NgFor, NgIf, NgTemplateOutlet } from '@angular/common';
import { DateFnsModule } from 'ngx-date-fns';

@Component({
  selector: 'mno-slider-gallery',
  templateUrl: './slider-gallery.component.html',
  styleUrls: ['./slider-gallery.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    SocialShareComponent,
    IconComponent,
    NgFor,
    NgTemplateOutlet,
    AsyncPipe,
    FullscreenDirective,
    DateFnsModule,
    AdultOverlayComponent,
    ClickStopPropagationDirective,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SliderGalleryComponent extends KesmaSliderGalleryComponent implements AfterViewInit {
  @ViewChild('swiperThumbs', { read: ElementRef, static: false }) swiperThumbs?: ExtendedSwiperContainer;
  @Input() showTitle = false;
  @HostBinding('class') hostClass = 'gallery';
  @Output() onFullSizeGallery = new EventEmitter<void>();
  @Output() navigateToGallery = new EventEmitter<number>();
  @Input() @HostBinding('class.fullsize') fullscreenGallery = false;
  @Input() isMobile = false;
  isFullScreen = true;

  readonly selectedIndex: Signal<number | undefined> = toSignal<number | undefined>(this.realIndex$);
  readonly selectedImage = computed(() => {
    return this.data?.images?.[this.selectedIndex() || 0];
  });

  swiperThumbsBreakpoints = {
    1024: { slidesPerView: 6, slidesPerGroup: 6 },
    640: { slidesPerView: 4, slidesPerGroup: 4 },
    300: { slidesPerView: 3, slidesPerGroup: 3 },
  };

  constructor(protected override readonly utilService: UtilService) {
    super(utilService);
  }

  get url(): string {
    return '/galeria/' + this.data?.slug;
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
    if (this.activeIndex > 0 && this.swiperRef()) {
      this.swiperRef()?.slideTo(this.activeIndex, 0);
    }
  }

  public goBack(): void {
    this.isFullScreen = false;
    this.onCloseGallery.emit(this.activeIndex);
  }

  public onToggleFullScreen(fullScreenDirective: FullscreenDirective): void {
    this.isFullScreen = !this.isFullScreen;
    fullScreenDirective.toggleFullScreen();
  }

  override onIndexChange(): void {
    super.onIndexChange();
    this.onSelectedIndexChanged.emit(this.realIndex);
  }

  goToSlide(index: number): void {
    this.swiperRef()?.slideToLoop(index);
    this.cdr.detectChanges();
    this.onSelectedIndexChanged.emit(index);
  }
}
