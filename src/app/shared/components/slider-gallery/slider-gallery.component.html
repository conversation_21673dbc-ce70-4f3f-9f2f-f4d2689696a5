<section #fullScreen="trFullscreen" *ngIf="data as gallery" class="gallery" trFullscreen>
  <div class="wrapper d-flex flex-column">
    <div *ngIf="fullscreenGallery" class="gallery-layer-header d-flex justify-content-between align-items-start">
      <div class="logo">
        <img alt="Magyar Nemzet logo világosszürke" height="44" src="/assets/images/logo-mno-lightgray.svg" width="152" />
      </div>
      <div class="gallery-layer-info d-flex flex-column">
        <h1 class="gallery-title">
          {{ gallery?.title }}
        </h1>
        <div class="gallery-layer-meta d-flex align-items-center justify-content-between">
          <div class="left d-flex align-items-center justify-content-start">
            <span *ngIf="selectedImage()?.title" class="gallery-layer-meta-title">{{ selectedImage()?.title }}</span>
            <span *ngIf="selectedImage()?.photographer || selectedImage()?.source" class="gallery-layer-meta-photographer"
              >Fotó: {{ selectedImage()?.photographer || selectedImage()?.source }}</span
            >
            <span class="gallery-layer-meta-date">
              {{ gallery?.publishDate | dfnsFormat: 'Pp' }}
            </span>
          </div>
          <mno-social-share [isMobile]="isMobile" [link]="[url]" [title]="gallery?.title || ''"></mno-social-share>
        </div>
      </div>
      <div class="gallery-layer-top-icons d-flex justify-content-end">
        <button (click)="onToggleFullScreen(fullScreen)" class="gallery-layer-top-icons-item desktop-only" type="button">
          <img [size]="32" alt="Teljes képernyő ikon" color="white" mno-icon="arrow-minimize" />
        </button>

        <button (click)="goBack()" class="gallery-layer-top-icons-item" type="button">
          <img [size]="32" alt="Bezárás ikon" color="white" mno-icon="dismiss" />
        </button>
      </div>
    </div>
    <div class="swiper-wrapper">
      <swiper-container
        #swiper
        allow-touch-move="true"
        auto-height="true"
        center-insufficient-slides="true"
        centered-slides="true"
        class="main"
        id="top"
        init="false"
        loop="true"
        slides-per-view="auto"
        space-between="0"
        thumbs-swiper="#bottom"
        [initialSlide]="realIndex$ | async"
      >
        <swiper-slide *ngFor="let image of gallery.images; trackBy: trackByFn; let i = index">
          <div *ngIf="image" class="gallery-slide">
            @if (data?.isAdult && !isAcceptedAdultContent && !isInsideAdultArticleBody) {
              <kesma-adult-overlay>
                <ng-container *ngTemplateOutlet="galleryImage"></ng-container>
                <button class="adult-btn" (click)="acceptAdultContent()" clickStopPropagation custom-overlay-content>Megnézem</button>
              </kesma-adult-overlay>
            } @else {
              <ng-container *ngTemplateOutlet="galleryImage"></ng-container>
            }
            <ng-template #galleryImage>
              <img
                (click)="navigateToGallery.emit(i)"
                [alt]="image.altText || image.title"
                [src]="image.url?.fullSize || 'assets/placeholder.svg'"
                [style.height.px]="swiperHeight"
                class="gallery-slide-image"
                loading="lazy"
              />
            </ng-template>
          </div>
        </swiper-slide>
      </swiper-container>
      <button (click)="swipePrev()" class="swiper-prev left">
        <img alt="Nyíl ikon" color="white" mno-icon="chevron-left" />
      </button>
      <button (click)="swipeNext()" class="swiper-next right">
        <img alt="Nyíl ikon" color="white" mno-icon="chevron-right" />
      </button>
    </div>
    <div class="gallery-details">
      <div class="gallery-details-inner">
        <span class="gallery-details-image-count">{{ ((realIndex$ | async) ?? 0) + 1 }}/{{ gallery.images.length }}</span>
        <span class="gallery-details-caption">
          {{ selectedImage()?.caption || selectedImage()?.title || gallery?.title }}
        </span>
      </div>
    </div>

    <div *ngIf="fullscreenGallery" class="swiper-bottom-wrapper">
      <swiper-container
        #swiperThumbs
        [breakpoints]="swiperThumbsBreakpoints"
        [initialSlide]="realIndex$ | async"
        class="thumbs"
        id="bottom"
        slides-per-group="6"
        slides-per-view="6"
        space-between="24"
        free-mode="true"
        watch-slides-progress="true"
      >
        <swiper-slide *ngFor="let image of gallery?.images; trackBy: trackByFn; let i = index">
          <article (click)="goToSlide(i)" *ngIf="image" class="slider-slide-container">
            <img alt="{{ image?.altText ?? image?.title }}" loading="lazy" src="{{ image?.url?.thumbnail }}" />
          </article>
        </swiper-slide>
      </swiper-container>
    </div>
  </div>
</section>
