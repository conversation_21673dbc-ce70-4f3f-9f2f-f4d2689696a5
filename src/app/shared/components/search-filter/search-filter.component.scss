@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  color: var(--kui-black);
  font-family: var(--kui-font-primary);
  margin-bottom: 30px;
  width: 100%;

  gap: 40px;

  @include media-breakpoint-down(sm) {
    gap: 20px;
    margin-bottom: 20px !important;
  }

  .search {
    &-by-keyword-input {
      border: 1px solid var(--kui-slate-950);
      border-right-width: 0;
      padding-inline: 10px 8px;
      border-radius: 2px 0 0 2px;

      @include media-breakpoint-down(sm) {
        font-size: 14px;
        padding-inline: 16px 32px;
      }

      &::placeholder {
        color: var(--kui-slate-500);
        font-weight: 500;
        line-height: 22px;
      }

      &-wrapper {
        .close-icon {
          position: absolute;
          top: 50%;
          right: 0;
          transform: translate(-50%, -50%);
          cursor: pointer;
          min-width: 50px;
          opacity: 0.5;

          @include media-breakpoint-down(sm) {
            width: 18px;
            height: 18px;
          }
        }
      }
    }

    &-submit {
      line-height: 20px;
      padding: 8px 8px;
      min-width: 192px;

      @include media-breakpoint-down(sm) {
        padding: 7px 16px;
        min-width: 48px;
      }
    }
  }

  ::ng-deep {
    app-search-filter-api-select::ng-deep .ng-select,
    .ng-select {
      &-container {
        padding: 10px 16px;
        height: 40px !important;
        min-height: auto;
        border: 1px solid var(--kui-slate-950) !important;
        border-radius: 2px;

        @include media-breakpoint-down(sm) {
          max-width: 100%;
          height: 32px !important;
        }

        .ng-value {
          &-container {
            padding: 0;
          }

          &-label {
            color: var(--kui-slate-950);
          }
        }
      }

      .ng-option {
        background-color: var(--kui-white) !important;
        border-radius: 0 !important;

        &-selected,
        &:hover {
          background-color: var(--kui-slate-950) !important;
        }

        &-label {
          font-size: 14px;
        }
      }

      &-bottom,
      &-top {
        border-color: var(--kui-slate-950) !important;
      }
    }
  }
}

// COMMON
.search {
  &-by-keyword {
    display: flex;
  }

  &-by-keyword-input {
    height: 42px;
    width: 100%;

    &-wrapper {
      position: relative;
      width: 100%;
    }
  }

  &-submit {
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    text-align: center;
  }

  &-filters {
    display: flex;
    gap: 20px;
    justify-content: space-between;

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      width: 100%;
      gap: 12px !important;
    }

    &-item {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex: 1;
      max-width: 200px;

      @include media-breakpoint-down(sm) {
        max-width: none;
      }

      &.tag-filter {
        border: 1px solid var(--kui-slate-950);
        border-radius: 2px;
        font-size: 14px;
        font-weight: 500;
        line-height: 28px;
        align-items: center;
        justify-content: center;

        @include media-breakpoint-down(sm) {
          line-height: 30px;
        }

        &.active {
          color: var(--kui-white);
          background: var(--kui-slate-950);
        }
      }
    }
  }
}

::ng-deep {
  .ng-select {
    &-container {
      background-color: var(--kui-white) !important;
      cursor: pointer !important;

      .ng-arrow-wrapper {
        @include icon('icons/chevron-down-dark.svg');
        height: 24px;
        width: 24px;
        transition: 0.3s;

        .ng-arrow {
          display: none !important;
        }
      }
    }

    &-opened {
      .ng-arrow-wrapper {
        transform: rotate(-180deg);
      }
    }

    .ng-value {
      &-label {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  .ng-option {
    &-selected,
    &:hover {
      color: var(--kui-white) !important;
    }
  }
}
