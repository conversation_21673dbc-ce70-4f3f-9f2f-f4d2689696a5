import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, HostBinding, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { format } from 'date-fns';
import { ApiService, CategoryService, SearchFilterService } from '../../services';
import { articleTypeFilters, publishFilters, SEARCH_FILTER_DATE_FORMAT, sortOptions } from '../../utils';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { IHttpOptions } from '@trendency/kesma-core';
import { NgIf } from '@angular/common';
import { SearchFilterApiSelectComponent } from '../search-filter-api-select/search-filter-api-select.component';
import { IconComponent } from '../icon/icon.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-search-filter',
  templateUrl: 'search-filter.component.html',
  styleUrls: ['search-filter.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, SearchFilterApiSelectComponent, IconComponent, NgSelectModule, FormsModule],
})
export class SearchFilterComponent implements OnInit, OnDestroy {
  @HostBinding('class') hostClass = 'search';
  @Output() filterEvent = new EventEmitter<Record<string, string>>();
  @Input() showPublishDateFilter = true;
  @Input() showContentTypeFilter = true;
  @Input() showColumnFilter = true;
  @Input() showAuthorFilter = true;
  @Input() showOnlyInTagsFilter = true;
  @Input() showPublishDateSort = false;

  readonly articleTypeFilters = articleTypeFilters;
  readonly publishFilters = publishFilters;
  readonly sortOptions = sortOptions;

  searchOnlyInTags = false;
  searchFilter: Record<string, string> = {
    global_filter: '',
    from_date: '',
    'columnSlugs[]': '',
    'content_types[]': '',
    'tagSlugs[]': '',
    author: '',
    'publishDate_order[]': 'desc',
  };

  selectedPublishDate = publishFilters[0];
  selectedArticleType = articleTypeFilters[0];
  selectedSortOption = sortOptions[0];

  destroy$: Subject<void> = new Subject<void>();

  selectedColumnSlug = '';
  selectedAuthorSlug = '';

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly searchFilterService: SearchFilterService,
    private readonly categoryService: CategoryService,
    private readonly api: ApiService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  @Input() set styleId(styleId: string) {
    this.hostClass = styleId;
  }

  @Input() set contentTypeFilter(contentTypeFilter: string) {
    this.searchFilter['content_types[]'] = contentTypeFilter;
    this.onSearch();
  }

  private get searchFilters(): Record<string, string> {
    const filters: Record<string, string> = {};
    Object.entries(this.searchFilter).forEach((entry) => {
      const [key, value] = entry;
      if (value) {
        filters[key] = value;
      }
      if (value && key === 'from_date') {
        filters['to_date'] = format(new Date(), SEARCH_FILTER_DATE_FORMAT);
      }
    });
    return filters;
  }

  columnSourceRequest = (options: IHttpOptions): any => {
    return this.searchFilterService.getColumnsRequest(options);
  };

  columnSingleItemRequest = (slug: string): any => {
    return this.categoryService.getColumn(slug);
  };

  authorsSourceRequest = (options: IHttpOptions): any => {
    return this.api.getPublicAuthors('', options);
  };

  authorSingleItemRequest = (slug: string): any => {
    return this.api.getPublicAuthorSocial(slug).pipe(
      // Need to map the response as the DTO is different, and it does not contain the public_author_name
      map((res) => ({
        ...res,
        data: { ...res.data, public_author_name: res.data.publicAuthorName },
      }))
    );
  };

  ngOnInit(): void {
    this.getRouteParams();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSearch(): void {
    this.setRouteParams();
    this.filterEvent.emit(this.searchFilters);
  }

  onSearchOnlyInTags(): void {
    this.searchOnlyInTags = !this.searchOnlyInTags;
  }

  onClearSearch(): void {
    this.searchFilter['global_filter'] = '';
  }

  getRouteParams(): void {
    this.route.data.pipe(takeUntil(this.destroy$)).subscribe(() => {
      const params = this.route.snapshot.queryParams;
      this.searchFilter = { ...this.searchFilter, ...params };

      if (this.route.snapshot.params['categorySlug']?.length > 0) {
        const categorySlug = this.route.snapshot.params['categorySlug'];
        this.searchFilter = {
          ...this.searchFilter,
          'columnSlugs[]': params['columnSlugs[]'] || categorySlug,
        };
        this.selectedColumnSlug = categorySlug;
      }
      if (this.route.snapshot.params['authorSlug']?.length > 0) {
        const authorSlug = this.route.snapshot.params['authorSlug'];
        this.searchFilter = {
          ...this.searchFilter,
          author: authorSlug,
        };
        this.selectedAuthorSlug = authorSlug;
      }

      const tagSlug = this.route.snapshot.params['tagName'] || this.route.snapshot.params['tag'];
      if (tagSlug && tagSlug.length > 0) {
        this.searchFilter = {
          ...this.searchFilter,
          'tagSlugs[]': tagSlug,
          global_filter: tagSlug,
        };
        this.searchOnlyInTags = true;
      }
      Object.entries(params).forEach(([paramKey, paramValue]) => {
        switch (paramKey) {
          case 'from_date':
            this.selectedPublishDate = publishFilters.find((filter) => filter.value === paramValue) || publishFilters[0];
            break;
          case 'columnSlugs[]':
            this.selectedColumnSlug = paramValue;
            break;
          case 'content_types[]':
            this.selectedArticleType = articleTypeFilters.find((filter) => filter.value === paramValue) || (articleTypeFilters[0] as any);
            break;
          case 'tagSlugs[]':
            this.searchFilter['global_filter'] = params['tagSlugs[]'];
            this.searchOnlyInTags = true;
            break;
          case 'author':
            this.selectedAuthorSlug = paramValue;
            break;
          case 'publishDate_order[]':
            this.selectedSortOption = sortOptions.find((filter) => filter.value === paramValue) || sortOptions[0];
            break;
        }
      });
      this.cdr.markForCheck();
    });
  }

  setRouteParams(): void {
    const queryParams = this.searchFilter;

    // Set tagSlugs[]
    queryParams['tagSlugs[]'] = this.searchOnlyInTags ? this.searchFilter['global_filter'] : '';

    // Remove empty props
    Object.entries(queryParams).forEach(([key, value]) => {
      if (!value) delete queryParams[key];
    });

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
    });
  }
}
