@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  border: 1px solid var(--kui-blue-950);
  border-bottom: 0;
  border-radius: 3px;
  width: 300px;
  text-align: center;
  align-items: center;
  justify-content: flex-start;
  @include media-breakpoint-down(sm) {
    width: 100%;
  }
}

.newspaper {
  &-container {
    padding: 7px 16px 0;
  }

  &-logo {
    height: 30px;
    border-bottom: 3px solid var(--kui-blue-950);
    margin-bottom: 2px;
    width: 100%;
    padding-bottom: 4px;
  }

  &-description {
    border-top: 1px solid var(--kui-blue-950);
    padding: 8px 0;
    font-weight: 500;
    font-size: 14px;
    line-height: 18px;
  }

  &-subscribe-button {
    width: calc(100% + 2px);
    margin: 0 -1px;
  }
}
