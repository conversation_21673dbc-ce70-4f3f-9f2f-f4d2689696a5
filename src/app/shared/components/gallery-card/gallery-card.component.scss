@use 'shared' as *;

:host {
  &.default {
    display: flex;
    justify-content: space-between;
    gap: 40px;

    &:hover {
      .gallery-card {
        &-title {
          color: var(--kui-blue-800);
        }
      }
    }

    @include media-breakpoint-down(sm) {
      flex-direction: column-reverse;
      gap: 12px;
    }

    .tag-with-date {
      align-items: center;
      flex-direction: row-reverse;
      justify-content: flex-end;
      margin-bottom: 4px;
      gap: 12px;
    }

    .gallery-card {
      &-content {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      &-title {
        display: inline;
        color: var(--kui-slate-950);
      }

      &-content {
        gap: 8px;
      }

      &-tag {
        color: var(--kui-blue-800);
        font-size: 14px;
        line-height: 18px;
        font-family: var(--kui-font-primary);
      }

      &-thumbnail {
        flex: 0 0 auto;
      }

      &-thumbnail-img {
        width: 200px;
        height: 150px;
        object-fit: cover;
        aspect-ratio: 16 / 9;

        @include media-breakpoint-down(sm) {
          width: 100%;
          height: auto;
        }
      }

      &-tag {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }
}
