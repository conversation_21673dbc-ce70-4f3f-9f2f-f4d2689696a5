import { ChangeDetectionStrategy, Component, HostBinding, inject, Input, OnInit } from '@angular/core';
import { AdultOverlayComponent, BaseComponent, GalleryData } from '@trendency/kesma-ui';
import { GalleryCardTypes } from '../../definitions';
import { StorageService } from '@trendency/kesma-core';
import { NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'mno-gallery-card',
  templateUrl: './gallery-card.component.html',
  styleUrls: ['./gallery-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgTemplateOutlet, AdultOverlayComponent],
})
export class GalleryCardComponent extends BaseComponent<GalleryData> implements OnInit {
  @Input() set styleID(styleID: GalleryCardTypes) {
    this.hostClass = styleID;
  }

  @HostBinding('class') hostClass: GalleryCardTypes = GalleryCardTypes.DEFAULT;
  @Input() withMargin = false;
  @Input() isNarrow = false;
  @Input() isInsideAdultArticleBody = false;

  isAdultChoice = false;

  private readonly storage = inject(StorageService);

  override ngOnInit(): void {
    super.ngOnInit();
    this.isAdultChoice = this.storage.getSessionStorageData('isAdultChoice') ?? false;
  }
}
