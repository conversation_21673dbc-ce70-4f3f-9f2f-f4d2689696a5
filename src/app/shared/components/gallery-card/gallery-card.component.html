<div class="gallery-card-content">
  <div class="tag-with-date">
    <span class="gallery-card-tag"> Galéria </span>
  </div>
  <a class="gallery-card-title" [routerLink]="['/', 'galeria', data?.slug, '1']">
    <h3>{{ data?.title }}</h3>
  </a>
</div>
<div class="gallery-card-thumbnail">
  @if (data?.isAdult && !isAdultChoice && !isInsideAdultArticleBody) {
    <kesma-adult-overlay>
      <ng-container *ngTemplateOutlet="galleryImage"></ng-container>
    </kesma-adult-overlay>
  } @else {
    <ng-container *ngTemplateOutlet="galleryImage"></ng-container>
  }
</div>

<ng-template #galleryImage>
  <img [alt]="data?.title ?? ''" [src]="data?.highlightedImageUrl || './assets/placeholder.svg'" class="gallery-card-thumbnail-img" loading="lazy" />
</ng-template>
