<div class="top-wrapper">
  <nav class="header-top">
    <div class="header-top-left">
      <button (click)="closeMenu()" [routerLink]="'/kereses'" class="header-menu-button">
        <img [src]="'assets/images/icons/search-' + (!isDark ? 'dark' : 'white') + '.svg'" alt="Nagyító ikon" />
      </button>
      <a (click)="onSubscribeClick($event)" [routerLink]="subscribeLink" class="header-menu-button desktop-only" title="Feliratkozom a hírlevélre">
        <img [src]="'assets/images/icons/mail-' + (!isDark ? 'dark' : 'white') + '.svg'" alt="Boríték ikon" />
      </a>
    </div>
    <nav *ngIf="trendingTags.length > 0" class="header-trending-tag-list only-on-stuck desktop-only">
      <a
        *ngFor="let tag of trendingTags | slice: 0 : 3; last as isLast"
        [class.last]="isLast"
        [routerLink]="['/', 'cimke', tag.slug]"
        class="header-trending-tag-list-item"
      >
        {{ tag.title | uppercase }}
      </a>
    </nav>

    <a (click)="closeMenu()" [routerLink]="'/'">
      <img [src]="'assets/images/logo-mno-' + (!isDark ? 'dark' : 'slightblue') + '.svg'" alt="Magyar Nemzet logo" class="header-logo" />
    </a>

    <nav *ngIf="trendingTags.length > 0" class="header-trending-tag-list only-on-stuck desktop-only">
      <a
        *ngFor="let tag of trendingTags | slice: 3 : 6; last as isLast"
        [class.last]="isLast"
        [routerLink]="['/', 'cimke', tag.slug]"
        class="header-trending-tag-list-item"
      >
        {{ tag.title | uppercase }}
      </a>
    </nav>

    <div class="header-top-right">
      <a
        (click)="closeMenu()"
        class="header-menu-button desktop-only"
        href="https://lapcentrum.hu/napilapok/12-magyar-nemzet.html"
        target="_blank"
        title="Előfizetek az újságra"
      >
        <img [src]="'assets/images/icons/news-' + (!isDark ? 'dark' : 'white') + '.svg'" alt="Újság ikon" />
      </a>
      <button (click)="toggleMenu()" [title]="isMenuOpen ? 'Bezárás' : 'Menü megnyitása'" class="header-menu-button hamburger">
        <img *ngIf="!isMenuOpen" [src]="'assets/images/icons/navigation-' + (!isDark ? 'dark' : 'white') + '.svg'" alt="Menü ikon" />
        <img *ngIf="isMenuOpen" [src]="'assets/images/icons/dismiss-' + (!isDark ? 'dark' : 'white') + '.svg'" alt="Bezárás ikon" class="mobile-close" />
      </button>
    </div>
  </nav>
</div>

<ng-container *ngIf="isMenuOpen || true">
  <nav [class.open]="isMenuOpen" class="hamburger-menu">
    <div class="hamburger-menu-header">
      <img [src]="'assets/images/logo-mno-slightblue.svg'" alt="Magyar Nemzet logo világosszürke" class="hamburger-menu-header-logo" />
      <button (click)="toggleMenu()" class="close-button">
        <img [src]="'assets/images/icons/dismiss-white.svg'" alt="Bezárás" />
      </button>
    </div>
    <ul>
      <li>
        <!--form (ngSubmit)="onSearchSubmit($event)">
          <input type="search" name="mobileSearch" [(ngModel)]="searchText" class="hamburger-menu-search" placeholder="  Keresés">
          <img type="submit" mno-icon="search" color="white">
        </form-->
        <button (click)="closeMenu()" [routerLink]="['/', 'kereses']" class="btn btn-ghost btn-ghost-white-transparent">
          <i class="icon icon-search" color="white" type="submit"></i>
          Keresés
        </button>
      </li>
    </ul>
    <ng-container *ngIf="trendingTags.length > 0">
      <ul>
        <li *ngFor="let tag of trendingTags | slice: 0 : 5">
          <a (click)="closeMenu()" [routerLink]="['/', 'cimke', tag.slug]" class="header-menu-item highlighted">{{ tag.title }}</a>
        </li>
      </ul>
    </ng-container>
    <nav>
      <ul>
        <li *ngFor="let menuItem of mainMenu; let index = index">
          <ng-container *ngIf="(menuItem.children ?? []).length > 0; else link">
            <ng-container *ngTemplateOutlet="parent; context: { item: menuItem, index: index }"></ng-container>
          </ng-container>
          <ng-template #link>
            <ng-container *ngTemplateOutlet="!menuItem.isCustomUrl ? basicLink : customUrl; context: { item: menuItem, index: index }"></ng-container>
          </ng-template>
        </li>
        <li *ngFor="let menuItem of menuItems.buttons">
          <a (click)="closeMenu()" *ngIf="!menuItem.isExternal; else external" [routerLink]="menuItem.link" class="header-menu-item header-menu-item-button">
            <i [ngClass]="'icon-' + menuItem.icon" class="icon"></i>
            {{ menuItem.title }}
          </a>
          <ng-template #external>
            <a (click)="closeMenu()" [href]="menuItem.link" class="header-menu-item header-menu-item-button" target="_blank">
              <i [ngClass]="'icon-' + menuItem.icon" class="icon"></i>
              {{ menuItem.title }}
            </a>
          </ng-template>
        </li>
        <li>
          <mno-social-block
            (click)="closeMenu()"
            [data]="menuItems.social"
            [withBorder]="true"
            title="Közösségi média oldalak és csatornáink"
          ></mno-social-block>
        </li>
        <li *ngFor="let menuItem of menuItems.links">
          <button (click)="onMenuItemClick(menuItem, $event)" *ngIf="menuItem.click; else link" class="header-menu-item header-menu-item-link">
            {{ menuItem.title }}
          </button>
          <ng-template #link>
            <a (click)="closeMenu()" *ngIf="!menuItem.isExternal; else external" [routerLink]="menuItem.link" class="header-menu-item header-menu-item-link">
              {{ menuItem.title }}
            </a>
            <ng-template #external>
              <a (click)="closeMenu()" [href]="menuItem.link" class="header-menu-item header-menu-item-link" target="_blank">
                {{ menuItem.title }}
              </a>
            </ng-template>
          </ng-template>
        </li>
      </ul>
    </nav>
  </nav>
  <div class="menu-overlay-"></div>
</ng-container>

<nav *ngIf="trendingTags.length > 0" class="header-trending-tag-list hidden-on-stuck">
  <a
    (click)="closeMenu()"
    *ngFor="let tag of trendingTags; last as isLast"
    [class.last]="isLast"
    [routerLink]="['/', 'cimke', tag.slug]"
    class="header-trending-tag-list-item"
  >
    {{ tag.title | uppercase }}
  </a>
</nav>
<div class="header-bottom"></div>

<ng-content select="[eb-header]"></ng-content>
<ng-content select="[olimpia-header]"></ng-content>

<section
  (onClose)="onBreakingCloseClick()"
  *ngIf="breaking && breakingAllowed"
  [color]="breakingColor"
  [data]="breaking"
  [labelText]="breakingText"
  mno-breaking-strip
></section>

<!-- Link templates -->
<ng-template #parent let-index="index" let-item="item">
  <h2 (click)="selectParent(index)" [class.active]="selectedParentIndex === index" class="header-menu-item">
    {{ item.title }}
    <span class="icon icon-chevron"></span>
  </h2>
  <ul [class.open]="selectedParentIndex === index" class="header-menu-item-submenu">
    <li *ngFor="let child of item.children">
      <ng-container *ngTemplateOutlet="!child.isCustomUrl ? basicLink : customUrl; context: { item: child, index: index }"></ng-container>
    </li>
  </ul>
</ng-template>

<ng-template #basicLink let-item="item">
  @if (item.relatedType === RelatedType.COLUMN && item?.related?.sponsorship; as sponsorship) {
    <a (click)="closeMenu()" [routerLink]="item.link" [target]="item.target" class="header-menu-item-with-logo"
      ><img [src]="sponsorship?.logo" loading="lazy" alt="" /> {{ item.title }}
    </a>
  } @else {
    <a (click)="closeMenu()" [routerLink]="item.link" [target]="item.target" class="header-menu-item"> {{ item.title }} </a>
  }
</ng-template>

<ng-template #customUrl let-item="item">
  <a (click)="closeMenu()" [href]="item.link" [target]="item.target" class="header-menu-item"> {{ item.title }} </a>
</ng-template>
