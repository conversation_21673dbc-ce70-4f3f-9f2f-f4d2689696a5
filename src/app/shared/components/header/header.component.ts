import { ChangeDetectionStrategy, Component, EventEmitter, HostBinding, Input, Output } from '@angular/core';
import { BaseComponent, BreakingBlock, RelatedType, SimplifiedMenuItem, TrendingTag } from '@trendency/kesma-ui';
import { StaticMenuItem } from '../../definitions';
import { buildSocialMenu, DEFAULT_SUBSCRIBE_LINK } from '../../utils';
import { BreakingStripComponent } from '../breaking-strip/breaking-strip.component';
import { SocialBlockComponent } from '../social-block/social-block.component';
import { NgClass, NgFor, NgIf, NgTemplateOutlet, SlicePipe, UpperCasePipe } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: '[mno-header]',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgIf, <PERSON>For, NgTemplateOutlet, Ng<PERSON>lass, SocialBlockComponent, BreakingStripComponent, UpperCasePipe, SlicePipe],
})
export class HeaderComponent extends BaseComponent<SimplifiedMenuItem[]> {
  @Input() breaking?: BreakingBlock;
  @Input() breakingColor = 'red';
  @Input() breakingText = 'Rendkívüli';
  @Input() breakingAllowed = true;

  @Input()
  @HostBinding('class.is-dark')
  isDark = false;
  @Input() trendingTags: TrendingTag[] = [];
  @Input() facebookLink = '';
  @Input() instagramLink = 'https://www.instagram.com/magyarnemzet.hu/?hl=en';
  @Input() twitterLink = '';
  @Input() videaLink = '';
  @Input() youtubeLink = 'https://www.youtube.com/@magyarnemzet_hivatalos';
  @Input() rssLink = '';
  @Output() cookieSettingsClick = new EventEmitter<MouseEvent>();
  @Output() subscriptionClick = new EventEmitter<MouseEvent>();
  // eslint-disable-next-line @angular-eslint/no-output-native
  @Output() search = new EventEmitter<string>();
  @Output() breakingClose = new EventEmitter<boolean>();
  @HostBinding('class') hostClass = 'header';
  subscribeLink = DEFAULT_SUBSCRIBE_LINK;
  isMenuOpen = false;
  selectedParentIndex: number | undefined;
  menuItems: Partial<Record<'buttons' | 'social' | 'links', StaticMenuItem[]>> = {};
  searchText = '';
  #isStuck = false;

  readonly RelatedType = RelatedType;

  constructor() {
    super();
  }

  get isStuck(): boolean {
    return this.#isStuck;
  }

  @Input()
  @HostBinding('class.is-stuck')
  set isStuck(value: boolean) {
    this.#isStuck = value;
  }

  get mainMenu(): SimplifiedMenuItem[] {
    return this.data ?? [];
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.menuItems = {
      buttons: [
        { title: 'In English', link: '/rovat/english' },
        { title: 'Videó', icon: 'play', link: '/video' },
        { title: 'Galéria', icon: 'image', link: '/galeriak' },
        { title: 'Podcast', icon: 'mic', link: '/podcast' },
        { title: 'Jobban mondva - heti véleményhírlevél', icon: 'mail', link: '/hirlevel/feliratkozas-2' },
        { title: 'Feliratkozom a hírlevélre', icon: 'mail', link: '/hirlevel/feliratkozas' },
        {
          title: 'Előfizetek az újságra',
          icon: 'news',
          link: 'https://lapcentrum.hu/napilapok/12-magyar-nemzet.html',
          isExternal: true,
        },
      ],
      social: buildSocialMenu(this.facebookLink, this.instagramLink, this.twitterLink, this.videaLink, this.youtubeLink),
      links: [
        { title: 'Impresszum', link: '/impresszum' },
        { title: 'Médiaajánlat', link: 'https://salesworks.hu/termekek/magyarnemzet-hu/', isExternal: true },
        { title: 'Adatvédelmi tájékoztató', link: '/adatvedelem' },
        { title: 'Felhasználói feltételek', link: '/felhasznalasi-feltetelek' },
        { title: 'Általános szerződési feltételek', link: '/aszf' },
        { title: 'Hirdetési ÁSZF', link: '/hirdetesi-aszf' },
        { title: 'Süti beállítások', click: (event): void => this.cookieSettingsClick.emit(event) },
      ],
    };
  }

  toggleMenu(): void {
    this.isMenuOpen = !this.isMenuOpen;
  }

  closeMenu(): void {
    this.isMenuOpen = false;
    this.removeSelectedIndex();
  }

  selectParent(index: number): void {
    if (this.selectedParentIndex === index) {
      this.selectedParentIndex = undefined;
    } else {
      this.selectedParentIndex = index;
    }
  }

  removeSelectedIndex(): void {
    this.selectedParentIndex = undefined;
  }

  onSearchSubmit(event: Event): void {
    this.search.emit(this.searchText);
    event.preventDefault();
  }

  onBreakingCloseClick(): void {
    this.breakingClose.emit(true);
  }

  onSubscribeClick($event: MouseEvent): void {
    this.subscriptionClick.emit($event);
    this.closeMenu();
  }

  onMenuItemClick(menuItem: StaticMenuItem, $event: MouseEvent): void {
    this.closeMenu();
    menuItem.click && menuItem.click($event);
  }
}
