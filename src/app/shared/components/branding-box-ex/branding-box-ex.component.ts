import { ChangeDetectionStrategy, Component, HostBinding, inject, Input, OnInit } from '@angular/core';
import { UtilService } from '@trendency/kesma-core';
import { BrandingBoxArticle, BrandingBoxExComponent, MindmegetteBrandingBoxArticle } from '@trendency/kesma-ui';
import { Observable, of } from 'rxjs';
import { PersonalizedRecommendationService } from 'src/app/shared/services/personalized-recommendation.service';
import { WidgetBrandingBoxExComponent } from '../microsite-widgets/widget-branding-box-ex/widget-branding-box-ex.component';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-mno-branding-box-ex',
  templateUrl: './branding-box-ex.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrl: './branding-box-ex.component.scss',
  imports: [BrandingBoxExComponent, WidgetBrandingBoxExComponent, AsyncPipe],
})
export class MnoBrandingBoxExComponent implements OnInit {
  @Input() @HostBinding('class') set brand(brand: string) {
    switch (brand) {
      case 'vilaggazdasag':
        this._traffickingPlatform = 'VG for MNO';
        break;
      case 'mindmegette':
        this._traffickingPlatform = 'Mindmegette_Brb';
        break;
      default:
        this._traffickingPlatform = undefined;
        break;
    }
    this._brand = brand;
  }

  @Input() showLabels: boolean;
  @Input() desktopWidth: number;

  data$: Observable<MindmegetteBrandingBoxArticle[] | BrandingBoxArticle[] | undefined>;

  brandingBoxService = inject(PersonalizedRecommendationService);
  utils = inject(UtilService);

  private _traffickingPlatform: string | undefined;
  private _brand: string;

  get traffickingPlatforms(): string | undefined {
    return this._traffickingPlatform;
  }

  get brand(): string {
    return this._brand;
  }

  ngOnInit(): void {
    this.data$ = this.getBrandingBoxData();
  }

  getBrandingBoxData(): Observable<MindmegetteBrandingBoxArticle[] | BrandingBoxArticle[] | undefined> {
    if (this.traffickingPlatforms) {
      return this.brandingBoxService.getTrafficDeflectorRecommendation(this.traffickingPlatforms, this.brand === 'vilaggazdasag' ? 7 : 4);
    } else {
      return of(undefined);
    }
  }
}
