@use 'shared' as *;

:root {
  --kui-kulturnemzet-red: #ee4e46;
  --kui-kulturnemzet-white: #fff;
  --kui-kulturnemzet-white-4: #f5f5f5;
  --kui-kulturnemzet-gray-3: #3e3e3e;
  --kui-micro-kulturnemzet-green: #004924;
}

.pager {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  margin-top: 60px;

  .pager-wrapper {
    display: flex;
    height: 22px;
    justify-content: center;
    align-items: center;
    width: calc(100% - 20px);

    .arrow,
    .pagenum {
      height: 22px;
      position: relative;
      display: block;
    }

    .arrow {
      width: 35px;
      height: 36px;
      line-height: 0;
      background-color: var(--kui-kulturnemzet-red);

      .icon {
        width: 14px;
        height: 14px;
        color: var(--kui-kulturnemzet-white);

        &.icon-arrow-left,
        &.icon-arrow-right {
          @include icon('icon-arrow-right-white.svg');

          &.icon-arrow-left {
            transform: scaleX(-1);
          }
        }

        &.icon-arrow-left-end,
        &.icon-arrow-right-end {
          @include icon('icon-arrow-right-end.png');

          &.icon-arrow-left-end {
            transform: scaleX(-1);
          }
        }
      }

      &[disabled] {
        background-color: var(--kui-kulturnemzet-white-4);
        pointer-events: none;
        cursor: default;
      }

      &.arrow-prev {
        margin-left: 3px;
      }

      &.arrow-next {
        margin-right: 3px;
      }
    }

    .pager-wrapper-inner {
      border-bottom: 2px solid var(--kui-kulturnemzet-white-4);
      height: 36px;
      margin: 0 8px;
      width: calc(100% - 160px);

      .page-number {
        height: 17px;
        color: var(--kui-kulturnemzet-gray-3);
        font-size: 14px;
        font-weight: 500;
        text-align: center;
      }
    }
  }
}

.kulturnemzet-pager {
  .kulturnemzet-pager-wrapper {
    display: flex;
    justify-content: space-between;
    width: 572px;
    max-width: 100%;
    margin: 0 auto;

    .kp-left,
    .kp-center,
    .kp-right {
      height: 36px;
      display: flex;
      align-items: center;
    }

    .kp-left {
      justify-content: flex-start;
    }

    .kp-center {
      justify-content: center;
      color: var(--kui-micro-kulturnemzet-green);
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
    }

    .kp-right {
      justify-content: flex-end;
    }

    .kbtn-pager {
      background-color: var(--kui-kulturnemzet-white);
      width: 36px;
      height: 36px;
      display: block;

      .icon {
        display: block;
        width: 36px;
        height: 36px;
      }

      &.kbtn-first {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
        margin-right: 4px;

        .icon {
          @include icon('widget-kulturnemzet/pager-arrow-first.png');
        }
      }

      &.kbtn-prev {
        .icon {
          @include icon('widget-kulturnemzet/pager-arrow-prev.png');
        }
      }

      &.kbtn-next {
        .icon {
          @include icon('widget-kulturnemzet/pager-arrow-next.png');
        }
      }

      &.kbtn-last {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
        margin-left: 4px;

        .icon {
          @include icon('widget-kulturnemzet/pager-arrow-last.png');
        }
      }

      &[class] .icon {
        background-size: 45%;
      }

      &.disabled .icon {
        filter: saturate(0);
        opacity: 0.3;
      }
    }
  }
}
