<div
  *ngIf="type === 'kulturnemzet' && currentPage !== undefined && totalPages"
  class="kulturnemzet-pager"
  mnoPagination
  #pagination="mnoPagination"
  [totalPages]="totalPages"
  [pageNo]="currentPage"
  (pageChange)="onPageChange($event)"
>
  <div class="kulturnemzet-pager-wrapper">
    <div class="kp-left">
      <button [disabled]="pagination.isFirst" class="kbtn-pager kbtn-first" [class.disabled]="pagination.isFirst" (click)="pagination.first()">
        <i class="icon"></i>
      </button>
      <button [disabled]="pagination.isFirst" class="kbtn-pager kbtn-prev" [class.disabled]="pagination.isFirst" (click)="pagination.prev()">
        <i class="icon"></i>
      </button>
    </div>
    <div class="kp-center">
      <span class="kultur-pagenum">{{ currentPage }}/{{ totalPages }}</span>
    </div>
    <div class="kp-right">
      <button [disabled]="pagination.isLast" class="kbtn-pager kbtn-next" [class.disabled]="pagination.isLast" (click)="pagination.next()">
        <i class="icon"></i>
      </button>
      <button [disabled]="pagination.isLast" class="kbtn-pager kbtn-last" [class.disabled]="pagination.isLast" (click)="pagination.last()">
        <i class="icon"></i>
      </button>
    </div>
  </div>
</div>
