import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { PaginationDirective } from '../../directives';
import { NgIf } from '@angular/common';

@Component({
  selector: 'mno-kulturnemzet-pager',
  templateUrl: './kulturnemzet-pager.component.html',
  styleUrls: ['./kulturnemzet-pager.component.scss'],
  imports: [NgIf, PaginationDirective],
})
export class KulturnemzetPagerComponent implements OnInit {
  @Input() rowFrom = 0;
  @Input() rowAllCount = 0;
  @Input() rowOnPageCount = 25;
  @Input() type = 'kulturnemzet';
  @Input() pageCurrent = 0;
  @Input() pageMax = 10;

  public totalPages = 0;
  public currentPage = 1;

  @Output() pageChange = new EventEmitter<number>();

  constructor() {}

  ngOnInit(): void {
    this.initState();
  }

  private initState(): void {
    if (this.pageCurrent !== undefined && this.pageMax) {
      this.currentPage = this.pageCurrent + 1;
      this.totalPages = this.pageMax + 1;
    } else {
      this.currentPage = Math.ceil(this.rowFrom / this.rowOnPageCount) + 1;
      this.totalPages = Math.ceil(this.rowAllCount / this.rowOnPageCount);
    }
  }

  onPageChange(pageNo: number): void {
    this.currentPage = pageNo;
    this.pageChange.emit(pageNo);
  }
}
