import { ChangeDetectionStrategy, Component, EventEmitter, Output } from '@angular/core';

@Component({
  selector: 'app-page-newsletter-banner',
  templateUrl: './page-newsletter-banner.component.html',
  styleUrls: ['./page-newsletter-banner.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [],
})
export class PageNewsletterBannerComponent {
  @Output() subscribeClick = new EventEmitter<MouseEvent>();
}
