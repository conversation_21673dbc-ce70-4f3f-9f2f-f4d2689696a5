@use 'shared' as *;

:host {
  width: 100%;
  display: flex;
  align-items: stretch;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  line-height: 18px; /* 128.571% */
  @include media-breakpoint-down(sm) {
    flex-direction: column;
    .banner {
      &-text {
        border-right-width: 1px;
        border-bottom: 0;
        border-radius: 2px 2px 0 0;
        text-align: center;
      }

      &-signup-button {
        border-radius: 0 0 2px 2px;
      }
    }
  }
}

.banner {
  &-text {
    border: 1px solid var(--kui-blue-500);
    border-radius: 2px 0 0 2px;
    border-right-width: 0;
    display: inline-flex;
    flex-grow: 1;
    padding: 6px 12px;
  }

  &-signup-button {
    flex-grow: 0;
    color: var(--kui-blue-500);
    display: inline-block;
    padding: 6px 12px;
    border-left: 1px solid var(--kui-blue-500);
    text-align: center;
    border-radius: 0 2px 2px 0;
  }
}
