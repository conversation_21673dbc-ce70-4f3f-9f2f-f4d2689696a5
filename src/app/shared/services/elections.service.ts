import { Injectable } from '@angular/core';
import { PortalConfigService } from './portal-config.service';
import { PortalConfigSetting } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class ElectionsService {
  readonly ELECTIONS_2024_LIVE_LINK = '/belfold/2024/06/valasztas-2024-elo-napkozben';
  readonly ELECTIONS_2024_RESULTS_LINK = '/belfold/2024/06/valasztas-2024-elo-eredmenyek';

  constructor(public readonly portalConfigService: PortalConfigService) {}

  isElections2024Enabled(): boolean {
    return this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_ELECTIONS_2024);
  }

  getElections2024Link(): string {
    return this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_ELECTIONS_2024_RESULTS)
      ? this.ELECTIONS_2024_RESULTS_LINK
      : this.ELECTIONS_2024_LIVE_LINK;
  }
}
