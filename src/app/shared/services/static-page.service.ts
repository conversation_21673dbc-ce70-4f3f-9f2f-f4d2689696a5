import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { ApiResponseStaticPageMeta, ApiResult, LayoutApiData, StaticPage } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class StaticPageService {
  constructor(private readonly reqService: ReqService) {}

  public getStaticPage(slug: string): Observable<ApiResult<StaticPage | LayoutApiData, ApiResponseStaticPageMeta>> {
    return this.reqService.get(`custom-static-page-by-slug/${slug}`);
  }

  public getStaticPagePreview(slug: string, previewHash: string): Observable<ApiResult<StaticPage | LayoutApiData, ApiResponseStaticPageMeta>> {
    return this.reqService.get(`content-page/static-page/${slug}/preview/view`, {
      params: {
        previewHash,
      },
    });
  }
}
