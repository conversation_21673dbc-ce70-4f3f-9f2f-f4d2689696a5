import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import {
  ApiListResult,
  ApiResult,
  ArticleCard,
  BackendArticleSearchResult,
  backendDateToDate,
  mapBackendArticleDataToArticleCard,
  Tag,
} from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class TagService {
  constructor(private readonly reqService: ReqService) {}

  public getTagsList(tag: string, page = 0, itemsPerPage = 10): Observable<ApiListResult<ArticleCard>> {
    return this.reqService
      .get<ApiListResult<BackendArticleSearchResult>>('content-page/search', {
        params: {
          'tagSlugs[]': tag,
          rowCount_limit: itemsPerPage.toString(),
          page_limit: page.toString(),
        },
      })
      .pipe(
        map(({ data, meta }) => ({
          data: data
            .map(mapBackendArticleDataToArticleCard)
            .map((article) => ({ ...article, publishDate: backendDateToDate(article.publishDate as string) }) as ArticleCard),
          meta,
        }))
      );
  }

  public getTag(slug: string): Observable<ApiResult<Tag>> {
    return this.reqService.get(`content-group/tags/${slug}`);
  }
}
