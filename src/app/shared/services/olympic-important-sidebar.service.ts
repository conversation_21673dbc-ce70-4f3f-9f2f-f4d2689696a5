import { Injectable, makeStateKey, TransferState } from '@angular/core';
import { Layout, PortalConfigSetting } from '@trendency/kesma-ui';
import { BehaviorSubject, catchError, map, Observable, of, switchMap } from 'rxjs';
import { PortalConfigService } from 'src/app/shared/services/portal-config.service';
import { StaticPageService } from './static-page.service';
import { OLYMPIC_IMPORTANT_SIDEBAR_SLUG, OLYMPIC_IMPORTANT_SIDEBAR_TRANSFERSTATE_KEY } from '../constants';

@Injectable({
  providedIn: 'root',
})
export class OlympicImportantSidebarService {
  #isLayoutRequested = false;
  readonly #layout$ = new BehaviorSubject<Layout | null>(null);

  constructor(
    private readonly staticPageService: StaticPageService,
    private readonly transferState: TransferState,
    private readonly portalConfig: PortalConfigService
  ) {
    if (this.transferState.hasKey(this.stateKey)) {
      this.#isLayoutRequested = true;
      const layout = this.transferState.get(this.stateKey, null);
      this.#layout$.next(layout);
    }
  }

  readonly stateKey = makeStateKey<Layout>(OLYMPIC_IMPORTANT_SIDEBAR_TRANSFERSTATE_KEY);

  getLayout(): Observable<Layout | null> {
    if (!this.portalConfig.isConfigSet(PortalConfigSetting.ENABLE_OLYMPICS_ELEMENTS)) {
      return of(null);
    }
    const layout$ = this.#layout$.asObservable().pipe();
    if (this.#isLayoutRequested) {
      return layout$;
    }
    this.#isLayoutRequested = true;
    return this.staticPageService.getStaticPage(OLYMPIC_IMPORTANT_SIDEBAR_SLUG).pipe(
      catchError(() => {
        return of(null);
      }),
      map((res) => {
        const data = res?.data as any as Layout;
        if (data) {
          this.#layout$.next(data);
        }
        return data;
      }),
      switchMap(() => layout$)
    );
  }
}
