import { inject, Injectable } from '@angular/core';
import { UtilService } from '@trendency/kesma-core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import {
  ApiListResult,
  BackendBrandingBoxResult,
  BrandingBoxArticle,
  BrandingBoxZoeFeedArticle,
  mapBackendBrandingBoxDataToBrandingBoxZoeFeedArticleArray,
} from '@trendency/kesma-ui';
import { environment } from '../../../environments/environment';
import { mapBackendSearchArticleToSearchArticle } from '../utils';
import { CleanHttpService } from './clean-http.service';
import { HttpErrorResponse } from '@angular/common/http';
import { format } from 'date-fns';
import { DOCUMENT } from '@angular/common';
import { BackendSearchArticle, SearchArticle, VG_BRANDING_BOX_UTM_PARAMS } from '../definitions';

@Injectable({
  providedIn: 'root',
})
export class VgBrandingBoxService {
  vgBrandingBoxSubject = new BehaviorSubject<BrandingBoxArticle[]>([]); // TODO: delete this variable if VG V2 will be deployed
  vgDataRequested = false; // TODO: delete this variable if VG V2 will be deployed
  private readonly document = inject(DOCUMENT);

  public constructor(
    private readonly httpService: CleanHttpService,
    private readonly utilService: UtilService
  ) {}

  private get vgUrl(): string {
    const env = this.utilService.isBrowser() && this.document.location.search.includes('bbdebug') ? 'prod' : environment.type;
    switch (env) {
      case 'prod':
      default:
        return this.utilService.isBrowser() ? 'https://www.vg.hu' : 'http://vgfe.app.content.private';
      case 'beta':
        return 'http://vgfe.apptest.content.private';
      case 'dev':
      case 'local':
        return ((environment.apiUrl as string) ?? '').replace('/publicapi/hu', '');
    }
  }

  public fetchVgNewestArticles(): Observable<SearchArticle[]> {
    const options = { headers: { portal: 'vilaggazdasag' } };
    return this.httpService
      .get<ApiListResult<BackendSearchArticle>>(`${this.vgUrl}/publicapi/hu/content-page/search?rowCount_limit=7&page_limit=0&portal=vg.hu`, options)
      .pipe(map(({ data }) => (data ?? []).map(mapBackendSearchArticleToSearchArticle)));
  }

  // TODO: delete this function if VG V2 will be deployed
  public tempFetchVgNewestArticles(): void {
    this.vgDataRequested = true;
    const options = { headers: { portal: 'vilaggazdasag' } };
    this.httpService
      .get<BackendBrandingBoxResult>(`${this.vgUrl}/publicapi/hu/content-page/search?rowCount_limit=7&page_limit=0&portal=vg.hu`, options)
      .pipe(
        catchError((error: HttpErrorResponse) => {
          this.utilService.log('HIBA: VG branding box nem tölthető be! ', 'error');
          this.utilService.log({ message: error.message, details: error.error.message }, 'error');
          return of();
        }),
        map(mapBackendBrandingBoxDataToBrandingBoxZoeFeedArticleArray),
        map((zoeFeedArticles) => zoeFeedArticles.map(this.tempMapZoeFeedArticleToVgBrandingBoxArticle))
      )
      .subscribe((data) => {
        this.vgBrandingBoxSubject.next(data);
      });
  }

  // TODO: delete this function if VG V2 will be deployed
  getTempVgBrandingBoxData(): Observable<BrandingBoxArticle[]> {
    if (!this.vgDataRequested) {
      this.tempFetchVgNewestArticles();
    }
    return this.vgBrandingBoxSubject;
  }

  // TODO: delete this function if VG V2 will be deployed
  private tempMapZoeFeedArticleToVgBrandingBoxArticle({
    slug,
    thumbnail,
    publishDate,
    columnSlug,
    title,
    lead,
  }: BrandingBoxZoeFeedArticle): BrandingBoxArticle {
    let siteUrl = '';
    switch (environment.type) {
      case 'prod':
        siteUrl = 'https://www.vg.hu';
        break;
      case 'beta':
        siteUrl = 'http://vgfe.apptest.content.private';
        break;
      case 'dev':
      case 'local':
        siteUrl = 'https://vilaggazdasag.dev.trendency.hu';
        break;
    }
    const url = `${siteUrl}/${columnSlug}/${format(publishDate, 'yyyy')}/${format(publishDate, 'LL')}/${slug}${VG_BRANDING_BOX_UTM_PARAMS}`;
    return {
      url,
      thumbnail: thumbnail ?? '',
      title: title ?? '',
      lead: lead ?? '',
    };
  }
}
