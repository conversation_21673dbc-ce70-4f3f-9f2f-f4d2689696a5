import { Injectable } from '@angular/core';
import { Api<PERSON><PERSON>ult, EbDid<PERSON>ouKnow, OlimpiaHeaderData, PortalConfigSetting } from '@trendency/kesma-ui';
import { PortalConfigService } from './portal-config.service';
import { BehaviorSubject, Observable } from 'rxjs';
import { ReqService } from '@trendency/kesma-core';

@Injectable({
  providedIn: 'root',
})
export class OlimpiaService {
  readonly isOlimpiaMainOrArticlePage = new BehaviorSubject<boolean>(false);

  didYouKnowData: EbDidYouKnow[] = [];

  constructor(
    private readonly portalConfigService: PortalConfigService,
    private readonly reqService: ReqService
  ) {}

  isEnableOlympicsElements(): boolean {
    return this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_OLYMPICS_ELEMENTS);
  }

  getHeadlineMedalsAndEvents(): Observable<ApiResult<OlimpiaHeaderData>> {
    return this.reqService.get(`/olympics/headline-medals-and-events`);
  }

  setIsOlimpiaMainOrArticlePage(value: boolean): void {
    this.isOlimpiaMainOrArticlePage.next(value);
  }

  getIsOlimpiaMainOrArticlePage$(): Observable<boolean> {
    return this.isOlimpiaMainOrArticlePage.asObservable();
  }
}
