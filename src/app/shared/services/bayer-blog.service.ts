import { inject, Injectable } from '@angular/core';
import { Observable, shareReplay, tap } from 'rxjs';
import { Article, Author, PortalConfigSetting } from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import { ApiService } from './api.service';
import { backendArticlesToArticles } from '../utils';
import { PortalConfigService } from './portal-config.service';
import { BAYER_BLOG_COLUMN, BAYER_ZSOLT_AUTHOR_SLUG } from '../definitions';

@Injectable({
  providedIn: 'root',
})
export class BayerBlogService {
  apiService: ApiService = inject(ApiService);
  portalConfigService = inject(PortalConfigService);
  author?: Author;

  getArticles(page = 0, itemsPerPage = 25, year?: number, month?: number, excludedIds = []): Observable<Article[]> {
    return this.apiService
      .getCategoryArticles(BAYER_BLOG_COLUMN, page, itemsPerPage, year?.toString(), month?.toString(), excludedIds)
      .pipe(map(({ data }) => data.map(backendArticlesToArticles)));
  }

  public readonly author$ = this.apiService.getAuthorFromPublicAuthor(BAYER_ZSOLT_AUTHOR_SLUG).pipe(
    map(
      ({ data }) =>
        ({
          slug: BAYER_ZSOLT_AUTHOR_SLUG,
          name: data.publicAuthorName,
          avatar: data.avatar.fullSizeUrl,
          rank: data.rank,
          avatarUrl: data.avatar.thumbnailUrl,
        }) as Author,
      tap((author: Author) => (this.author = author))
    ),
    shareReplay({ bufferSize: 1, refCount: false })
  );

  isBayerBlog(article: Article): boolean {
    return this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_BAYER_ZSOLT_BLOG_COMPONENTS)
      ? this.isBayerBlogColumn(article.columnSlug) ||
          this.isBayerBlogColumn(article.primaryColumn?.slug) ||
          (article.secondaryColumns?.map(({ slug }) => slug) ?? []).includes(BAYER_BLOG_COLUMN)
      : false;
  }

  isBayerBlogColumn(slug: string = ''): boolean {
    return slug === BAYER_BLOG_COLUMN;
  }
}
