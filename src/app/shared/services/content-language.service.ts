import { Inject, Injectable, OnDestroy } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { DOCUMENT } from '@angular/common';

const DEFAULT_LANG = 'hu';

@Injectable({
  providedIn: 'root',
})
export class ContentLanguageService implements OnDestroy {
  private readonly langSubject = new BehaviorSubject<string>('');
  private readonly alternateLangLinks = new BehaviorSubject<HTMLLinkElement[]>([]);

  constructor(@Inject(DOCUMENT) private readonly document: Document) {
    // Initialize the BehaviorSubject with the current lang attribute value
    const initialLang = this.document.documentElement.lang || DEFAULT_LANG;
    this.langSubject.next(initialLang);
  }

  readonly lang$ = this.langSubject.asObservable();

  getLang(): string {
    return this.langSubject.value;
  }

  setLang(lang: string): void {
    if (lang === this.getLang()) {
      return;
    }
    this.document.documentElement.lang = lang;
    this.langSubject.next(lang);
  }

  resetLang(): void {
    this.setLang(DEFAULT_LANG);
    this.alternateLangLinks.value.forEach((link) => link.remove());
    this.alternateLangLinks.next([]);
  }

  addAlternateLangLink(hrefLang: string, url: string): void {
    const link = this.document.createElement('link');
    link.rel = 'alternate';
    link.hreflang = hrefLang;
    link.href = url;
    this.document.head.appendChild(link);
    this.alternateLangLinks.next([...this.alternateLangLinks.value, link]);
  }

  ngOnDestroy(): void {
    this.langSubject.complete();
    this.alternateLangLinks.complete();
  }
}
