import { Injectable } from '@angular/core';
import { IHttpOptions } from '@trendency/kesma-core';
import { catchError, map, mergeMap, tap } from 'rxjs/operators';
import { BehaviorSubject, Observable } from 'rxjs';
import { Api<PERSON>istResult, PrimaryColumn } from '@trendency/kesma-ui';
import { ApiService } from './api.service';
import { CategoryService } from './category.service';
import { BackendAuthorData } from '../definitions';

@Injectable({
  providedIn: 'root',
})
export class SearchFilterService {
  baseFilterOptions = {
    columns: new BehaviorSubject<PrimaryColumn[]>([]),
    authors: new BehaviorSubject<BackendAuthorData[]>([]),
  };

  constructor(
    private readonly apiService: ApiService,
    private readonly categoryService: CategoryService
  ) {}

  getColumns(): Observable<PrimaryColumn[]> {
    if (this.baseFilterOptions.columns.value?.length > 0) {
      return this.baseFilterOptions.columns.asObservable();
    }

    return this.categoryService
      .getParentColumns()
      .pipe(map(({ data }) => data))
      .pipe(
        tap((data) => {
          // Update the BehaviorSubject with the retrieved data
          this.baseFilterOptions.columns.next(data);
        }),
        catchError((error) => {
          // Handle errors if necessary
          console.error('Error fetching column data:', error);
          throw error; // rethrow the error
        }),
        mergeMap(() => this.baseFilterOptions.columns)
      );
  }

  getAuthors(): any {
    if (this.baseFilterOptions.authors.value?.length > 0) {
      return this.baseFilterOptions.authors.asObservable();
    } else {
      const authors$ = this.apiService.getPublicAuthors().pipe(map(({ data }) => data));
      return authors$.pipe(
        tap((data) => {
          // Currently we update both subject. In the future we need to separate logic for sport, you can change it here
          this.baseFilterOptions.authors.next(data);
        }),
        catchError((error) => {
          // Handle errors if necessary
          console.error('Error fetching author data:', error);
          throw error; // rethrow the error
        }),
        mergeMap(() => this.baseFilterOptions.authors)
      );
    }
  }

  getColumnsRequest(options: IHttpOptions): Observable<ApiListResult<PrimaryColumn>> {
    return this.categoryService.getParentColumns(options);
  }
}
