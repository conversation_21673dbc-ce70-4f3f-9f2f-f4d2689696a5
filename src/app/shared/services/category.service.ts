import { Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import {
  AllColumns,
  AllColumnsResponse,
  ApiListResult,
  ApiResponseMeta,
  ApiResult,
  ArticleCard,
  ArticleCategoryParams,
  BackendArticleSearchResult,
  backendDateToDate,
  mapBackendArticleDataToArticleCard,
  PrimaryColumn,
  RedirectService,
} from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class CategoryService {
  constructor(
    private readonly reqService: ReqService,
    private readonly redirectService: RedirectService
  ) {}

  public getCategoryArticlesEnsurePage(
    categorySlug: string,
    page = 0,
    itemsPerPage = 10,
    year?: string,
    month?: string,
    excludedIds: string[] = [],
    rowFrom_limit?: number
  ): Observable<ApiListResult<ArticleCard>> {
    return this.getCategoryArticles(categorySlug, page, itemsPerPage, year, month, excludedIds, rowFrom_limit).pipe(
      tap((res: ApiListResult<ArticleCard>) => {
        const pageMax = res?.meta.limitable.pageMax ?? 0;
        const pageCurrent = res?.meta.limitable.pageCurrent ?? 0;
        const rowFrom = res?.meta.limitable.rowFrom ?? 0;
        const rowCount = res?.meta.limitable.rowAllCount ?? 0;
        if (
          !res.meta.limitable.rowAllCount || // No items. There is nothing to paginate, catchError will handle this
          (res.data.length && (rowFrom < rowCount || pageCurrent < pageMax))
        ) {
          //Everything is fine, we have enough items
          return;
        }
        if (this.redirectService.shouldBeRedirect(page, res?.data)) {
          this.redirectService.redirectOldUrl(`rovat/${categorySlug}`, false, 302);
        }
      })
    );
  }

  public getCategoryArticles(
    columnSlug: string,
    page = 0,
    itemsPerPage = 10,
    year?: string,
    month?: string,
    excludedIds: string[] = [],
    rowFrom_limit = 0
  ): Observable<ApiListResult<ArticleCard>> {
    let params: ArticleCategoryParams = {
      columnSlug,
      rowCount_limit: itemsPerPage.toString(),
      'excludedArticleIds[]': excludedIds ? excludedIds : [],
      rowFrom_limit: rowFrom_limit?.toString(),
      page_limit: page?.toString(),
    };
    params = year ? { ...params, year } : params;
    params = month ? { ...params, month } : params;

    return this.reqService.get<ApiListResult<BackendArticleSearchResult>>('content-page/articles-by-column', { params: params }).pipe(
      map(({ data, meta }) => {
        return {
          meta,
          data: data.map((article) => {
            const [publishYear, publishMonth] = (article.publishDate as string).split('-');
            return {
              ...article,
              ...mapBackendArticleDataToArticleCard(article as BackendArticleSearchResult),
              publishDate: backendDateToDate(article.publishDate as string),
              publishYear,
              publishMonth,
              isOpinion: (article as any).isOpinion ?? (article.contentType as any) === 'opinion',
            } as unknown as ArticleCard;
          }),
        };
      }),
      catchError(() =>
        of({
          data: [],
          meta: {},
        } as any)
      )
    );
  }

  public getAllColumns(itemsPerPage = 20, ids?: string[], contentTypes?: string[]): Observable<ApiListResult<AllColumns>> {
    const params: Partial<ApiResponseMeta> = {
      rowCount_limit: itemsPerPage.toString(),
    };

    if (ids) {
      params['ids[]'] = ids;
    }

    if (contentTypes) {
      params['content_type[]'] = contentTypes;
    }

    return this.reqService.get<AllColumnsResponse>('source/content-group/columns', {
      params,
    });
  }

  getParentColumns(options?: IHttpOptions): Observable<ApiListResult<PrimaryColumn>> {
    return this.reqService.get('/source/content-group/columns?parents_only=1', options);
  }

  getColumn(slug: string): Observable<ApiResult<PrimaryColumn>> {
    return this.reqService.get(`/content-group/columns/${slug}`);
  }
}
