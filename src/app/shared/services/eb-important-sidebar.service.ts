import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, switchMap } from 'rxjs';
import { Layout, PortalConfigSetting } from '@trendency/kesma-ui';
import { catchError, map } from 'rxjs/operators';
import { StaticPageService } from './static-page.service';
import { PortalConfigService } from './portal-config.service';
import { EB_SIDEBAR_SLUG } from '../constants';

@Injectable({
  providedIn: 'root',
})
export class EbImportantSidebarService {
  #isLayoutRequested = false;
  readonly #layout$ = new BehaviorSubject<Layout | null>(null);

  constructor(
    private readonly staticPageService: StaticPageService,
    private readonly portalConfig: PortalConfigService
  ) {}

  getLayout(categorySlug: string): Observable<Layout | null> {
    if (!this.portalConfig.isConfigSet(PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS)) {
      return of(null);
    }
    if (categorySlug !== 'sport') {
      return of(null);
    }
    const layout$ = this.#layout$.asObservable();
    if (this.#isLayoutRequested) {
      return layout$;
    }
    this.#isLayoutRequested = true;
    return this.staticPageService.getStaticPage(EB_SIDEBAR_SLUG).pipe(
      catchError(() => {
        return of(null);
      }),
      map((res) => {
        const data = res?.data as any as Layout;
        if (data) {
          this.#layout$.next(data);
        }
        return data;
      }),
      switchMap(() => layout$)
    );
  }
}
