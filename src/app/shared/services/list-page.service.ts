import { Injectable } from '@angular/core';
import {
  Api<PERSON>ist<PERSON><PERSON>ult,
  ApiResponseMetaList,
  ApiResult,
  ArticleSearchResult,
  BackendArticleSearchResult,
  DossierArticle,
  FilterParams,
} from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiService } from 'src/app/shared/services/api.service';
import { backendArticlesSearchResultsToArticleSearchResArticles } from '../utils';
import { DossierService } from './dossier.service';

@Injectable({
  providedIn: 'root',
})
export class ListPageService {
  constructor(
    private readonly apiService: ApiService,
    private readonly dossierService: DossierService
  ) {}

  searchArticle(page = 0, itemsPerPage = 10, extraParams: FilterParams = {}): Observable<ApiListResult<ArticleSearchResult>> {
    return this.apiService.searchByKeyword(page, itemsPerPage, extraParams).pipe(
      map(({ data, meta }: ApiListResult<BackendArticleSearchResult>) => ({
        data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
        meta,
      }))
    );
  }

  searchDossier(slug = '', page = 0, itemsPerPage = 10): Observable<ApiResult<DossierArticle[], ApiResponseMetaList>> {
    return this.dossierService.getDossier(slug, page, itemsPerPage);
  }
}
