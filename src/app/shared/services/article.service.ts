import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import {
  ApiListResult,
  ApiResult,
  Article,
  ArticleBody,
  ArticleBodyDetails,
  ArticleBodyType,
  ArticleCard,
  BackendArticleSearchResult,
  previewBackendArticleToArticleCard,
  RecommendationsData,
  ThumbnailImage,
} from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import { ISearchParams } from '../definitions';

@Injectable({
  providedIn: 'root',
})
export class ArticleService {
  constructor(private readonly reqService: ReqService) {}

  getArticlePreview(articleSlug: string, previewHash: string, previewType: string): Observable<ApiResult<Article>> {
    const timestamp = Math.floor(new Date().getTime());
    return this.reqService.get(`content-page/article/${articleSlug}/preview/view?previewHash=${previewHash}&t=${timestamp.toString()}`, {
      params: {
        previewType,
      },
    });
  }

  getArticle(category: string, year: string, month: string, articleSlug: string): Observable<ApiResult<Article>> {
    return this.reqService.get(`content-page/article/${category}/${year}/${month}/${articleSlug}`);
  }

  getArticleRecommendations(articleSlug: string): Observable<ApiResult<RecommendationsData>> {
    return this.reqService.get(`content-page/article/${articleSlug}/recommendation?fields[]=foundationTagSlug&fields[]=foundationTagTitle`);
  }

  getVideoTypeArticles(
    searchQuery: string,
    page?: number,
    rowCountLimit?: number,
    searchParams: ISearchParams = {} as ISearchParams
  ): Observable<ApiListResult<BackendArticleSearchResult>> {
    return this.reqService.get(`/content-page/articles-by-video-type`, {
      params: {
        global_filter: searchQuery,
        rowCount_limit: rowCountLimit?.toString(),
        page_limit: page?.toString(),
        ...(searchParams ?? {}),
      },
    });
  }

  getPodcastTypeArticles(
    searchQuery: string,
    page?: number,
    rowCountLimit?: number,
    searchParams: ISearchParams = {} as ISearchParams
  ): Observable<ApiListResult<BackendArticleSearchResult>> {
    return this.reqService.get(`/content-page/articles-by-podcast-type`, {
      params: {
        global_filter: searchQuery,
        rowCount_limit: rowCountLimit?.toString(),
        page_limit: page?.toString(),
        ...(searchParams ?? {}),
      },
    });
  }

  getNewsArticles(
    page?: number,
    rowCountLimit?: number,
    orderByAsc?: boolean,
    from_date?: string,
    to_date?: string,
    contentTypes?: string[],
    column?: string[]
  ): Observable<ApiListResult<ArticleCard>> {
    let params: ISearchParams = {
      rowCount_limit: rowCountLimit?.toString(),
      page_limit: page?.toString(),
      'publishDate_order[0]': orderByAsc ? 'asc' : 'desc',
      'content_types[]': contentTypes ? contentTypes : [],
      'columnSlugs[]': column ? column : [],
    };
    params = from_date ? { ...params, from_date } : params;
    params = to_date ? { ...params, to_date } : params;

    return this.reqService.get<ApiListResult<ArticleCard>>(`/content-page/articles-by-any`, { params }).pipe(
      map((apiResult) => ({
        meta: apiResult.meta,
        data: apiResult.data.map((article: ArticleCard) => ({
          ...article,
          thumbnail: {
            url: (article.thumbnail as unknown as string) ?? '',
            // BE returns thumbnail as string instead of object here, unlike in other places
          } as ThumbnailImage,
          hasGallery: article.hasGallery ? !!+article.hasGallery : false,
          isPaywalled: article.isPaywalled ? !!+article.isPaywalled : false,
        })),
      }))
    );
  }

  getFreshNews(
    global_filter?: string,
    sort?: string,
    from_date?: string,
    to_date?: string,
    contentTypes?: string[],
    column?: string[],
    page = 0,
    rowCount_limit = 20,
    material_types_only?: string,
    tag?: string[],
    priority?: string[]
  ): Observable<ApiListResult<BackendArticleSearchResult>> {
    let params: ISearchParams = {
      rowCount_limit: rowCount_limit?.toString(),
      page_limit: page?.toString(),
      'publishDate_order[0]': sort === 'date_asc' ? 'asc' : 'desc',
      'content_types[]': contentTypes ? contentTypes : [],
      'columnSlugs[]': column ? column : [],
      'tagSlugs[]': tag ? tag : [],
      'priorityIds[]': priority ? priority : [],
    };
    params = global_filter ? { ...params, global_filter } : params;
    params = from_date ? { ...params, from_date } : params;
    params = to_date ? { ...params, to_date } : params;
    params = material_types_only ? { ...params, material_types_only } : params;

    return this.reqService.get(`/content-page/search`, {
      params,
    });
  }

  public prepareArticleBody(body: ArticleBody[]): ArticleBody[] {
    return body?.map((bodyPart: ArticleBody) => ({
      ...bodyPart,
      details: (bodyPart.details ?? []).map((detail: ArticleBodyDetails) => ({
        ...detail,
        ...this.prepareArticleBodyDetail(detail, bodyPart.type),
      })),
    }));
  }

  public prepareArticleBodyDetail(detail: ArticleBodyDetails, type: ArticleBodyType): ArticleBodyDetails {
    let newDetail: ArticleBodyDetails;
    switch (type) {
      case ArticleBodyType.Article:
        newDetail = {
          ...detail,
          value: { ...previewBackendArticleToArticleCard(detail.value), ...{ label: { text: 'Ezt is ajánljuk a témában' } } },
        };
        break;
      default:
        newDetail = { ...detail };
    }
    return newDetail;
  }
}
