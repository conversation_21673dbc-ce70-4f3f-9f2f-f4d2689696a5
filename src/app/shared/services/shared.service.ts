import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class SharedService {
  private readonly isArticlePage$ = new BehaviorSubject<boolean>(false);

  isArticlePageObs$: Observable<boolean> = this.isArticlePage$.asObservable();

  setIsArticlePage(value: boolean): void {
    this.isArticlePage$.next(value);
  }
}
