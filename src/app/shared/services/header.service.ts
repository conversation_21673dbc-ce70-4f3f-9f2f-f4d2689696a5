import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class HeaderService {
  private readonly headerTopOffsetSubject = new BehaviorSubject<number>(0);
  private readonly headerHeightSubject = new BehaviorSubject<number>(0);

  setHeaderTopOffset(offset: number): void {
    this.headerTopOffsetSubject.next(offset);
  }

  setHeaderHeight(height: number): void {
    this.headerHeightSubject.next(height);
  }

  get headerTopOffset$(): Observable<number> {
    return this.headerTopOffsetSubject.asObservable();
  }

  get headerTopOffset(): number {
    return this.headerTopOffsetSubject.getValue();
  }

  get headerHeight$(): Observable<number> {
    return this.headerHeightSubject.asObservable();
  }

  get headerHeight(): number {
    return this.headerHeightSubject.getValue();
  }
}
