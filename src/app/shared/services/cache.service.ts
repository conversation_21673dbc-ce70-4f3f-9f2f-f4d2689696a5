import { inject, Injectable } from '@angular/core';
import { RESPONSE } from '@trendency/kesma-core';
import type { Response } from 'express';
import { ApiResponseMeta } from '@trendency/kesma-ui';
import get from 'lodash-es/get';

@Injectable({
  providedIn: 'root',
})
export class CacheService {
  private readonly response = inject<Response>(RESPONSE, { optional: true });

  setCache(meta: ApiResponseMeta): void {
    if (!this.response || !meta) {
      return;
    }
    const cacheIds = get(meta, 'd.d.dx') as string[];
    if (cacheIds?.length) {
      this.response.setHeader('xkey', cacheIds.join(' '));
    }
  }
}
