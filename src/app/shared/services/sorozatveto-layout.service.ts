import { DestroyRef, inject, Injectable } from '@angular/core';
import { ArticleReview, ArticleReviewBody, ReviewService, SorozatvetoOpinionCard } from '@trendency/kesma-ui';
import { map, Observable, shareReplay } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Injectable({
  providedIn: 'root',
})
export class SorozatvetoLayoutService {
  private readonly destroyRef = inject(DestroyRef);
  sorozatvetoCache: Record<string, Observable<SorozatvetoOpinionCard<ArticleReviewBody[]>[]>> = {};

  constructor(private readonly reviewService: ReviewService) {}

  loadReviews(articleId: string, useBody = true): Observable<SorozatvetoOpinionCard<ArticleReviewBody[]>[]> {
    const req$ = this.reviewService.getArticleReviews(articleId).pipe(
      shareReplay({ bufferSize: 1, refCount: true }),
      map(({ data }) => data),
      map((reviews: ArticleReview[]) => {
        return (reviews ?? [])
          .filter((r) => !!r)
          .map((review: ArticleReview) => ({
            ...review,
            author: {
              name: review.publicAuthorName || review.publicAuthor?.authorData?.fullName || '',
              avatar: review.publicAuthor?.avatarFullSizeUrl,
              avatarUrl: review.publicAuthorAvatarThumbnailUrl || review.publicAuthor?.avatarThumbnail || review.publicAuthor?.avatarFullSizeUrl,
            },
            lead: review.excerpt || review.lead || review.title,
            body: useBody ? review.body : [],
          }));
      }),
      takeUntilDestroyed(this.destroyRef)
    );
    this.sorozatvetoCache[articleId] = req$;
    return req$;
  }
}
