import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { FilterParams } from '@trendency/kesma-ui';
import { calculateFilters } from '../utils';

@Injectable({
  providedIn: 'root',
})
export class SearchFilterNavigationService {
  constructor(private readonly router: Router) {}

  navigateBySearchFilters(filters: FilterParams): void {
    const baseRoute = '/';
    filters = calculateFilters(filters);

    const appliedFilterTypes = Object.keys(filters);
    if (appliedFilterTypes.includes('author')) {
      this.router.navigate([baseRoute, 'szerzo', filters['author']], {
        queryParams: {
          ...filters,
          author: null,
        },
      });
      return;
    }

    if (appliedFilterTypes.includes('columnSlugs[]') && appliedFilterTypes.length === 1) {
      this.router.navigate([baseRoute, 'rovat', filters['columnSlugs[]']]);
    }

    if (appliedFilterTypes.includes('tagSlugs[]')) {
      this.router.navigate([baseRoute, 'cimke', filters['tagSlugs[]']], {
        queryParams: {
          ...filters,
          'tagSlugs[]': null,
          'global_filter[]': null,
        },
      });
      return;
    }

    this.router.navigate([baseRoute, 'kereses'], {
      queryParams: { ...filters, page: null },
    });
  }
}
