import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { ApiListResult, ApiResult, Video } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class VideoService {
  constructor(private readonly reqService: ReqService) {}

  getVideos(rowCount_limit?: number): Observable<ApiListResult<Video>> {
    const queryParam = rowCount_limit !== undefined ? '?rowCount_limit=' + rowCount_limit : '';
    return this.reqService.get('media/videos' + queryParam);
  }

  getVideo(slug: string): Observable<ApiResult<Video[]>> {
    return this.reqService.get('media/videos/' + slug);
  }
}
