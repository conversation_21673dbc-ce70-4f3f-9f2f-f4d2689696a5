import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { ApiListResult, ApiResult, ChampionshipSchedule, LiveSport, SingleElimination } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CompetitionSummary, ScheduleByCompetitions, SportCompetitions, SportPhases } from '../definitions';

@Injectable({
  providedIn: 'root',
})
export class SportResultService {
  constructor(private readonly reqService: ReqService) {}

  getLiveSports(): Observable<ApiListResult<LiveSport>> {
    return this.reqService.get<ApiListResult<LiveSport>>(`/sport/live-sports`);
  }

  getCompetitions(liveSportId: string): Observable<ApiListResult<SportCompetitions>> {
    return this.reqService.get(`/sport/competitions/magyar_nemzet/${liveSportId}`);
  }

  getPhasesByCompetition(competitionSlug: string): Observable<ApiResult<SportPhases>> {
    return this.reqService.get(`/sport/phases-by-competition/${competitionSlug}`);
  }

  getSchedulesByPhase(phaseId: string): Observable<ApiResult<ScheduleByCompetitions>> {
    return this.reqService.get(`/sport/schedule/by-phase/${phaseId}`);
  }

  getScheduleByCompetition(competitionSlug: string): Observable<ApiResult<ScheduleByCompetitions>> {
    return this.reqService.get(`/sport/schedule/by-competition/${competitionSlug}`);
  }

  getPastScheduleByCompetition(competitionSlug: string): Observable<ApiResult<ScheduleByCompetitions>> {
    return this.reqService.get(`/sport/schedule/by-competition/${competitionSlug}/past`);
  }

  getCompetitionTabella(competitionSlug: string): Observable<any> {
    return this.reqService.get(`/sport/competition/${competitionSlug}/tabella-by-slug`);
  }

  getTabellaByPhase(phaseId: string): Observable<any> {
    return this.reqService.get(`/sport/phase/${phaseId}/tabella`);
  }

  getScorerList(competitionSlug: string): Observable<any> {
    return this.reqService.get(`/sport/competition/${competitionSlug}/scorer-list`);
  }

  getVersusData(team1?: string, team2?: string): Observable<ApiListResult<ChampionshipSchedule>> {
    return this.reqService.get(`/sport/schedule/versus/${team1}/${team2}`);
  }

  getLast5Schedules(teamSlug?: string, competitionSlug?: string): Observable<any> {
    return this.reqService.get(`/sport/team/${teamSlug}/${competitionSlug}/schedules?limit=5`);
  }

  getLastSchedules(teamSlug: string, competitionSlug: string): Observable<any> {
    return this.reqService.get(`/sport/team/${teamSlug}/${competitionSlug}/schedules`);
  }

  getCompetitionSummary(competitionSlug: string): Observable<CompetitionSummary[]> {
    return this.reqService.get<{ data: CompetitionSummary[] }>(`sport/competition/${competitionSlug}/summary`).pipe(map(({ data }) => data));
  }

  getSchedulesGroupedByRound(competitionSlug: string): Observable<SingleElimination> {
    return this.reqService.get<{ data: SingleElimination }>(`/sport/schedule/groupped-by-round/${competitionSlug}`).pipe(map(({ data }) => data));
  }

  getSchedulesGroupedByRoundList(scheduleIds: string[]): Observable<Record<string, ChampionshipSchedule[]>> {
    return this.reqService.post<Record<string, ChampionshipSchedule[]>>('sport/schedule/grouped-by-round-list', {
      scheduleIds: scheduleIds ?? [],
    });
  }
}
