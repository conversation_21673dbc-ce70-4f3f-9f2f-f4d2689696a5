import { Inject, Injectable, Optional } from '@angular/core';
import { REQUEST, UtilService } from '@trendency/kesma-core';
import { Sponsorship } from '@trendency/kesma-ui';
import { BehaviorSubject, distinctUntilChanged } from 'rxjs';
import type { Request } from 'express';
import { checkIsMobileDevice, isMobileApp } from '../utils';

const MOBILE_WIDTH = 768;

interface HeaderConfig {
  isDark: boolean;
  breakingColor: 'red' | 'blue';
}

@Injectable({
  providedIn: 'root',
})
export class ConfigService {
  header: HeaderConfig = {
    isDark: false,
    breakingColor: 'red',
  };

  readonly isMobileDevice: boolean;
  readonly isMobileApp: boolean;
  readonly #mobile$ = new BehaviorSubject<boolean>(this.isMobile());
  isMobile$ = this.#mobile$.pipe(distinctUntilChanged());
  readonly #sponsorship$ = new BehaviorSubject<{ sponsor: Sponsorship; title: string } | undefined>(undefined);
  sponsorship$ = this.#sponsorship$.pipe(distinctUntilChanged());

  constructor(
    private readonly utilService: UtilService,
    @Inject(REQUEST) @Optional() private readonly request: Request
  ) {
    const ua = this.utilService.isBrowser() ? navigator.userAgent || (window as any).opera : this.request?.headers?.['user-agent'];

    this.isMobileDevice = checkIsMobileDevice(ua);
    this.isMobileApp = isMobileApp(ua);
  }

  setHeader(config: Partial<HeaderConfig>): void {
    this.header = {
      ...this.header,
      breakingColor: 'red',
      ...config,
    } as HeaderConfig;
  }

  setSponsorship(sponsor?: Sponsorship, title = 'A rovat tartalmának támogatója'): void {
    this.#sponsorship$.next(sponsor ? { sponsor, title } : undefined);
  }

  clearSponsorship(): void {
    this.setSponsorship();
  }

  isMobile(): boolean {
    return this.isMobileDevice || (this.utilService.isBrowser() && window.innerWidth < MOBILE_WIDTH);
  }

  checkMobile(): void {
    this.#mobile$.next(this.isMobile());
  }
}
