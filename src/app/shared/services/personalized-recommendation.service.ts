import { Inject, Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { Observable, of } from 'rxjs';
import { DOCUMENT } from '@angular/common';
import {
  ArticleCard,
  BrandingBoxArticle,
  mapPersonalizedRecommendationToArticleCard,
  MindmegetteBrandingBoxArticle,
  PersonalizedRecommendationApiResponse,
  PersonalizedRecommendationArticle,
} from '@trendency/kesma-ui';
import { catchError, map, tap } from 'rxjs/operators';
import { EnvironmentApiUrl, UtilService } from '@trendency/kesma-core';
import { CleanHttpService } from './clean-http.service';

@Injectable({
  providedIn: 'root',
})
export class PersonalizedRecommendationService {
  private readonly DOMAIN: string = 'Magyar Nemzet';
  private readonly PLATFORM: string = 'Magyar Nemzet';

  private isPersonalizedContentFetched: boolean = false;

  constructor(
    private readonly httpService: CleanHttpService,
    private readonly utilsService: UtilService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  get deflectorApiUrl(): string {
    return environment.type === 'beta' ? 'https://terelo.app.content.private/api' : (environment.personalizedRecommendationApiUrl as string);
  }

  get personalizedRecommendationApiUrl(): string {
    if (typeof environment.personalizedRecommendationApiUrl === 'string') {
      return environment.personalizedRecommendationApiUrl as string;
    }

    const { clientApiUrl, serverApiUrl } = environment.personalizedRecommendationApiUrl as EnvironmentApiUrl;
    return this.utilsService.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  getPersonalizedRecommendations(limit = 12): Observable<ArticleCard[]> {
    const currentArticleUrl: string = this.document?.defaultView?.location.href || '';
    const vidCookie: string = this.document?.cookie?.match(/_vid=([^;]+)/)?.[1] || '';
    const pureSiteUrl: string = environment.siteUrl?.replace(/^https?:\/\/(www\.)?/, '') || '';

    return this.httpService
      .get<PersonalizedRecommendationApiResponse>(`${this.personalizedRecommendationApiUrl}/recommendation`, {
        params: {
          'traffickingPlatforms[]': this.PLATFORM,
          fingerPrint: vidCookie,
          articleCurrent: encodeURIComponent(currentArticleUrl),
          utmSource: pureSiteUrl,
          withoutPos: '1',
        },
      })
      .pipe(
        map((res: PersonalizedRecommendationApiResponse) => res[this.PLATFORM].map(mapPersonalizedRecommendationToArticleCard).slice(0, limit)),
        tap(() => (this.isPersonalizedContentFetched = true))
      );
  }

  sendPersonalizedRecommendationAv(externalRecommendations: ArticleCard[]): Observable<void> {
    if (!this.isPersonalizedContentFetched) {
      return of();
    }

    const ids: string[] = externalRecommendations.map((article: ArticleCard) => article.id) as string[];
    const avs: Record<string, string> = ids.reduce((res, value, index) => ({ ...res, [`a[${index + 1}]`]: value }), {});

    return this.httpService.post<void>(`${this.personalizedRecommendationApiUrl}/av`, null, {
      params: {
        domain: this.DOMAIN,
        platform: this.PLATFORM,
        ...avs,
      },
    });
  }

  getTrafficDeflectorRecommendation(traffickingPlatforms: string, limit = 4): Observable<MindmegetteBrandingBoxArticle[] | BrandingBoxArticle[] | undefined> {
    return this.httpService
      .get<PersonalizedRecommendationApiResponse>(`${this.deflectorApiUrl}/recommendation`, {
        params: {
          traffickingPlatforms,
          utmSource: 'mindmegette.hu',
          withoutPos: '1',
        },
      })
      .pipe(
        map((res: PersonalizedRecommendationApiResponse) =>
          res?.[traffickingPlatforms]
            ?.map((personalizedRecommendationArticle: PersonalizedRecommendationArticle): MindmegetteBrandingBoxArticle | BrandingBoxArticle => ({
              title: personalizedRecommendationArticle?.title,
              lead: personalizedRecommendationArticle?.head,
              url: personalizedRecommendationArticle?.url,
              thumbnail: personalizedRecommendationArticle?.image,
              author: personalizedRecommendationArticle?.author,
              category: personalizedRecommendationArticle?.category,
              isAdult: personalizedRecommendationArticle?.is_adult === 1,
              hasVideo: personalizedRecommendationArticle?.is_video === 1,
              hasGallery: false, // Nem érkezik ilyen adat
            }))
            ?.slice(0, limit)
        ),
        catchError(() => {
          return of(undefined);
        })
      );
  }
}
