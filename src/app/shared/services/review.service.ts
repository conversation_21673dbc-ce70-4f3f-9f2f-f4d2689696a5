import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { ApiListResult, ArticleReview } from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class ReviewService {
  constructor(private readonly reqService: ReqService) {}

  getArticleReviews(reviewedArticleId: string): Observable<ApiListResult<ArticleReview>> {
    return this.reqService.get(`content-page/reviewed-article/${reviewedArticleId}/reviews`, {});
  }

  getReviews(reviewedArticleId: string): Observable<ArticleReview[]> {
    return this.reqService
      .get<ApiListResult<ArticleReview>>(`content-page/reviewed-article/${reviewedArticleId}/reviews`, {
        params: {
          rowCount_limit: '100',
        },
      })
      .pipe(map(({ data }) => data));
  }
}
