import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { ApiListResult, ApiResponseMetaList, ApiResult, backendDateToDate, BasicDossier, DossierArticle } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export const DOSSIERS_PER_PAGE = 10;

@Injectable({
  providedIn: 'root',
})
export class DossierService {
  constructor(private readonly reqService: ReqService) {}

  public getDossier(dossierSlug: string, page = 0, itemsPerPage = 20): Observable<ApiResult<DossierArticle[], ApiResponseMetaList & Partial<BasicDossier>>> {
    return this.reqService
      .get<ApiResult<DossierArticle[], ApiResponseMetaList & Partial<BasicDossier>>>(`content-group/dossiers/${dossierSlug}`, {
        params: {
          rowCount_limit: itemsPerPage.toString(),
          page_limit: page.toString(),
        },
      })
      .pipe(
        map(({ data, meta }) => ({
          meta,
          data: data.map((article) => ({
            ...article,
            publishDate: backendDateToDate(article.publishDate as any as string) as Date,
          })),
        }))
      );
  }

  public getAllDossiers(page = 0, itemsPerPage = 15): Observable<ApiListResult<BasicDossier>> {
    return this.reqService.get('content-group/dossiers', {
      params: {
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }
}
