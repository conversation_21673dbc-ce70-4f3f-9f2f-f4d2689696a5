import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import {
  ApiListResult,
  ApiResponseMetaList,
  ApiResult,
  BackendGallery,
  BackendGalleryDetails,
  GalleriesResult,
  GalleryDetails,
  GalleryRecommendationData,
  mapBackendGalleriesResultToGalleriesResult,
  mapBackendGalleryDetailsResultToGalleryDetails,
} from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class GalleryService {
  constructor(private readonly reqService: ReqService) {}

  public getGalleries(from: number, count: number): Observable<GalleriesResult> {
    const params: IHttpOptions['params'] = {
      rowFrom_limit: String(from),
      rowCount_limit: String(count),
    };

    return this.reqService
      .get<ApiResult<BackendGallery[], ApiResponseMetaList>>('media/galleries', { params })
      .pipe(map(mapBackendGalleriesResultToGalleriesResult));
  }

  public getGalleryDetails(slug: string): Observable<GalleryDetails> {
    return this.reqService
      .get<ApiResult<BackendGalleryDetails[], ApiResponseMetaList>>(`media/gallery/${slug}`)
      .pipe(map(mapBackendGalleryDetailsResultToGalleryDetails));
  }

  public getGalleryRecommendationDatas(slug: string): Observable<ApiListResult<GalleryRecommendationData>> {
    return this.reqService.get<ApiListResult<GalleryRecommendationData>>(`media/gallery/${slug}/recommendation`);
  }
}
