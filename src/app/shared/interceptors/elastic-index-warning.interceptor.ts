import { HttpEvent, HttpHandlerFn, HttpRequest, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { ApiResult } from '@trendency/kesma-ui';
import { environment } from '../../../environments/environment';

export function elasticIndexWarningInterceptor(req: HttpRequest<unknown>, next: HttpHandlerFn): Observable<HttpEvent<any>> {
  return next(req).pipe(
    tap((event) => {
      if (!environment.production && event instanceof HttpResponse && event.body) {
        const res = event.body as ApiResult<any>;
        if (res.meta && res.meta['es'] === false) {
          console.error('[ !!! ES INDEX MISS !!! ]' + 'The request made to the following url was missed the ES index. Please correct this mistake!', req);
        }
      }
    })
  );
}
