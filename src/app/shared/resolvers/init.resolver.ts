import { Injectable } from '@angular/core';
import {
  DossierArticleShort,
  DossierCard,
  InitResolverData,
  InitResponse,
  LiveSport,
  PortalConfigSetting,
  SimplifiedMenuTree,
  TrendingTag,
} from '@trendency/kesma-ui';
import { forkJoin, Observable, of, switchMap } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiService, EbService, OlimpiaService, PortalConfigService, SportResultService } from '../services';
import { SportCompetitions } from '../definitions';

export const EB_SPORT_TITLE = 'Labdarúgás';
export const EB_COMPETITION_TITLE = 'EB';

@Injectable({ providedIn: 'root' })
export class InitResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly portalConfigService: PortalConfigService,
    private readonly sportResultService: SportResultService,
    private readonly ebService: EbService,
    private readonly olimpiaService: OlimpiaService
  ) {}

  resolve(): Observable<InitResolverData> {
    return forkJoin([
      this.apiService.init().pipe(
        map(({ data }) => {
          this.portalConfigService.setConfig(data.portalConfigs);
          return data;
        }),
        switchMap((init: InitResponse) => {
          if (
            !this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS) &&
            !this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_OLYMPICS_ELEMENTS)
          ) {
            return of(init as InitResponse);
          }
          return this.sportResultService.getLiveSports().pipe(
            map(({ data }) => data.find(({ title }) => title === EB_SPORT_TITLE) as LiveSport),
            switchMap(({ id }) => {
              return this.sportResultService.getCompetitions(id).pipe(
                map((comp) => comp?.data?.find((compData) => compData?.publicTitle === EB_COMPETITION_TITLE) as SportCompetitions),
                map((eb_comp) => {
                  this.ebService.didYouKnowData = init?.didYouKnow || [];
                  this.olimpiaService.didYouKnowData = init?.didYouKnow || [];
                  this.ebService.setSlug(eb_comp?.competition?.slug);
                  return init;
                })
              );
            })
          );
        }),
        catchError(() => {
          return of({} as InitResponse);
        })
      ) as Observable<InitResponse>,
      this.apiService.getMenu().pipe(
        catchError(() => {
          return of({} as SimplifiedMenuTree);
        })
      ) as Observable<SimplifiedMenuTree>,
      this.apiService.getDossiers().pipe(
        map((res) => {
          return of(
            res.data.map((data: DossierCard) => {
              return {
                title: data.title,
                slug: data?.slug,
                headerImage: data.thumbnail,
                mainArticle: {} as DossierArticleShort,
                secondaryArticles: [],
              };
            })
          );
        }),
        catchError(() => {
          return of([] as DossierCard[]);
        })
      ) as unknown as Observable<DossierCard[]>,

      this.apiService.getTagsOnHeaderBar().pipe(
        map(({ data }) => {
          return data.map((data: TrendingTag) => {
            return {
              ...data,
              thumbnail: data.thumbnailUrl,
            };
          });
        }),
        catchError(() => {
          return of([] as TrendingTag[]);
        })
      ) as Observable<TrendingTag[]>,
    ]).pipe(
      map<[InitResponse, SimplifiedMenuTree, DossierCard[], TrendingTag[]], InitResolverData>(([init, menu, dossiers, tags]) => ({
        init,
        menu,
        dossiers,
        tags,
      }))
    );
  }
}
