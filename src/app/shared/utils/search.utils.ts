import { ArticleSearchResult, BackendArticleSearchResult, backendDateToDate, FilterParams } from '@trendency/kesma-ui';

export function backendArticlesSearchResultsToArticleSearchResArticles(article: BackendArticleSearchResult): ArticleSearchResult {
  const [year, month] = article.publishDate.split('-');
  return {
    ...article,
    length: parseInt(article.length, 10),
    year,
    month,
    columnTitleColor: '',
    publishDate: backendDateToDate(article.publishDate as string) as Date,
  } as unknown as ArticleSearchResult;
}

export function calculateFilters(filters: FilterParams): FilterParams {
  // if tagSlugs exists then global_filter should be deleted
  if (filters['tagSlugs[]'] && filters.global_filter) {
    // delete operator doesn't work somehow :O
    filters = Object.fromEntries(Object.entries(filters).filter(([key]) => key !== 'global_filter'));
  }

  // if from_date doesn't exist then to_date should be deleted
  if (!filters.from_date && filters.to_date) {
    filters = Object.fromEntries(Object.entries(filters).filter(([key]) => key !== 'to_date'));
  }

  return filters;
}

export function calculatePageTypeFilters(filters: FilterParams, pageParams: FilterParams): FilterParams {
  if (pageParams['columnSlugs[]']) {
    filters = { ...filters, 'columnSlugs[]': pageParams['columnSlugs[]'] };
  }

  return filters;
}
