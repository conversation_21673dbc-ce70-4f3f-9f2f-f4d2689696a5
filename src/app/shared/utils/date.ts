import { DateInterval } from '@trendency/kesma-ui';
import { format } from 'date-fns';

export const todayDateInterval = (): DateInterval => {
  return {
    date_from: format(new Date(), 'yyyy-MM-dd') ?? undefined,
    date_until: format(new Date(), 'yyyy-MM-dd') ?? undefined,
  };
};

export const tomorrowDatepare = (): DateInterval => {
  const todayMonthDay = new Date().getDate();
  const tomorrow = new Date(new Date().setDate(todayMonthDay + 1));
  return {
    date_from: format(tomorrow, 'yyyy-MM-dd') ?? undefined,
    date_until: format(tomorrow, 'yyyy-MM-dd') ?? undefined,
  };
};

export const nextWeekDateInterval = (): DateInterval => {
  const today = new Date();
  const todayWeekDay = today.getDay() === 0 ? 7 : today.getDay();
  const todayMonthDay = today.getDate();
  const nextMondayWeekDay = 8;
  const nextSundayWeekDay = 14;
  const needToAddMonday = nextMondayWeekDay - todayWeekDay;
  const needToAddSunday = nextSundayWeekDay - todayWeekDay;
  const monday = new Date(new Date().setDate(todayMonthDay + needToAddMonday));
  const sunday = new Date(new Date().setDate(todayMonthDay + needToAddSunday));
  return {
    date_from: format(monday, 'yyyy-MM-dd') ?? undefined,
    date_until: format(sunday, 'yyyy-MM-dd') ?? undefined,
  };
};

export const thisWeekEndDateInterval = (): DateInterval => {
  const today = new Date();
  const todayWeekDay = today.getDay() === 0 ? 7 : today.getDay();
  const todayMonthDay = today.getDate();
  const saturdayWeekDay = 6;
  const sundayWeekDay = 7;
  const needToAddSaturday = saturdayWeekDay - todayWeekDay;
  const needToAddSunday = sundayWeekDay - todayWeekDay;
  const saturday = new Date(new Date().setDate(todayMonthDay + needToAddSaturday));
  const sunday = new Date(new Date().setDate(todayMonthDay + needToAddSunday));
  return {
    date_from: format(saturday, 'yyyy-MM-dd') ?? undefined,
    date_until: format(sunday, 'yyyy-MM-dd') ?? undefined,
  };
};
