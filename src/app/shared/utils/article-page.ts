import {
  Article,
  ArticleBody,
  ArticleBodyType,
  ArticleCard,
  BackendArticle,
  backendDateToDate,
  BackendDossierArticle,
  BackendRecommendedArticle,
  backendVotingDataToVotingData,
  BasicDossier,
  DossierArticle,
  getPrimaryColumnColorComboByColumnTitleColor,
  PreviewBackendArticle,
  SubsequentDossier,
} from '@trendency/kesma-ui';

export const backendArticlesToArticles = (article: BackendArticle): Article => {
  const { lastUpdated, publishDate: pubDate, dossier } = article;
  let publishDate: Date | undefined;
  let year = 0;
  let month = 0;
  if (pubDate) {
    publishDate = backendDateToDate(typeof pubDate === 'string' ? pubDate : pubDate.date) ?? undefined;
    year = publishDate?.getUTCFullYear() ?? 0;
    month = publishDate?.getUTCMonth() ?? 0;
  }

  const last: Date | undefined = lastUpdated ? (backendDateToDate(lastUpdated) ?? undefined) : undefined;
  const body: ArticleBody[] = article.body.map((element: ArticleBody) => {
    if (element.type === ArticleBodyType.Voting) {
      const votingValue = backendVotingDataToVotingData(element.details[0]?.value);
      const detail = { ...element.details[0], value: votingValue };
      return { ...element, details: [detail] };
    }
    return element;
  });
  return {
    ...article,
    publicAuthor: article?.publicAuthor ?? 'metropol',
    dossier: dossier?.[0],
    lastUpdated: last,
    publishDate,
    year,
    month,
    preTitle: article?.preTitle,
    tag: article?.tags?.[0],
    columnSlug: article.primaryColumn?.slug,
    columnTitle: article?.primaryColumn?.title,
    primaryColumnColorCombo: article?.primaryColumn?.titleColor ? getPrimaryColumnColorComboByColumnTitleColor(article?.primaryColumn?.titleColor) : undefined,
    body,
  };
};

export const backendDossierArticleToDossierArticle = (dossier: BackendDossierArticle): DossierArticle =>
  dossier && {
    ...dossier,
    publishDate: backendDateToDate(dossier.publishDate) as Date,
    length: dossier.length ? parseInt(dossier.length as string, 10) : undefined,
    thumbnailCreatedAt: dossier.thumbnailCreatedAt ? (backendDateToDate(dossier.thumbnailCreatedAt) ?? undefined) : undefined,
    regions: [],
    tags: [],
  };

export const backendRecommendedArticleToArticleCard = ({
  id,
  slug,
  title,
  publishDate,
  thumbnailUrl,
  thumbnail,
  excerpt,
  readingLength,
  columnTitle,
  columnTitleColor,
  titleColor,
  columnSlug,
  preTitle,
  preTitleColor,
  regions,
  tags,
}: BackendRecommendedArticle): ArticleCard => {
  const [publishYear, publishMonth] = publishDate.split('-');
  return {
    id,
    title,
    slug,
    regions,
    tags,
    publishDate: backendDateToDate(publishDate) as Date,
    publishMonth,
    publishYear,
    thumbnail:
      thumbnailUrl || thumbnail
        ? {
            url: thumbnailUrl || thumbnail || '',
          }
        : undefined,
    category: {
      name: columnTitle,
      slug: columnSlug,
    },
    readingTime: readingLength?.toString(),
    columnTitle: columnTitle,
    preTitle: preTitle,
    primaryColumnColorCombo:
      columnTitleColor || columnTitleColor ? getPrimaryColumnColorComboByColumnTitleColor(columnTitleColor ? columnTitleColor : (titleColor ?? '')) : undefined,
    columnSlug,
    preTitleColor,
    lead: excerpt,
    label: {
      text: preTitle ?? '',
    },
  };
};

export const previewBackendArticleToArticleCard = ({
  id,
  slug,
  title,
  publishDate,
  primaryColumn,
  column,
  preTitle,
  excerpt,
  thumbnailUrl,
}: PreviewBackendArticle): ArticleCard => {
  if (!id) {
    id = '';
  }
  let publishDateFromBackend: Date | undefined;
  let publishYear = 0;
  let publishMonth = 0;
  if (publishDate) {
    publishDateFromBackend = backendDateToDate(typeof publishDate === 'string' ? publishDate : publishDate.date) ?? undefined;
    publishYear = publishDateFromBackend?.getUTCFullYear() ?? 0;
    publishMonth = (publishDateFromBackend?.getUTCMonth() ?? 0) + 1;
  }

  return {
    id,
    slug,
    title,
    preTitle,
    excerpt,
    publishDate: publishDateFromBackend,
    columnTitle: column?.title,
    columnSlug: column?.slug,
    thumbnail: thumbnailUrl
      ? {
          url: thumbnailUrl,
        }
      : undefined,
    primaryColumnColorCombo: primaryColumn?.titleColor ? getPrimaryColumnColorComboByColumnTitleColor(primaryColumn?.titleColor) : undefined,
    publishYear,
    publishMonth,
    category: { ...primaryColumn, name: primaryColumn?.title },
    label: {
      text: 'Ezt ne hagyd ki!',
    },
  };
};

export const subsequentDossierToBasicDossier = ({
  slug,
  title,
  coverImage: thumbnailUrl,
  relatedArticles: articles,
}: SubsequentDossier): BasicDossier<Date> | undefined =>
  slug
    ? {
        slug,
        title,
        thumbnailUrl,
        articles: articles?.map((article) => ({
          ...article,
          publishDate: backendDateToDate(article.publishDate) as Date,
        })),
      }
    : undefined;
