import { ApiResponseMetaList, ApiResult, ArticleCard, Layout, LayoutWithExcludeIds } from '@trendency/kesma-ui';
import { CategoryResolverResponse } from '../../feature/category/category.definitions';

export const mapCategoryResponse = (
  categoryResponse: ApiResult<ArticleCard[], ApiResponseMetaList>,
  slug: string,
  year = '',
  month = '',
  layoutResponse: LayoutWithExcludeIds = { data: null, excludedIds: [], columnTitle: '' },
  noLayoutData = false
): CategoryResolverResponse => ({
  layoutApiResponse: (noLayoutData ? null : layoutResponse?.data) as Layout,
  columnTitle: layoutResponse.columnTitle,
  columnParentSlug: layoutResponse.columnParentSlug,
  columnParentTitle: String(categoryResponse?.meta?.['columnParentTitle']),
  excludedIds: layoutResponse?.excludedIds,
  category: categoryResponse as ApiResult<ArticleCard[], ApiResponseMetaList>,
  slug,
  year,
  month,
});
