import { format, subDays, subMonths, subWeeks, subYears } from 'date-fns';

export const SEARCH_FILTER_DATE_FORMAT = 'yyyy-MM-dd';

export const publishFilters: { label: string; value: string }[] = [
  {
    label: 'Dátum tartomány',
    value: '',
  },
  {
    label: 'Az utóbbi 24 órában',
    value: format(subDays(new Date(), 1), SEARCH_FILTER_DATE_FORMAT),
  },
  {
    label: 'Az utóbbi egy héten',
    value: format(subWeeks(new Date(), 1), SEARCH_FILTER_DATE_FORMAT),
  },
  {
    label: 'Utóbbi egy hónapban',
    value: format(subMonths(new Date(), 1), SEARCH_FILTER_DATE_FORMAT),
  },
  {
    label: 'Az utóbbi egy évben',
    value: format(subYears(new Date(), 1), SEARCH_FILTER_DATE_FORMAT),
  },
];

export const articleTypeFilters: { label: string; value: string }[] = [
  {
    label: '<PERSON>ikk típusa',
    value: '',
  },
  {
    label: 'Vélemény',
    value: 'opinion',
  },
  {
    label: 'Videós tartalom',
    value: 'articleVideo',
    // value: 'video'
  },
  {
    label: 'Rövid hír',
    value: 'shortNews',
  },
  {
    label: 'Podcast tartalom',
    value: 'articlePodcast',
  },
  {
    label: 'Jegyzet',
    value: 'notebook',
  },
];

export const sortOptions: { label: string; value: string }[] = [
  {
    label: 'Legfrissebb elöl',
    value: 'desc',
  },
  {
    label: 'Legrégebbi elöl',
    value: 'asc',
  },
];
