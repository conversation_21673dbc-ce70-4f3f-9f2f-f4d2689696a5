import { StaticMenuItem } from '../definitions';

export const DEFAULT_RSS_LINK = '/publicapi/hu/rss/magyar_nemzet/articles';
export const DEFAULT_SUBSCRIBE_LINK = '/hirlevel/feliratkozas';
export const buildSocialMenu = (
  facebookLink: string,
  instagramLink: string,
  twitterLink: string,
  videaLink: string,
  youtubeLink: string,
  addSubscribe = false,
  rssLink = DEFAULT_RSS_LINK,
  subscribeLink = DEFAULT_SUBSCRIBE_LINK,
  subscriptionClick: (event?: MouseEvent) => void = () => {}
): StaticMenuItem[] => [
  ...(addSubscribe
    ? [
        {
          icon: 'mail',
          link: subscribeLink,
          title: '<PERSON><PERSON>rle<PERSON><PERSON><PERSON> felirat<PERSON>',
          click: subscriptionClick,
        },
      ]
    : []),
  { icon: 'facebook', link: facebookLink, title: 'Facebook' },
  { icon: 'instagram', link: instagramLink, title: 'Instagram' },
  { icon: 'twitter', link: twitterLink, title: 'Twitter' },
  { icon: 'videa', link: videaLink, title: 'Videa' },
  { icon: 'youtube', link: youtubeLink, title: 'YouTube' },
  { icon: 'rss', link: rssLink, title: 'RSS' },
];
