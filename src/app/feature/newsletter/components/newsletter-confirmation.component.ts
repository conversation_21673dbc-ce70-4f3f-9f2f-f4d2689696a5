import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import { defaultMetaInfo } from '../../../shared';

@Component({
  selector: 'app-newsletter-confirmation',
  templateUrl: './newsletter-confirmation.component.html',
  styleUrls: ['./newsletter-confirmation.component.scss'],
})
export class NewsletterConfirmationComponent implements OnInit {
  constructor(
    private readonly seo: SeoService,
    private readonly route: ActivatedRoute
  ) {}

  email: string;

  ngOnInit(): void {
    const title = 'Hírlevél | Magyar Nemzet';
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    });

    this.email = this.route.snapshot?.queryParamMap?.get('email') ?? '';
  }
}
