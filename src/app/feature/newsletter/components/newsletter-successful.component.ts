import { Component, OnInit } from '@angular/core';
import { SeoService } from '@trendency/kesma-core';
import { defaultMetaInfo } from '../../../shared';

@Component({
  selector: 'app-newsletter-successful',
  templateUrl: './newsletter-successful.component.html',
  styleUrls: ['./newsletter-successful.component.scss'],
})
export class NewsletterSuccessfulComponent implements OnInit {
  constructor(private readonly seo: SeoService) {}

  ngOnInit(): void {
    const title = 'Hírlevél | Magyar Nemzet';
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    });
  }
}
