@use 'shared' as *;

.main {
  max-width: 614px;
  margin: auto;
  display: flex;
  flex-direction: column;
  align-content: center;
  padding-bottom: 80px;

  @include media-breakpoint-down(sm) {
    padding-left: 10px;
    padding-right: 10px;
  }

  h1 {
    font-family: var(--kui-font-secondary);
    font-size: 36px;
    font-style: normal;
    font-weight: 700;
    line-height: 48px;
    letter-spacing: 0;
    text-align: center;
    margin-bottom: 50px;
    margin-top: 50px;
  }

  p {
    margin: 0 0 25px 0;
    font-family: var(--kui-font-primary);
    font-size: 18px;
    font-style: normal;
    line-height: 32px;
    letter-spacing: 0;
    text-align: center;
    word-break: break-word;
  }
}
