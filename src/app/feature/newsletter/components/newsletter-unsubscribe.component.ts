import { Component, OnInit } from '@angular/core';
import { SeoService } from '@trendency/kesma-core';
import { defaultMetaInfo } from '../../../shared';

@Component({
  selector: 'app-newsletter-unsubscribe',
  templateUrl: './newsletter-unsubscribe.component.html',
  styleUrls: ['./newsletter-unsubscribe.component.scss'],
})
export class NewsletterUnsubscribeComponent implements OnInit {
  constructor(private readonly seo: SeoService) {}

  ngOnInit(): void {
    const title = 'Hírlevél | Magyar Nemzet';
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    });
  }
}
