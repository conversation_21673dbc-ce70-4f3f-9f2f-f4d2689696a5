@use 'shared' as *;

$color_1: #000000;
$color_2: #ffffff;
$color_3: #2b2f33;
$background-color_1: #ffffff;

.form-input-container {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: stretch;
  width: 100%;
}

.automizy-form-form {
  box-sizing: border-box;
  max-width: 840px;
  width: 100%;
  margin: 24px auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 40px;

  @include media-breakpoint-down(sm) {
    padding: 0 16px;
  }
}

.automizy-form-fields {
  display: flex;
  flex-direction: column;
  gap: 40px;

  @include media-breakpoint-down(sm) {
    gap: 16px;
  }
}

.automizy-form-title {
  font-family: var(--kui-font-secondary);
  font-size: 24px;
  font-weight: 700;
  line-height: 30px;
  text-align: center;

  @include media-breakpoint-down(sm) {
    text-align: left;
    padding: 12px 24px 12px 24px;
  }
}

.automizy-form-description {
  font-size: 16px;
  font-weight: 700;
  line-height: 21px;
}

.automizy-form-button-box {
  text-align: center;
}

.automizy-form-privacy {
  text-align: left;
  font-size: 14px;
  color: $color_1;
  line-height: 1.5;
  font-weight: normal;
  font-style: normal;
  text-decoration: none;
  padding: 12px 24px 12px 24px;
}

.automizy-form-checkbox {
  padding: 12px 24px 12px 24px;

  &-label {
    display: flex;
    align-items: baseline;
    justify-content: flex-start;
  }

  &-input {
    margin: 0;
    min-width: 15px;
    min-height: 15px;
  }

  &-content {
    margin: 0;
    display: inline-block;
    text-align: left;
    padding: 0 0 0 12px;
  }
}

.automizy-form-button {
  text-align: center;
  width: 100%;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 18px; /* 128.571% */
  padding: 6px 12px;

  @include media-breakpoint-down(sm) {
    font-size: 12px;
  }
}

.form-input-container {
  .automizy-form-input-box {
    flex-direction: column;

    @include media-breakpoint-down(sm) {
      width: 100%;
    }
  }

  @include media-breakpoint-down(sm) {
    flex-direction: column;
  }
}

.automizy-form-input-box {
  display: flex;
  flex: 1;

  .form-error {
    @include transition;
    transition-property: opacity !important;
    display: inline-flex;
    align-items: center;
    justify-content: flex-start;
    color: var(--kui-red);
    font-size: 12px;
    font-weight: 400;
    line-height: 16px; /* 133.333% */
    gap: 4px;
    opacity: 0;
  }

  .automizy-form-input {
    margin: 0;
    box-sizing: border-box;
    padding: 10px 8px;
    border: 1px solid var(--kui-slate-500);
    border-radius: 2px;
    width: 100%;

    &::placeholder {
      color: var(--kui-slate-500);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px; /* 150% */
    }

    &:user-invalid + .form-error {
      opacity: 1;
      @include transition;
      transition-property: opacity !important;
    }
  }

  .automizy-form-input-label {
    display: none;
  }
}

.submitted {
  .automizy-form-input-box {
    .automizy-form-input:invalid + .form-error {
      opacity: 1;
      @include transition;
      transition-property: opacity !important;
    }
  }
}

.radios {
  display: flex;
  align-items: center;
  justify-content: space-between;

  @include media-breakpoint-down(sm) {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .automizy-form-input {
    width: unset;
    margin-right: 8px;

    &-box {
      flex: unset;
    }
  }

  input[type='radio']:checked + label {
    color: var(--kui-blue-500);
  }

  .automizy-form-input-label {
    display: block;
    color: var(--kui-slate-600);
    font-size: 16px;
    font-weight: 700;
    line-height: 21px;
  }
}

.checkbox-label {
  text-align: left;
  padding: 0 0 0 8px;
  line-height: 1.5;
}

.checkbox {
  display: block;
  width: 24px;
}
