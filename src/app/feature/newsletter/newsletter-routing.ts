import { Routes } from '@angular/router';
import { NewsletterConfirmationComponent } from './components/newsletter-confirmation.component';
import { NewsletterParentComponent } from './components/newsletter-parent.component';
import { NewsletterSuccessfulComponent } from './components/newsletter-successful.component';
import { NewsletterUnsubscribeComponent } from './components/newsletter-unsubscribe.component';
import { NewsletterComponent } from './newsletter.component';
import { NewsletterType } from '../../shared';

export const NEWSLETTER_ROUTES: Routes = [
  {
    path: '',
    component: NewsletterParentComponent,
    children: [
      {
        path: 'feliratkozas',
        component: NewsletterComponent,
        pathMatch: 'full',
        data: { type: NewsletterType.napi_hirlevel },
      },
      {
        path: 'feliratkozas-2',
        component: NewsletterComponent,
        pathMatch: 'full',
        data: { type: NewsletterType.heti_velemeny_s<PERSON>mle },
      },
      {
        path: 'megerosites',
        component: NewsletterConfirmationComponent,
        pathMatch: 'full',
      },
      {
        path: 'sikeres',
        component: NewsletterSuccessfulComponent,
        pathMatch: 'full',
      },
      {
        path: 'leiratkozas',
        component: NewsletterUnsubscribeComponent,
        pathMatch: 'full',
      },
    ],
  },
];
