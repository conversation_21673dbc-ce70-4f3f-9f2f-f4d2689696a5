import { DOCUMENT } from '@angular/common';
import { Component, ElementRef, Inject, OnInit, Renderer2, ViewChild } from '@angular/core';
import { SeoService } from '@trendency/kesma-core';
import { defaultMetaInfo, NewsletterType } from '../../shared';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { BypassPipe, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { IconComponent } from 'src/app/shared';

@Component({
  selector: 'app-newsletter',
  templateUrl: './newsletter.component.html',
  styleUrls: ['./newsletter.component.scss'],
  imports: [RouterLink, BypassPipe, IconComponent],
})
export class NewsletterComponent implements OnInit {
  @ViewChild('newsletterForm', { static: true, read: ElementRef }) newsletterForm: ElementRef<HTMLFormElement>;

  constructor(
    private readonly seo: SeoService,
    private readonly renderer2: Renderer2,
    private readonly route: ActivatedRoute,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  recaptcha = 'https://www.google.com/recaptcha/api.js';
  action = 'https://api.automizy.com/v2/forms/submit/jYSVkq-I46lk25t1X6NklFqnOd0l3lddHzhnvnJM6XY/n5pqJVgtl1t_uyfx5-xReqQprEA';
  submitted = false;
  type: NewsletterType = NewsletterType.napi_hirlevel;
  NewsletterType = NewsletterType;

  ngOnInit(): void {
    this.type = this.route?.snapshot?.data?.['type'];
    const canonical = createCanonicalUrlForPageablePage('hirlevel/feliratkozas');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
    const title = 'Hírlevél | Magyar Nemzet';
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    });
    this.renderRecaptcha();
  }

  private renderRecaptcha(): void {
    const s = this.renderer2.createElement('script');
    s.type = 'text/javascript';
    s.src = this.recaptcha;
    s.text = '';
    this.renderer2.appendChild(this.document.body, s);
  }

  onSubmitClick($event: MouseEvent): void {
    this.submitted = true;
    if (!this.newsletterForm?.nativeElement?.checkValidity()) {
      $event.preventDefault();
      $event.stopPropagation();
      $event.stopImmediatePropagation();
    }
  }
}
