import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { ApiResponseMetaList, RedirectService, Tag } from '@trendency/kesma-ui';
import { forkJoin, map, Observable, of, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { TagService } from '../../shared';
import { IGlossaryResolverData } from './glossary.definitions';
import { GlossaryService } from './glossary.service';

const GLOSSARY_RELATED_ARTICLES_COUNT = 50;

@Injectable({
  providedIn: 'root',
})
export class GlossaryResolverService {
  constructor(
    private readonly service: GlossaryService,
    private readonly tagService: TagService,
    private readonly router: Router,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<IGlossaryResolverData> {
    const pageIndex = route.queryParams['page'] ?? 0;
    const slug = route.params['slug'];
    return forkJoin({
      glossary: this.service.getGlossary(slug),
      articles: this.tagService.getTagsList(slug, pageIndex, GLOSSARY_RELATED_ARTICLES_COUNT).pipe(
        tap(({ data }) => {
          if (this.redirectService.shouldBeRedirect(pageIndex, data)) {
            this.redirectService.redirectOldUrl(`sportlexikon/${slug}`, false, 302);
          }
        }),
        catchError(() => {
          return of({ data: [], meta: {} as ApiResponseMetaList });
        })
      ),
      tag: this.tagService.getTag(route.params['slug']).pipe(
        map(({ data }) => data),
        catchError(() => {
          return of({} as Tag);
        })
      ),
    }).pipe(
      map(({ tag, glossary, articles: { data: articles, meta: listMeta } }) => ({
        tag,
        glossary,
        articles,
        listMeta,
      })),
      catchError((error) => {
        console.error('Glossary item not found: ', error);
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });
        return throwError(error);
      })
    );
  }
}
