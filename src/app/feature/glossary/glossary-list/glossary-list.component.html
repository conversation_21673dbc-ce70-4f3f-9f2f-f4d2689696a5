<section class="block article">
  <div class="wrapper">
    <div class="glossary-container">
      <h1 class="glossary-title">
        Sport tudástár
        <hr />
        <span class="subtitle">A Magyar Nemzet sport melléklete</span>
      </h1>
      <div *ngIf="glossary$ | async" class="glossary-shortcuts">
        <a
          (click)="scrollToLetter(letter.toLocaleLowerCase(), $event)"
          *ngFor="let letter of allLetters"
          [attr.data-scrollSpyNavItem]="letter.toLocaleLowerCase()"
          [class.disabled]="letters.indexOf(letter.toLocaleLowerCase()) === -1"
          [href]="'#letter-' + letter.toLocaleLowerCase()"
          class="glossary-shortcut-letter"
          ><h2 class="glossary-letter">{{ letter.toLocaleUpperCase() }}</h2></a
        >
      </div>

      <h2 class="glossary-subtitle">Szószedet</h2>
      <ng-container *ngIf="glossary$ | async">
        <ng-container *ngFor="let letter of letters">
          <a [id]="'letter-' + letter.toLocaleLowerCase()"></a>
          <h2 #indexLetter [attr.data-letter]="letter.toLocaleLowerCase()" class="glossary-letter">{{ letter.toLocaleUpperCase() }}</h2>
          <div class="glossary-letter-list">
            <a *ngFor="let glossary of letteredGlossary[letter]" [routerLink]="['/', 'sportlexikon', glossary?.slug]">
              {{ glossary.title }}
            </a>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>
</section>
