import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, On<PERSON><PERSON>roy, OnInit, ViewChildren } from '@angular/core';
import { Observable, Subject, tap } from 'rxjs';
import { Glossary } from '../glossary.definitions';
import { GlossaryService } from '../glossary.service';
import { accentedCompareLetters } from '../glossary.utils';
import { AsyncPipe, NgFor, NgIf, ViewportScroller } from '@angular/common';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { takeUntil } from 'rxjs/operators';
import { defaultMetaInfo, HeaderService } from '../../../shared';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';

@Component({
  selector: 'app-glossary-list',
  templateUrl: './glossary-list.component.html',
  styleUrls: ['./glossary-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, NgIf, NgFor, RouterLink],
})
export class GlossaryListComponent implements OnInit, OnDestroy {
  glossary$: Observable<Glossary[]>;
  glossaryList: Glossary[] = [];
  letters: string[] = [];
  allLetters = 'AÁBCDEÉFGHIÍJKLMNOÓÖŐPQRSTUÚÜŰVWXYZ'.split('');
  letteredGlossary: Record<string, Glossary[]> = {};
  activeLetter: string;
  headerOffset: number;
  headerHeight: number;
  @ViewChildren('indexLetter', { read: ElementRef }) indexLetters: ElementRef<HTMLHeadingElement>[];
  readonly #destroy$ = new Subject<boolean>();

  constructor(
    private readonly glossaryService: GlossaryService,
    private readonly viewportScroller: ViewportScroller,
    private readonly utilService: UtilService,
    private readonly cdr: ChangeDetectorRef,
    private readonly headerService: HeaderService,
    private readonly seo: SeoService,
    private readonly route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.glossary$ = this.glossaryService.getGlossaryList().pipe(
      tap((glossaryList) => {
        this.letters = (
          [
            ...glossaryList
              .map((item) => item.title?.trim()?.[0])
              .reduce((letters, letter) => {
                if (!letters.has(letter?.toLowerCase())) letters.add(letter?.toLowerCase());
                return letters;
              }, new Set())
              .values(),
          ] as string[]
        ).sort(accentedCompareLetters) as string[];

        this.glossaryList = glossaryList;
        this.letteredGlossary = this.letters.reduce(
          (all, letter) => ({
            ...all,
            [letter]: this.glossaryByLetter(letter),
          }),
          {} as Record<string, Glossary[]>
        );
        this.setMetaData();
      })
    );
    this.getHeaderOffset();
    this.getHeaderHeight();
  }

  ngOnDestroy(): void {
    this.#destroy$.next(true);
    this.#destroy$.complete();
  }

  glossaryByLetter(letter: string): Glossary[] {
    return this.glossaryList.filter(({ title }) => title?.[0].toLowerCase() === letter.toLowerCase()) ?? [];
  }

  getHeaderOffset(): void {
    this.headerService.headerTopOffset$.pipe(takeUntil(this.#destroy$)).subscribe((top) => {
      this.headerOffset = top;
      this.cdr.detectChanges();
    });
  }

  getHeaderHeight(): void {
    this.headerService.headerHeight$.pipe(takeUntil(this.#destroy$)).subscribe((height) => {
      this.headerHeight = height;
      this.cdr.detectChanges();
    });
  }

  scrollToLetter(letter: string, event: MouseEvent): void {
    event.preventDefault();

    this.indexLetters.forEach(({ nativeElement }) => {
      if (letter === nativeElement.dataset['letter']) {
        this.activeLetter = letter;
        this.viewportScroller.setOffset([0, this.headerHeight]);
        this.viewportScroller.scrollToAnchor('letter-' + letter);

        // Should call again scrollToAnchor function because at the very first time the header is not "sticky" yet
        // When the header is becoming "sticky", the scrollToAnchor function will not work
        if (this.utilService.isBrowser()) {
          setTimeout(() => {
            this.viewportScroller.scrollToAnchor('letter-' + letter);
          }, 200);
        }
      }
    });
  }

  private setMetaData(): void {
    const title = `Sportlexikon | Magyar Nemzet`;
    const canonical = createCanonicalUrlForPageablePage('sportlexikon', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical, { addHostUrl: true });
    this.seo.setMetaData({
      ...defaultMetaInfo,
      ogTitle: title,
      title,
    });
  }
}
