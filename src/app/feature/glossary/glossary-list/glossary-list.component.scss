@use 'shared' as *;

:host {
  display: block;
}

.glossary {
  &-container {
    width: 100%;
    margin: 0 auto;

    @include media-breakpoint-down(md) {
      width: calc(100% - 440px);
    }

    @include media-breakpoint-down(sm) {
      width: calc(100% - 20px);
      margin: 0 auto;
      > .glossary-letter {
        float: left;
        width: 54px;
        height: 54px;
        margin-right: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 36px;
        font-weight: 700;
        line-height: 44px; /* 122.222% */
      }
      > .glossary-letter-list {
        grid-template-columns: 1fr;
        gap: 0;
        margin-bottom: 20px;

        > a {
          height: 40px;
          align-items: center;
          justify-content: flex-start;
          display: inline-flex;
        }
      }
      > a {
        clear: both;
        display: block;
        width: 100%;
      }
    }
  }

  &-title {
    font-size: 24px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 32px;
    margin: 24px 0 0;
    padding: 32px 0;
    text-align: center;
    color: var(--kui-white);
    background-image: url('/assets/images/glossary-title-bg.png');
    background-repeat: no-repeat;
    background-size: cover;

    @include media-breakpoint-down(sm) {
      padding: 19px 20px;
      margin: 8px 0 16px;

      font-family: var(--kui-font-primary);
      font-size: 20px;
      font-weight: 800;
      line-height: 26px; /* 130% */
      letter-spacing: 0.3px;
    }

    .subtitle {
      font-family: var(--kui-font-secondary);
      margin-left: 16px;
      padding-left: 16px;
      border-left: 1px solid var(--kui-white);

      @include media-breakpoint-down(sm) {
        margin-left: 0;
        padding-left: 0;
        border-left: 0;
        display: block;
        width: 100%;
        font-family: var(--kui-font-primary);
        font-size: 14px;
        font-weight: 600;
        line-height: 20px; /* 142.857% */
      }
    }

    > hr {
      width: 32px;
      height: 1px;
      border: solid var(--kui-white);
      border-width: 1px 0 0 0;
      @include media-breakpoint-up(md) {
        display: none;
      }
    }
  }

  &-shortcuts {
    display: grid;
    grid-template-columns: repeat(18, 1fr);
    background-color: var(--kui-blue-900);
    padding: 16px;
    gap: 16px;
    margin-bottom: 32px;

    @include media-breakpoint-down(md) {
      overflow-x: scroll;
    }
  }

  &-subtitle {
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;
    letter-spacing: 0.015em;
    color: var(--kui-blue-900);
    margin-bottom: 16px;
  }

  &-letter {
    margin-bottom: 15px;
    border-bottom: 2px dotted var(--kui-slate-300);
    font-family: var(--kui-font-secondary);
    color: var(--kui-blue-500);

    &-list {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      gap: 32px;
      margin-bottom: 35px;

      a {
        margin-bottom: 10px;
        transition: 0.2s ease-out;

        &,
        &:visited {
          color: var(--kui-black);
        }

        &:hover,
        &:focus,
        &:active {
          color: var(--kui-red);
          transition: 0.2s ease-out;
        }
      }
    }
  }

  &-shortcut-letter {
    margin: 0;
    padding: 0;

    .glossary-letter {
      border: 1px solid var(--kui-slate-600) !important;

      color: var(--kui-white);
      font-family: var(--kui-font-primary);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      font-weight: 800;
      line-height: 22px;
      text-align: center;
      padding: 10px;
      margin: 0;
    }

    @include transition;

    &,
    &:visited,
    &:focus,
    &:active {
      @include transition;
      color: var(--kui-slate-50);
    }

    &:hover {
      @include transition;
      background-color: var(--kui-blue-500);
    }

    &.disabled {
      cursor: not-allowed;

      .glossary-letter {
        color: var(--kui-slate-400) !important;
      }
    }
  }
}

.wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.sidebar {
  width: 300px;
}
