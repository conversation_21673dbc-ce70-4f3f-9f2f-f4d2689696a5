import { backendDateToDate } from '@trendency/kesma-core';
import { Glossary } from './glossary.definitions';

export const deaccent = (input: string): string => input.normalize('NFD').replace(/[\u0300-\u036f]/g, '');

export const mapBackendGlossaryToGlossary = ({ id, title, slug, description, startMatch, endMatch, updatedAt }: Glossary<string>): Glossary<Date> =>
  id || title
    ? {
        id,
        title,
        slug,
        description,
        startMatch,
        endMatch,
        updatedAt: backendDateToDate(updatedAt ?? ''),
      }
    : ({} as Glossary);

export const sortGlossary = (glossaryList: Glossary[]): Glossary[] =>
  (glossaryList ?? []).sort((a, b) => {
    if (a.startMatch?.length === b.startMatch?.length) {
      return (b.endMatch?.length ?? 0) - (a.endMatch?.length ?? 0);
    }
    return (b.startMatch ?? '').length - (a.startMatch ?? '').length;
  });

const letters = 'aábcdeéfghiíjklmnoóöőpqrstuúüűvwxyz';
export const accentedCompareLetters = (a: string, b: string): number => {
  const indexA = letters.indexOf(a);
  const indexB = letters.indexOf(b);
  return indexA === indexB ? 0 : indexA < indexB ? -1 : 1;
};

export const toHtml = (str?: string): string => {
  if (!str || str?.match(/^</)) return '';
  const paragraphs = str.split(/\n{2,}/);
  const breaks = paragraphs.join('</p><p>').split('\n');
  return `
<p>
    ${breaks.join('<br>\n')}
</p>`;
};
