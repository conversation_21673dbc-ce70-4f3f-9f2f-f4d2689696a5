import { DOCUMENT } from '@angular/common';
import { Inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ArticleBodyDetailType, ArticleBodyType, ComponentData, PortalConfigSetting } from '@trendency/kesma-ui';
import { PortalConfigService } from '../../shared';
import { Glossary } from './glossary.definitions';
import { sortGlossary } from './glossary.utils';

@Injectable({
  providedIn: 'root',
})
export class GlossaryProcessorService {
  private baseUrl: string;
  private glossaryList: Glossary[];

  constructor(
    private readonly portalConfigService: PortalConfigService,
    private readonly router: Router,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  init(glossaryList: Glossary[], baseUrl = 'szoszedet'): void {
    if (!this.isEnabled) {
      return;
    }

    this.baseUrl = baseUrl;

    this.glossaryList = sortGlossary(glossaryList);
  }

  linkGlossaryItems(contentData: ComponentData[]): ComponentData[] {
    if (!this.isEnabled) {
      return contentData;
    }

    this.unusedGlossary = [...this.glossaryList];

    return contentData.map(
      (item: ComponentData): ComponentData =>
        item.type === ArticleBodyType.Wysywyg.valueOf()
          ? {
              ...item,
              details: [
                ...item.details.map((detail) =>
                  detail.type === ArticleBodyDetailType.WysywygDetail.valueOf()
                    ? {
                        ...detail,
                        value: this.handleWysiwygContent(detail.value as string),
                      }
                    : (detail as any)
                ),
              ],
            }
          : item
    );
  }

  private handleWysiwygContent(str = ''): string {
    const rootNode = this.document.createElement('div');
    rootNode.innerHTML = str;

    this.processNode(rootNode); // Then we check the whole content again for auto linking

    return rootNode.innerHTML;
  }

  unusedGlossary: Glossary[] = [];

  processTextNode(textNode: Node): Node[] | null {
    let processedNodes: Node[] = [];

    let foundStart = false;

    [...this.unusedGlossary].forEach((rec, glossaryIndex) => {
      if (foundStart) return null;
      if (rec.startMatch) {
        const startReg = new RegExp('[.,!? ]' + rec.startMatch + '[.,!?;: ]', 'i');
        foundStart = !!startReg.exec(' ' + textNode.textContent);
      }

      if (foundStart) {
        this.unusedGlossary.splice(glossaryIndex, 1);

        const splitted = (' ' + textNode.textContent).split(new RegExp('([.,!?;: ]' + rec.startMatch + '[.,!?;: ])', 'i'));
        const anchor: HTMLAnchorElement = this.document.createElement('a');
        anchor.href = this.baseUrl + '/' + rec?.slug;
        anchor.title = 'Szószedet: ' + rec.title;
        anchor.innerText = splitted[1];
        anchor.onclick = (event: MouseEvent): void => {
          event.preventDefault();
          event.stopPropagation();
          this.router.navigate(anchor.href.split('/'));
        };

        processedNodes = processedNodes.concat([
          this.document.createTextNode(splitted[0] + (splitted[1][0] === ' ' ? ' ' : '')),
          anchor,
          this.document.createTextNode(splitted.slice(2).join('')),
        ]);
        return null;
      }

      return null;
    });

    if (processedNodes.length > 1) {
      // as 1 processor runs only until finds 1 match and returns 0 or 3 nodes,
      // if it found a match the returned new nodes need to be reprocessed for possible other glossary item matches
      const dblCheck: Node[] = [...processedNodes];
      processedNodes = [];
      dblCheck.forEach((processedNode) => {
        if (processedNode.nodeType !== Node.TEXT_NODE) {
          processedNodes.push(processedNode);
          return null;
        }

        let reProcessed: Node[] | null = null;
        if (this.unusedGlossary.length) {
          reProcessed = this.processTextNode(processedNode);
        }

        if ((reProcessed as Node[])?.length) {
          processedNodes = processedNodes.concat(reProcessed as Node[]);
        } else {
          processedNodes.push(processedNode);
        }

        return null;
      });

      return processedNodes;
    }

    return null;
  }

  processNode(node: Node): Node[] | null {
    if (node?.childNodes?.length > 0) {
      let processedNodes: Node[] = [];
      Array.from(node.childNodes).forEach((childNode) => {
        const res = this.processNode(childNode);
        if ((res as Node[])?.length) {
          // node needs to be replaced
          processedNodes = processedNodes.concat(res ?? []);
        } else {
          processedNodes.push(childNode);
        }
      });

      if (processedNodes.length > node.childNodes.length) {
        Array.from(node.childNodes).forEach((n) => n.remove());
        processedNodes.forEach((child) => node.appendChild(child));
      }

      return null;
    }

    if (node?.nodeType === Node.TEXT_NODE) {
      const newNodes = this.processTextNode(node);
      if ((newNodes as Node[])?.length) {
        return newNodes;
      }
    }

    return null;
  }

  private get isEnabled(): boolean {
    return this.portalConfigService.isConfigSet(PortalConfigSetting.CONTENT_ARTICLE_GLOSSARY);
  }
}
