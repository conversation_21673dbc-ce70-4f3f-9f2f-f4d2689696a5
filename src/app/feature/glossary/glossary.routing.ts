import { Routes } from '@angular/router';
import { GlossaryListComponent } from './glossary-list/glossary-list.component';
import { GlossaryPageComponent } from './glossary-page/glossary-page.component';
import { GlossaryResolverService } from './glossary.resolver.service';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const GLOSSARY_ROUTES: Routes = [
  {
    path: '',
    component: GlossaryListComponent,
    pathMatch: 'full',
  },
  {
    path: ':slug',
    component: GlossaryPageComponent,
    providers: [GlossaryResolverService],
    resolve: { data: GlossaryResolverService },
    canActivate: [PageValidatorGuard],
  },
];
