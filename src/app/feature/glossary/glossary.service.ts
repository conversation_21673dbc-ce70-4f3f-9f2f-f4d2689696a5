import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { ApiListResult, ApiResult } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Glossary } from './glossary.definitions';
import { mapBackendGlossaryToGlossary, sortGlossary } from './glossary.utils';

@Injectable({
  providedIn: 'root',
})
export class GlossaryService {
  constructor(private readonly reqService: ReqService) {}

  getGlossaryList(): Observable<Glossary[]> {
    return this.reqService
      .get<ApiListResult<Glossary<string>>>('content-page/glossary/list', {
        params: {
          rowCount_limit: '999',
        },
      })
      .pipe(
        map(({ data }) => data?.map(mapBackendGlossaryToGlossary)),
        map(sortGlossary)
      );
  }

  getGlossary(slug: string): Observable<Glossary> {
    return this.reqService.get<ApiResult<Glossary<string>>>(`content-page/glossary/${slug}`).pipe(
      map(({ data }) => ({
        ...mapBackendGlossaryToGlossary(data),
      }))
    );
  }
}
