import { ApiResponseMetaList, ArticleBodyDetailType, ArticleBodyType, ArticleCard, Tag } from '@trendency/kesma-ui';

export interface Glossary<DateType extends Date | string = Date> {
  id?: string;
  title?: string;
  slug: string;
  description?: IGlossaryContent[];
  startMatch?: string;
  endMatch?: string;
  updatedAt?: DateType;
}

export interface IGlossaryResolverData {
  glossary: Glossary;
  articles: ArticleCard[];
  tag?: Tag;
  listMeta: ApiResponseMetaList;
}

export interface IGlossaryContent {
  id?: string;
  type: ArticleBodyType.Wysywyg;
  details?: {
    key: string;
    type: ArticleBodyDetailType.WysywygDetail;
    value: string;
  }[];
  subComponents?: any[];
}
