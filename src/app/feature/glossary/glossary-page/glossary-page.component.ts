import { ChangeDetectionStrategy, ChangeDetectorRef, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  ArticleCard,
  NEWSLETTER_COMPONENT_TYPE,
  Tag,
  WysiwygMain,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { Observable, Subject, takeUntil } from 'rxjs';
import { map } from 'rxjs/operators';
import { ConfigService, defaultMetaInfo } from '../../../shared';
import { Glossary, IGlossaryResolverData } from '../glossary.definitions';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { ArticleCardComponent, ArticleCardType, ArticleTextComponent, PagerComponent } from 'src/app/shared';
import { AsyncPipe, NgFor, NgIf, SlicePipe } from '@angular/common';
import { ModuleHeadingComponent } from 'src/app/shared/components/module-heading/module-heading.component';
import { PageNewsletterBannerComponent } from 'src/app/shared/components/page-newsletter-banner/page-newsletter-banner.component';

@Component({
  selector: 'app-glossary-page',
  templateUrl: './glossary-page.component.html',
  styleUrls: ['./glossary-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AdvertisementAdoceanComponent,
    SidebarComponent,
    PagerComponent,
    ArticleCardComponent,
    AsyncPipe,
    NgIf,
    NgFor,
    SlicePipe,
    ModuleHeadingComponent,
    ArticleTextComponent,
    PageNewsletterBannerComponent,
  ],
})
export class GlossaryPageComponent implements OnInit, OnDestroy {
  article: Glossary;
  tagArticles: ArticleCard[] = [];
  tag?: Tag;
  rowAllCount = 0;
  rowOnPageCount = 0;
  rowFrom = 0;
  description: WysiwygMain = {} as any;

  readonly ArticleCardType = ArticleCardType;
  ads: AdvertisementsByMedium = {} as AdvertisementsByMedium;
  isMobile$: Observable<boolean>;
  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly cd: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly analyticsService: AnalyticsService,
    private readonly configService: ConfigService
  ) {}

  ngOnInit(): void {
    this.isMobile$ = this.configService.isMobile$;
    this.route.data.pipe(takeUntil(this.destroy$)).subscribe((routeData) => {
      const {
        data: { glossary, articles, tag, listMeta },
      } = routeData as { data: IGlossaryResolverData };
      this.article = glossary;
      this.tagArticles = articles as unknown as ArticleCard[];
      this.tag = tag;
      this.description = (this.article.description?.[0] ?? { details: [{}] }) as WysiwygMain;

      const limitable = listMeta.limitable;
      this.rowAllCount = limitable.rowAllCount ?? 0;
      this.rowOnPageCount = limitable.rowOnPageCount ?? 0;
      this.rowFrom = limitable.rowFrom ?? 0;

      const canonical = createCanonicalUrlForPageablePage('sportlexikon', this.route.snapshot);
      if (canonical) {
        this.seo.updateCanonicalUrl(canonical);
      }
      const title = `${this.article.title} | Magyar Nemzet`;
      this.seo.setMetaData({
        ...defaultMetaInfo,
        title,
        ogTitle: title,
        description: `${this.article.title} sportlexikon oldal | ${defaultMetaInfo.description}`,
      });
      this.cd.markForCheck();
    });

    this.adStore.advertisemenets$
      .pipe(takeUntil(this.destroy$))
      .pipe(map((ads: Advertisement[]) => this.adStore.separateAdsByMedium(ads)))
      .subscribe((ads: AdvertisementsByMedium) => {
        this.ads = ads;
        this.cd.markForCheck();
      });
  }

  trackByFn(index: number, item: ArticleCard): string {
    return item?.id ?? item.slug ?? index?.toString();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSubscriptionClick(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.LAYOUT);
  }
}
