@use '../../../../scss/shared.scss' as *;

:host {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.wrapper {
  max-width: $article-max-width;
  width: calc(100% - #{$side-padding * 2});
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin: 0 auto;

  @include media-breakpoint-down(sm) {
    flex-wrap: wrap;
    max-width: 100%;
    width: 100%;
    padding: 0 $mobile-side-padding;
  }
}

.glossary {
  &-title {
    font-family: var(--kui-font-secondary);
    font-size: 36px;
    font-weight: 700;
    line-height: 44px;
    letter-spacing: 0em;
  }

  &-page-title {
    font-size: 24px;
    font-weight: 700;
    letter-spacing: normal;
    line-height: 32px;
    margin: 24px 0;
    padding: 32px 0;
    text-align: center;
    color: var(--kui-white);
    background-image: url('/assets/images/glossary-title-bg.png');
    background-repeat: no-repeat;
    background-size: cover;

    @include media-breakpoint-down(sm) {
      padding: 19px 20px;
      margin: 8px 0 16px;

      font-family: var(--kui-font-primary);
      font-size: 20px;
      font-weight: 800;
      line-height: 26px; /* 130% */
      letter-spacing: 0.3px;
    }

    .subtitle {
      font-family: var(--kui-font-secondary);
      margin-left: 16px;
      padding-left: 16px;
      border-left: 1px solid var(--kui-white);

      @include media-breakpoint-down(sm) {
        margin-left: 0;
        padding-left: 0;
        border-left: 0;
        display: block;
        width: 100%;
        font-family: var(--kui-font-primary);
        font-size: 14px;
        font-weight: 600;
        line-height: 20px; /* 142.857% */
      }
    }

    > hr {
      width: 32px;
      height: 1px;
      border: solid var(--kui-white);
      border-width: 1px 0 0 0;
      @include media-breakpoint-up(md) {
        display: none;
      }
    }
  }

  &-container {
    width: 100%;
    margin: 0 auto;

    @include media-breakpoint-down(md) {
      margin-right: 30px;
      width: calc(100% - 330px);
    }

    @include media-breakpoint-down(sm) {
      margin: 0;
      width: 100%;
    }

    app-article-text::ng-deep {
      p:first-of-type {
        font-size: 24px;
        font-weight: 700;
        line-height: 32px;
        letter-spacing: 0.01em;
        text-align: left;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--kui-slate-300);
        margin-bottom: 24px;
        @include media-breakpoint-down(sm) {
          font-size: 20px;
          font-weight: 700;
          line-height: 26px; /* 130% */
          letter-spacing: 0.3px;
        }
      }
    }
  }
}

.tags-list {
  gap: 24px;
}

.no-result {
  text-align: center;
}

app-module-heading::ng-deep {
  padding: 24px 0;
  width: 100%;
  @include media-breakpoint-down(sm) {
    h1 {
      font-size: 24px;
      font-weight: 700;
      line-height: 32px; /* 133.333% */
      letter-spacing: 0.24px;
    }
  }
}

mno-block-title-row + article + .tags-ad {
  margin-top: 24px;
}
