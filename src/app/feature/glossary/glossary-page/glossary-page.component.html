<section class="wrapper">
  <div class="glossary-container">
    <h1 class="glossary-page-title">
      Sport tudástár
      <hr />
      <span class="subtitle">A Magyar Nemzet sport fogalmak érthetően</span>
    </h1>

    <div class="glossary-header">
      <h1 class="glossary-title">
        {{ article.title }}
      </h1>
    </div>

    <app-article-text [data]="description"></app-article-text>
  </div>
</section>

<section class="wrapper flex-column">
  <app-module-heading [title]="article.title ?? ''" color="white" [articles]="tagArticles" [isMobile]="(isMobile$ | async) === true"></app-module-heading>
  <p class="no-result" *ngIf="rowAllCount < 1">Nincs kapcsolód<PERSON> cikk.</p>
  <div class="tags-ad" *ngIf="ads?.desktop?.roadblock_1 as ad">
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  </div>

  <div class="tags-ad-sm" *ngIf="ads?.mobile?.mobilrectangle_1 as ad">
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  </div>
</section>

<section class="wrapper with-aside tags-list-container">
  <div class="tags-list d-flex flex-column left-column">
    <app-page-newsletter-banner (subscribeClick)="onSubscriptionClick()"></app-page-newsletter-banner>

    <ng-container *ngFor="let article of tagArticles | slice: 1; let i = index; trackBy: trackByFn">
      <article
        mno-article-card
        [data]="article"
        [styleID]="ArticleCardType.DateImgRightTagsTitleLeadWide"
        [showThumbnail]="!!article?.thumbnailUrl || !!article?.thumbnailUrl43 || !!article?.thumbnail?.url"
      ></article>
      <ng-container *ngIf="i === 3">
        <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
      </ng-container>
    </ng-container>

    <mno-pager
      *ngIf="rowAllCount > 0"
      [rowAllCount]="rowAllCount"
      [rowOnPageCount]="rowOnPageCount"
      [isListPager]="true"
      [hasSkipButton]="true"
      [allowAutoScrollToTop]="true"
      [maxDisplayedPages]="5"
    ></mno-pager>

    <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
    <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
  </div>
  <app-sidebar></app-sidebar>
</section>
