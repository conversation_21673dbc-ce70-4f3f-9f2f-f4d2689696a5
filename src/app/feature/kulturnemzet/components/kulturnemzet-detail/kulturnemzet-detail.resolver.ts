import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { ApiListResult, ProgramDetail, ProgramRecommendationItem } from '@trendency/kesma-ui';
import { catchError, map, Observable, of, switchMap, throwError } from 'rxjs';
import { KulturnemzetService } from '../../kulturnemzet.service';

@Injectable({
  providedIn: 'root',
})
export class KulturnemzetProgramDetailResolver {
  constructor(
    private readonly kulturnemzetService: KulturnemzetService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ProgramDetail> {
    const programSlug: string = route.params['programSlug'];
    return this.kulturnemzetService
      .getProgramDetail(programSlug)
      .pipe(
        switchMap((res) =>
          this.kulturnemzetService
            .getProgramSearchData({ global_filter: res.title })
            .pipe(catchError(() => of({ data: [], meta: {} } as any)))
            .pipe(
              map((searchResult: ApiListResult<ProgramRecommendationItem>) => {
                return {
                  ...(res as ProgramDetail),
                  thumbnailUrl: searchResult?.data?.[0]?.image,
                  thumbnail: searchResult?.data?.[0]?.image,
                };
              })
            )
        )
      )
      .pipe(
        catchError((error: HttpErrorResponse) => {
          this.router.navigate(['/', '404'], {
            skipLocationChange: true,
          });
          return throwError(() => error);
        })
      );
  }
}
