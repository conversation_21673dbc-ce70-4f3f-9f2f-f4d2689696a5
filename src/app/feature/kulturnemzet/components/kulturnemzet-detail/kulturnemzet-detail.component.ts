import { ChangeDetectorRef, Component, inject, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormatDatePipe, IMetaData, SeoService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  Article,
  ArticleCard,
  BackendArticleSearchResult,
  backendDateToDate,
  BlockTitle,
  ProgramDetail,
  ProgramQueryParams,
  ProgramRecommendationItem,
  SearchDefinition,
  SearchService,
  SluggableItem,
  Tag,
  WysiwygMain,
} from '@trendency/kesma-ui';
import { forkJoin, map, Observable, of, Subject, switchMap, take, takeUntil } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { KulturnemzetService } from '../../kulturnemzet.service';
import { DOCUMENT, NgForOf, NgIf } from '@angular/common';
import {
  ArticleCardComponent,
  ArticleCardType,
  ArticlePageImageComponent,
  ArticleTextComponent,
  BlockTitleRowComponent,
  IconComponent,
  ProgramDate,
  SocialShareComponent,
  TagListComponent,
} from 'src/app/shared';
import { SidebarComponent } from 'src/app/feature/layout/components/sidebar/sidebar.component';
import { DateFnsModule } from 'ngx-date-fns';

@Component({
  selector: 'app-kulturnemzet-detail',
  templateUrl: './kulturnemzet-detail.component.html',
  styleUrls: ['./kulturnemzet-detail.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [
    AdvertisementAdoceanComponent,
    ArticleCardComponent,
    ArticlePageImageComponent,
    IconComponent,
    TagListComponent,
    SocialShareComponent,
    BlockTitleRowComponent,
    ArticleTextComponent,
    FormatDatePipe,
    ProgramDate,
    SidebarComponent,
    NgIf,
    NgForOf,
    DateFnsModule,
  ],
})
export class KulturnemzetDetailComponent implements OnInit {
  programDetailData?: ProgramDetail;
  article?: Article;
  searchSelectedType?: SluggableItem;
  searchSelectedLocation?: SluggableItem;
  searchSelectedDate?: SluggableItem;
  startDate?: Date;
  endDate?: Date;
  tags: Tag[] = [];
  body: WysiwygMain[];
  ads: AdvertisementsByMedium = {} as AdvertisementsByMedium;
  adPageType = 'program';
  url = '';
  programRecommendations: ArticleCard[];
  articleRecommendations: ArticleCard[];
  morePrograms: ArticleCard[];
  recommendedProgramTitle: BlockTitle = {
    text: 'Kiemelt program',
  };
  recommendedArticlesTitle: BlockTitle = {
    text: 'Kultúra híreink',
  };
  moreProgramTitle: BlockTitle = {
    text: 'További program lehetőségek',
  };
  readonly ArticleCardType = ArticleCardType;
  private readonly document = inject(DOCUMENT);
  private readonly destroy$ = new Subject<boolean>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly searchService: SearchService,
    private readonly router: Router,
    private readonly seo: SeoService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly analyticsService: AnalyticsService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly kulturnemzetService: KulturnemzetService
  ) {}

  get articleCard(): ArticleCard {
    return this.article as ArticleCard;
  }

  ngOnInit(): void {
    this.url = this.document.location.pathname;

    this.searchService.queryParams$.pipe(takeUntil(this.destroy$)).subscribe((data: SearchDefinition & ProgramQueryParams) => {
      this.searchSelectedDate = data?.date;
      this.searchSelectedLocation = data?.['program_location[]']?.[0];
      this.searchSelectedType = data?.['program_type[]']?.[0];
      this.getProgramRecommendations();
    });

    (this.route.data as any as Observable<{ pageData: ProgramDetail }>)
      .pipe(
        take(1),
        map(({ pageData }) => pageData)
      )
      .pipe(
        tap((data: ProgramDetail) => {
          this.url = this.document.location.pathname;
          this.programDetailData = this.addListedLocations(data);
          this.article = {
            ...(this.programDetailData as any as Article),
          } as Article;
          const dates = this.programDetailData?.dates ?? [];
          this.startDate = backendDateToDate(dates[0]?.startDate as string) as Date;
          this.endDate = backendDateToDate(dates[dates.length - 1]?.endDate as string) as Date;
          this.body = this.programDetailData?.body as any as WysiwygMain[];
          this.tags = this.programDetailData?.tags as Tag[];
          setTimeout(() => {
            this.analyticsService.sendPageView({
              pageCategory: this.programDetailData?.slug ?? 'program',
            });
          }, 0);
          this.getProgramRecommendations();
          this.setMetaData();
        }),
        switchMap(() => this.adStore.advertisemenets$),
        map((ads) => this.adStore.separateAdsByMedium(ads, this.adPageType))
      )
      .subscribe((ads: AdvertisementsByMedium) => {
        this.ads = ads;
        this.changeRef.detectChanges();
      });

    this.setMetaData();
  }

  getLink(type: string): void {
    const limitQueryParams: { rowCount_limit: number; page_limit: number } = {
      rowCount_limit: 3,
      page_limit: 0,
    };

    switch (type) {
      case 'date':
        this.router.navigate(['/', 'kulturnemzet'], {
          queryParams: {
            date: this.searchSelectedDate?.id,
            ...limitQueryParams,
          },
        });
        break;
      case 'location':
        this.router.navigate(['/', 'kulturnemzet'], {
          queryParams: {
            program_location: this.searchSelectedLocation?.id,
            ...limitQueryParams,
          },
        });
        break;
      case 'type':
        this.router.navigate(['/', 'kulturnemzet'], {
          queryParams: {
            proram_type: this.searchSelectedType?.id,
            ...limitQueryParams,
          },
        });
        break;
    }
  }

  private setMetaData(): void {
    const { title, lead } = this.programDetailData || {};
    if (!this.programDetailData) {
      return;
    }

    const metaData: IMetaData = {
      title: title,
      description: lead,
      ogTitle: title,
    };

    this.seo.setMetaData(metaData);

    const cannonicalUrl = `${this.seo.hostUrl}${this.url}`;
    if (cannonicalUrl) {
      this.seo.updateCanonicalUrl(cannonicalUrl, { addHostUrl: false });
    }
  }

  private addListedLocations(response: ProgramDetail): ProgramDetail | undefined {
    if (!response) {
      return;
    }

    let listedLocations: string[] = [];
    response?.programLocations.map((location) => {
      listedLocations = [...listedLocations, ` ${location?.title}`];
    });

    return { ...response, listedLocations: listedLocations };
  }

  private getProgramRecommendations(): void {
    const params: ProgramQueryParams = {
      page_limit: 0,
      rowCount_limit: 3,
      //...thisWeekEndDateInterval(),
      'program_type[]': (this.programDetailData?.programTypes ?? []).map(({ slug }) => slug) as any,
    };

    forkJoin([
      this.kulturnemzetService.getHighlightedProgramRecommendations('1').pipe(catchError(() => of({ data: [] as BackendArticleSearchResult[] }))),
      this.kulturnemzetService.getProgramRecommendedArticles().pipe(catchError(() => of([] as BackendArticleSearchResult[]))),
      this.kulturnemzetService.getProgramSearchData(params),
    ]).subscribe(([{ data: programRecommendations }, articleRecommendations, { data: morePrograms }]) => {
      this.programRecommendations = ((programRecommendations ?? []) as ProgramRecommendationItem[]).map(
        (program) =>
          ({
            id: program.id,
            title: program.title,
            slug: program?.slug,
            thumbnail: { url: program.image },
            labelText: program?.types?.[0]?.title,
            tags: program.tags,
            lead: program.lead,
            publishDate: program.startDate ? backendDateToDate(program.startDate as string) : '',
          }) as ArticleCard
      );
      this.articleRecommendations = (articleRecommendations ?? []).map((article) => ({
        ...article,
        publishDate: article.publishDate ? backendDateToDate(article.publishDate as string) : '',
        thumbnail: {
          url: article.thumbnail as string,
        },
      })) as any as ArticleCard[];
      this.morePrograms = ((morePrograms ?? []) as ProgramRecommendationItem[]).map(
        (program) =>
          ({
            id: program.id,
            title: program.title,
            slug: program?.slug,
            thumbnail: { url: program.image },
            labelText: program?.types?.[0]?.title,
            tags: program.tags,
            lead: program.lead,
            publishDate: program.startDate ? backendDateToDate(program.startDate as string) : '',
          }) as ArticleCard
      );
      this.changeRef.markForCheck();
    });
  }
}
