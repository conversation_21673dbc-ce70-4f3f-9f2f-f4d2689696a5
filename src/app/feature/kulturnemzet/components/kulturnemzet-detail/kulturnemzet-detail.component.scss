@use '../../../../../scss/shared.scss' as *;

:host {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: center;
}

.tags-list {
  gap: 24px;
}
.ad-zone {
  background-color: var(--kui-gray-100);
}

.program-list-top {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
  @include media-breakpoint-down(sm) {
    grid-template-columns: 1fr;
  }
}

.main-container {
  position: relative;
  display: flex;
  justify-content: space-between;
  width: $layout-max-width;
  max-width: 100%;
  margin: 40px auto;

  @include media-breakpoint-down(sm) {
    flex-wrap: wrap;
    padding: 0 15px;
  }

  .program {
    display: flex;
    flex-direction: column;
    align-content: flex-start;
    gap: 8px;

    margin-bottom: 125px;
    // margin-right: 20px;
    margin-right: auto;

    &.full {
      width: 100%;
    }

    &.with-sidebar {
      @include media-breakpoint-up(md) {
        width: 70%;
        max-width: 873px;
      }

      @include media-breakpoint-down(xs) {
        width: 100%;
        max-width: initial;
      }
    }

    mno-tag-list::ng-deep {
      margin-bottom: 0;
    }

    .title {
      font-size: 32px;
      font-weight: 700;
      line-height: 40px; /* 125% */
    }

    .lead {
      font-size: 20px;
      font-weight: 400;
      line-height: 24px; /* 120% */
    }

    &-information {
      border-top: 1px solid var(--kui-slate-300);
      padding-top: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .icon {
        margin-right: 6px;
      }

      &-dates {
        a {
          font-weight: 700;
          color: var(--kui-blue-500);
        }
      }
    }

    .article-program {
      &-container {
        display: flex;
        gap: 16px;
        align-items: center;
      }

      &-location,
      &-date {
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        line-height: 18px; /* 128.571% */
      }
    }

    @include media-breakpoint-down(xs) {
      width: 90%;
      margin: auto;
    }

    .wrapper {
      max-width: 100%;

      &.program-list {
        gap: 12px;
        display: flex;
        flex-direction: column;
        margin-top: 24px;

        [mno-article-card] {
          padding-bottom: 12px;
          border-bottom: 1px dotted var(--kui-slate-300);
        }
      }

      .dossier-ad {
        padding: 50px 0;
        margin: 40px 0;
        border-top: 1px solid var(--kui-slate-300);
        border-bottom: 1px solid var(--kui-slate-300);
      }

      .dossier-ad-sm {
        @include media-breakpoint-down(xs) {
          padding: 50px 0;
          margin: 40px 0;
          border-top: 1px solid var(--kui-slate-300);
          border-bottom: 1px solid var(--kui-slate-300);
        }
      }
    }
  }

  .sidebar {
    width: 300px;
    display: flex;
    flex-direction: column;
    gap: 24px;

    section {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    @include media-breakpoint-down(sm) {
      margin: 0;
      width: 100%;
    }

    mno-block-title-row {
      margin-bottom: 8px;
    }
  }
}
