<div class="wrapper ad-zone">
  <div *ngIf="ads?.desktop?.roadblock_1 as ad" class="dossier-ad">
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  </div>
  <div *ngIf="ads?.mobile?.mobilrectangle_1 as ad" class="dossier-ad-sm">
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  </div>
</div>
<div class="wrapper main-container">
  <section class="program with-sidebar">
    <mno-tag-list [data]="tags"></mno-tag-list>

    <h1 class="title">{{ programDetailData?.title }}</h1>
    <div class="lead">{{ programDetailData?.lead }}</div>
    <div class="program-information">
      <div class="article-program-container">
        <div class="article-program-location">
          <img [size]="24" alt="Helyszín ikon" mno-icon="location" />{{ programDetailData?.programLocations?.[0]?.title }}
        </div>
        <div class="article-program-date">
          <img [size]="24" alt="Dátum ikon" mno-icon="clock" />{{ startDate | dfnsFormat: 'MMMM d. EEEE p' }} -
          {{ endDate | dfnsFormat: 'MMMM d. EEEE p' }}
        </div>
      </div>
      <mno-social-share [link]="[url]" [title]="article?.title" color="dark"></mno-social-share>
    </div>

    <mno-article-page-image *ngIf="!!article?.thumbnail" [data]="article"></mno-article-page-image>

    <section class="program-information-body">
      <ng-container *ngFor="let detail of body">
        <app-article-text [data]="detail"></app-article-text>
      </ng-container>
    </section>

    <section class="program-information-dates">
      <ul>
        <ng-container *ngFor="let dateRange of programDetailData?.dates">
          <li>
            <ng-container *ngIf="dateRange && dateRange.endDate !== dateRange?.startDate; else isOnlyStartDate">
              <time class="program-information-date">
                {{ dateRange?.startDate ?? '' | formatDate: 'y-l-d-h-m' }} -
                {{ dateRange?.endDate | programDate: dateRange?.startDate }}
              </time>
            </ng-container>
            <ng-template #isOnlyStartDate>
              <time class="program-information-date">
                {{ dateRange?.startDate ?? '' | formatDate: 'y-l-d-h-m' }}
              </time>
            </ng-template>
            <a *ngIf="programDetailData?.externalURL" [href]="programDetailData?.externalURL" class="program-information-link" target="_blank">
              Jegyvásárlás: ITT
            </a>
          </li>
        </ng-container>
      </ul>
    </section>
    <mno-block-title-row [data]="moreProgramTitle"></mno-block-title-row>
    <!-- div class="program-list">
      <ng-container *ngFor="let program of morePrograms">
        <article mno-article-card
                 [styleID]="ArticleCardType.ImgRightTagsTitleLeadBadge"
                 [asProgram]="true"
                 [data]="program"
        ></article>
      </ng-container>
    </div -->
  </section>

  <aside class="sidebar">
    <section>
      <mno-block-title-row [data]="recommendedProgramTitle" [isSidebar]="true"></mno-block-title-row>
      <ng-container *ngFor="let programRecommendation of programRecommendations">
        <article [asProgram]="true" [data]="programRecommendation" [styleID]="ArticleCardType.ImgRightTagsTitleLead50" mno-article-card></article>
      </ng-container>
    </section>
    <div *ngIf="ads?.desktop?.box_7 as ad" class="">
      <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
    </div>
    <div *ngIf="ads?.mobile?.mobilrectangle_7 as ad" class="">
      <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
    </div>

    <section>
      <mno-block-title-row [data]="recommendedArticlesTitle" [isSidebar]="true"></mno-block-title-row>
      <ng-container *ngFor="let articleRecommendation of articleRecommendations">
        <article [asProgram]="false" [data]="articleRecommendation" [styleID]="ArticleCardType.Img16TopTagsTitleLeadBadge" mno-article-card></article>
      </ng-container>
    </section>

    <div *ngIf="ads?.desktop?.box_8 as ad" class="">
      <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
    </div>
    <div *ngIf="ads?.mobile?.mobilrectangle_8 as ad" class="">
      <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
    </div>

    <app-sidebar [adPageType]="adPageType"></app-sidebar>
  </aside>
</div>
