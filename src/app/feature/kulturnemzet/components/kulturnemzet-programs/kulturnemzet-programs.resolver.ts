import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { ApiListResult, BackendArticleSearchResult, DateInterval, ProgramQueryParams, ProgramRecommendationItem, RedirectService } from '@trendency/kesma-ui';
import { forkJoin, Observable, of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { nextWeekDateInterval, thisWeekEndDateInterval, todayDateInterval, tomorrowDatepare } from '../../../../shared';
import { KulturnemzetService } from '../../kulturnemzet.service';

export interface KulturnemzetProgramsResolverData {
  programs: ApiListResult<ProgramRecommendationItem>;
  recommendations: BackendArticleSearchResult[];
}

@Injectable()
export class KulturnemzetProgramsResolver {
  constructor(
    private readonly kulturnemzetService: KulturnemzetService,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<KulturnemzetProgramsResolverData> {
    const params: ProgramQueryParams = {
      page_limit: 0,
      rowCount_limit: 12,
    };

    if (route.queryParams?.['dateName']) {
      let dateInterval: DateInterval;

      switch (route.queryParams['dateName']) {
        case 'today':
          dateInterval = todayDateInterval();
          break;
        case 'tomorrow':
          dateInterval = tomorrowDatepare();
          break;
        case 'this_weekend':
          dateInterval = thisWeekEndDateInterval();
          break;
        case 'next_week':
          dateInterval = nextWeekDateInterval();
          break;
      }
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      if (dateInterval) {
        params.date_from = dateInterval.date_from;
        params.date_until = dateInterval.date_until;
      }
    }
    if (route.queryParams['page'] || route.queryParams['page_limit']) {
      const page = Number(route.queryParams['page'] ?? route.queryParams['page_limit']);
      params.page_limit = page ? page - 1 : 0;
    }
    if (route.queryParams['program_type[]']) {
      params['program_type[]'] = route.queryParams['program_type[]'];
    }
    if (route.queryParams['program_location[]']) {
      params['program_location[]'] = route.queryParams['program_location[]'];
    }
    return forkJoin([
      this.kulturnemzetService.getProgramSearchData(params).pipe(
        tap((recommendations) => {
          if (this.redirectService.shouldBeRedirect(route.queryParams['page'], recommendations?.data)) {
            this.redirectService.redirectOldUrl('kulturnemzet', false, 302);
          }
        }),
        catchError(() => of({ data: [], meta: {} } as any))
      ),
      this.kulturnemzetService.getProgramRecommendedArticles().pipe(catchError(() => of([] as BackendArticleSearchResult[]))),
    ]).pipe(
      map(([programs, recommendations]) => ({
        programs,
        recommendations: (recommendations ?? []).slice(0, 6),
      })),
      catchError((error: HttpErrorResponse) => {
        return throwError(() => error);
      })
    );
  }
}
