import { <PERSON>OC<PERSON>EN<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, SlicePipe } from '@angular/common';
import { Component, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementsByMedium,
  ArticleCard,
  BackendArticleSearchResult,
  backendDateToDate,
  createCanonicalUrlForPageablePage,
  LimitableMeta,
  ProgramQueryParams,
  ProgramRecommendationItem,
} from '@trendency/kesma-ui';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SidebarComponent } from 'src/app/feature/layout/components/sidebar/sidebar.component';
import { ArticleCardComponent, ArticleCardType, MostReadComponent, MostReadType, PagerComponent } from 'src/app/shared';
import { defaultMetaInfo } from '../../../../shared';
import { KulturnemzetService } from '../../kulturnemzet.service';
import { KulturnemzetProgramsResolverData } from './kulturnemzet-programs.resolver';

@Component({
  selector: 'app-kulturnemzet-programs',
  templateUrl: './kulturnemzet-programs.component.html',
  styleUrls: ['./kulturnemzet-programs.component.scss'],
  imports: [AdvertisementAdoceanComponent, SidebarComponent, PagerComponent, ArticleCardComponent, SlicePipe, MostReadComponent, NgIf, NgFor, NgForOf],
})
export class KulturnemzetProgramsComponent implements OnInit, OnDestroy {
  private readonly document = inject(DOCUMENT);

  public programRecommendationsData: ProgramRecommendationItem[];
  private readonly destroy$ = new Subject<void>();

  limit: LimitableMeta;
  isMobile = false;

  private queryParams: ProgramQueryParams = {
    page_limit: 0,
  };

  readonly ArticleCardType = ArticleCardType;
  readonly MostReadType = MostReadType;
  ads: AdvertisementsByMedium = {} as AdvertisementsByMedium;
  recommendedArticles: BackendArticleSearchResult[];

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly router: Router,
    // private readonly utilService: UtilService,
    private readonly kulturnemzetService: KulturnemzetService
    // private readonly adStore: AdvertisementAdoceanStoreService,
    // private readonly cd: ChangeDetectorRef,
  ) {}

  get articleCards(): ArticleCard[] {
    return this.programRecommendationsData as ArticleCard[];
  }

  get recommendedArticlesList(): ArticleCard[] {
    return this.recommendedArticles as any as ArticleCard[];
  }

  ngOnInit(): void {
    //    this.isMobile = !this.utilService.isBrowser()

    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe((params: ProgramQueryParams) => {
      this.queryParams = params;
    });

    this.route.data.pipe(takeUntil(this.destroy$)).subscribe({
      next: (routeData) => {
        const {
          programs: { data, meta },
          recommendations,
        } = {
          programs: { data: [], meta: {} },
          recommendations: [],
          ...routeData['pageData'],
        } as KulturnemzetProgramsResolverData;
        this.kulturnemzetService.setMeta(meta);
        if (data) {
          this.recommendedArticles = recommendations;
          this.programRecommendationsData = (data ?? []).map(
            (program) =>
              ({
                id: program.id,
                title: program.title,
                slug: program?.slug,
                thumbnail: { url: program.image },
                labelText: program?.types?.[0]?.title,
                tags: program.tags,
                lead: program.lead,
                publishDate: program.startDate ? backendDateToDate(program.startDate as string) : '',
                thumbnailFocusedImages: (program as ProgramRecommendationItem)?.imageFocusedImages,
                program,
              }) as ArticleCard
          ) as any as ProgramRecommendationItem[];
          this.limit = meta.limitable;

          this.programRecommendationsData = this.addListedLocations(this.programRecommendationsData);

          const canonical = createCanonicalUrlForPageablePage('cimke', this.route.snapshot);
          if (canonical) {
            this.seo.updateCanonicalUrl(canonical);
          }
          const title = 'Kultúrnemzet | Magyar Nemzet';
          this.seo.setMetaData({
            ...defaultMetaInfo,
            title,
            ogTitle: title,
            // eslint-disable-next-line max-len
          });
          //           this.cd.detectChanges();
          this.setCanonical();
        }
      },
    });
    //    this.adStore.advertisemenets$
    //   .pipe(takeUntil(this.destroy$))
    //   .pipe(
    //     map((ads: Advertisement[]) =>
    //        this.adStore.separateAdsByMedium(ads, 'listing', [...this.filterBannersNames]),
    //      ),
    //    )
    //   .subscribe((ads: AdvertisementsByMedium) => {
    //     this.ads = ads;
    //     this.cd.markForCheck();
    //   });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private addListedLocations(items: ProgramRecommendationItem[]): ProgramRecommendationItem[] {
    if (!items) {
      return [];
    }

    return items.map((recommendationItem: ProgramRecommendationItem) => {
      let listedLocations: string[] = [];
      (recommendationItem.locations ?? []).map((location) => {
        listedLocations = [...listedLocations, ` ${location?.title}`];
      });
      return { ...recommendationItem, listedLocations: listedLocations };
    });
  }

  trackByFn(index: number, item: ArticleCard): string {
    return item?.id ?? item.slug ?? index?.toString();
  }

  onSelectPage(page: number): void {
    const query = {
      ...this.queryParams,
      rowCount_limit: 12,
      page_limit: page - 1,
    };

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: query,
    });
  }

  setCanonical(): void {
    const url = this.document.location.pathname;
    const cannonicalUrl = `${this.seo.hostUrl}${url}`;
    if (cannonicalUrl) {
      this.seo.updateCanonicalUrl(cannonicalUrl, { addHostUrl: false });
    }
  }
}
