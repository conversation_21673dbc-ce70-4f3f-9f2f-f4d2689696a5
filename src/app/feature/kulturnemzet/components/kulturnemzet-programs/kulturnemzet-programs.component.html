<section class="wrapper with-aside">
  <div class="left-column">
    <div class="program-list-top">
      <ng-container *ngFor="let elem of articleCards | slice: 0 : 3; let i = index">
        <article
          mno-article-card
          [data]="elem"
          [asProgram]="true"
          [styleID]="isMobile ? ArticleCardType.ImgRightTagsTitleLead50 : ArticleCardType.Img1TopTagsTitleLeadBadge"
        ></article>
      </ng-container>
    </div>
  </div>
  <aside>
    <mno-most-read [data]="recommendedArticlesList" [isSidebar]="true" [styleID]="MostReadType.Sidebar" title="Legolvasottabb"></mno-most-read>
  </aside>
</section>

<section class="wrapper">
  <div class="tags-ad" *ngIf="ads?.desktop?.roadblock_1 as ad">
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  </div>

  <div class="tags-ad-sm" *ngIf="ads?.mobile?.mobilrectangle_1 as ad">
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  </div>
</section>

<div class="wrapper with-aside tags-list-container">
  <div class="program-list d-flex flex-column left-column">
    <ng-container *ngFor="let article of articleCards | slice: 3; let i = index; trackBy: trackByFn">
      <article mno-article-card [styleID]="ArticleCardType.DateImgRightTagsTitleLeadWide" [data]="article" [asProgram]="true"></article>

      <ng-container *ngIf="i === 4">
        <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
      </ng-container>
    </ng-container>

    <mno-pager
      *ngIf="(limit?.pageMax ?? 0) > 0"
      [rowAllCount]="limit?.rowAllCount ?? 0"
      [rowOnPageCount]="limit?.rowOnPageCount ?? 0"
      [isListPager]="true"
      [hasSkipButton]="true"
      [allowAutoScrollToTop]="true"
      [maxDisplayedPages]="5"
    ></mno-pager>

    <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
    <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
  </div>
  <app-sidebar></app-sidebar>
</div>
