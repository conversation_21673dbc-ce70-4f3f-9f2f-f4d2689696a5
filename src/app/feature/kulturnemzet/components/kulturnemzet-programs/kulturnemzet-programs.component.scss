@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: center;
}

.program-list {
  gap: 32px;
}

.program-list-top {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
  @include media-breakpoint-down(sm) {
    grid-template-columns: 1fr;
  }
}

.with-aside {
  aside {
    width: 300px;

    mno-most-read::ng-deep {
      margin-top: 0;
    }
  }
}

.wrapper {
  width: 100%;
  max-width: 100%;
}
