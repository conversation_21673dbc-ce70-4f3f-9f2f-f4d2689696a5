import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import {
  Api<PERSON>istResult,
  ApiResponseMetaList,
  ApiResult,
  BackendArticleSearchResult,
  ProgramDetail,
  ProgramQueryParams,
  ProgramRecommendationItem,
  ProgramTypes,
  SluggableItem,
} from '@trendency/kesma-ui';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class KulturnemzetService {
  readonly #lastSearchMeta$ = new BehaviorSubject<ApiResponseMetaList>({} as ApiResponseMetaList);
  lastSearchMeta$: Observable<ApiResponseMetaList>;

  constructor(private readonly reqService: ReqService) {
    this.lastSearchMeta$ = this.#lastSearchMeta$.asObservable();
  }

  public getProgramTypes(): Observable<ProgramTypes[]> {
    return this.reqService.get<ApiListResult<ProgramTypes>>('program/program-types').pipe(map(({ data }) => data));
  }

  public getProgramSelectTypes(): Observable<SluggableItem[]> {
    return this.reqService.get<ApiListResult<SluggableItem>>('source/program/program-recommendation/program_types').pipe(map(({ data }) => data));
  }

  public getProgramSelectLocations(): Observable<SluggableItem[]> {
    return this.reqService.get<ApiListResult<SluggableItem>>('source/program/program-recommendation/program_locations').pipe(map(({ data }) => data));
  }

  public getProgramSelectDates(): Observable<SluggableItem[]> {
    return this.reqService.get<ApiListResult<SluggableItem>>('source/program/program-recommendation/program_dates').pipe(map(({ data }) => data));
  }

  public getHighlightedProgramRecommendations(programLimit = '12'): Observable<ApiListResult<ProgramRecommendationItem>> {
    return this.reqService.get<ApiListResult<ProgramRecommendationItem>>('program/very-recommended-ones', {
      params: { rowCount_limit: programLimit, page_limit: '0' },
    });
  }

  public getProgramRecommendedArticles(): Observable<BackendArticleSearchResult[]> {
    return this.reqService.get<ApiListResult<BackendArticleSearchResult>>('content-page/articles-by-tag?tagSlug=kulturnemzet').pipe(map(({ data }) => data));
  }

  public getProgramDetail(programSlug: string): Observable<ProgramDetail> {
    return this.reqService.get<ApiResult<ProgramDetail>>(`program/program-recommendation/${programSlug}`).pipe(map(({ data }) => data));
  }

  public getProgramSearchData(params: ProgramQueryParams & Record<string, any>): Observable<ApiListResult<ProgramRecommendationItem>> {
    return this.reqService.get<ApiListResult<ProgramRecommendationItem>>('program/program-recommendation-search', {
      params,
    });
  }

  public setMeta(meta: ApiResponseMetaList): void {
    this.#lastSearchMeta$.next(meta);
  }
}
