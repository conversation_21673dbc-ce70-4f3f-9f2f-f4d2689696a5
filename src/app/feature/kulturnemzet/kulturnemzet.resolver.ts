import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { forkJoin, Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { KulturnemzetResolverResponse } from './kulturnemzet.dto.interface';
import { KulturnemzetService } from './kulturnemzet.service';

@Injectable({
  providedIn: 'root',
})
export class KulturnemzetResolver {
  constructor(private readonly kulturnemzetService: KulturnemzetService) {}

  resolve(): Observable<KulturnemzetResolverResponse> {
    return forkJoin([
      this.kulturnemzetService.getProgramTypes(),
      this.kulturnemzetService.getProgramSelectTypes(),
      this.kulturnemzetService.getProgramSelectLocations(),
    ]).pipe(
      map(([programTypes, selectTypes, selectLocations]) => ({
        programTypes,
        selectTypes,
        selectLocations,
      })),
      catchError((error: HttpErrorResponse) => {
        return throwError(() => error);
      })
    );
  }
}
