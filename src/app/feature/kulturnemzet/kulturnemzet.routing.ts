import { Routes } from '@angular/router';
import { KulturnemzetDetailComponent } from './components/kulturnemzet-detail/kulturnemzet-detail.component';
import { KulturnemzetProgramDetailResolver } from './components/kulturnemzet-detail/kulturnemzet-detail.resolver';
import { KulturnemzetProgramsComponent } from './components/kulturnemzet-programs/kulturnemzet-programs.component';
import { KulturnemzetProgramsResolver } from './components/kulturnemzet-programs/kulturnemzet-programs.resolver';
import { KulturnemzetComponent } from './kulturnemzet.component';
import { KulturnemzetResolver } from './kulturnemzet.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const KULTURNEMZET_ROUTES: Routes = [
  {
    path: '',
    component: KulturnemzetComponent,
    providers: [KulturnemzetResolver],
    resolve: { pageData: KulturnemzetResolver },
    children: [
      {
        path: '',
        runGuardsAndResolvers: 'paramsOrQueryParamsChange',
        component: KulturnemzetProgramsComponent,
        providers: [KulturnemzetProgramsResolver],
        resolve: { pageData: KulturnemzetProgramsResolver },
        data: { isSearchPage: true },
        canActivate: [PageValidatorGuard],
      },
      {
        path: ':categorySlug/:programSlug',
        component: KulturnemzetDetailComponent,
        providers: [KulturnemzetProgramDetailResolver],
        resolve: { pageData: KulturnemzetProgramDetailResolver },
        data: { isSearchPage: false },
      },
    ],
  },
];
