import { ChangeDetectorRef, Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { BackendArticleSearchResult, ProgramTypes, SluggableItem } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { filter } from 'rxjs/operators';
import { defaultMetaInfo } from '../../shared';
import { KulturnemzetResolverResponse } from './kulturnemzet.dto.interface';
import { DOCUMENT } from '@angular/common';
import { WheadKulturnemzetComponent } from 'src/app/shared/components/microsite-widgets/whead-kulturnemzet/whead-kulturnemzet.component';
import { KulturnemzetSearchbarComponent } from 'src/app/shared/components/microsite-widgets/kulturnemzet-searchbar/kulturnemzet-searchbar.component';

@Component({
  selector: 'app-kulturnemzet',
  templateUrl: './kulturnemzet.component.html',
  styleUrls: ['./kulturnemzet.component.scss'],
  imports: [RouterOutlet, WheadKulturnemzetComponent, KulturnemzetSearchbarComponent],
})
export class KulturnemzetComponent implements OnInit {
  private readonly document = inject(DOCUMENT);

  public programTypes: ProgramTypes[];
  public selects: {
    selectTypes: SluggableItem[];
    selectLocations: SluggableItem[];
    selectDates: SluggableItem[];
  };
  public recommendedArticles: BackendArticleSearchResult[];
  public selectDates: SluggableItem[] = [
    {
      title: 'Ma',
      slug: 'today',
    },
    {
      title: 'Holnap',
      slug: 'tomorrow',
    },
    {
      title: 'Ezen a hétvégén',
      slug: 'this_weekend',
    },
    {
      title: 'Jövő héten',
      slug: 'next_week',
    },
  ];
  isSearchPage = true;
  url = '';

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly seo: SeoService,
    private readonly changeRef: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.resolverValues(this.route.snapshot.data['pageData'] ?? {});
    this.isSearchPage = !!this.route.snapshot.data['isSearchPage'];
    this.url = this.document.location.pathname;
    (this.router.events.pipe(filter((event) => event instanceof NavigationEnd)) as Observable<NavigationEnd>).subscribe((navigation: NavigationEnd) => {
      const splitUrl = navigation.url.split('/');
      this.url = navigation.url;
      if (splitUrl.length < 4) {
        const urlSlug: string = splitUrl[2];
        this.resolverValues(this.route.snapshot.data['pageData'] ?? {}, urlSlug);
      }
      this.isSearchPage = !!this.route.snapshot.data['isSearchPage'];
      this.changeRef.detectChanges();
    });
    this.setMetaData();
  }

  private resolverValues(data: KulturnemzetResolverResponse, categoryParam?: string): void {
    const defaultValues = { id: '', slug: '' };
    const subpageType = (data.selectTypes || []).filter((selectItem: SluggableItem) => selectItem?.slug === categoryParam);

    this.programTypes = data.programTypes;

    this.selects = {
      selectTypes: categoryParam
        ? subpageType
        : [
            {
              ...defaultValues,
              title: 'Programtípus',
            },
            ...(data.selectTypes || []),
          ],
      selectLocations: [{ ...defaultValues, title: 'Helyszín' }, ...(data.selectLocations || [])],
      selectDates: [{ ...defaultValues, title: 'Időpont' }, ...(this.selectDates || {})],
    };
  }

  private setMetaData(): void {
    const title = 'Kultúrnemzet | Magyar Nemzet';

    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };

    this.seo.setMetaData(metaData);
  }
}
