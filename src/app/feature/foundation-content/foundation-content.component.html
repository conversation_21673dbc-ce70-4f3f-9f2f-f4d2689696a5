<div class="wrapper foundation-content-wrapper">
  <mno-block-title-row [data]="{ text: 'Tartalmaink' }" [headingLevel]="1"></mno-block-title-row>

  <div *ngIf="searchResponse$ | async as searchResponse" class="foundation-content-list">
    <ng-container *ngFor="let article of articles$ | async; let i = index; let isLast = last; trackBy: trackByFn">
      <article
        [asResult]="(isMobile$ | async) === true"
        [data]="article"
        [styleID]="(isMobile$ | async) ? ArticleCardType.ImgRightTagsTitleLeadBadge : ArticleCardType.DateImgRightTagsTitleLeadWide"
        mno-article-card
      ></article>
      <hr *ngIf="!isLast" class="list-separator" />

      <ng-container *ngIf="i === 3">
        <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_2 as ad" [ad]="ad"></kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_2 as ad" [ad]="ad"></kesma-advertisement-adocean>
      </ng-container>
    </ng-container>

    <mno-pager
      *ngIf="(searchResponse.meta.limitable?.rowAllCount ?? 0) > 0"
      [allowAutoScrollToTop]="true"
      [hasSkipButton]="true"
      [isListPager]="true"
      [maxDisplayedPages]="5"
      [rowAllCount]="searchResponse.meta.limitable?.rowAllCount ?? 0"
      [rowOnPageCount]="searchResponse.meta.limitable?.rowOnPageCount ?? 0"
    ></mno-pager>
  </div>
</div>
