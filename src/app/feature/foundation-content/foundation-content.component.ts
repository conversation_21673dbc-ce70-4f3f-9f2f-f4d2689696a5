import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ApiListResult,
  ArticleCard,
  ArticleSearchResult,
  searchResultToArticleCard,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { Observable, Subject, takeUntil } from 'rxjs';
import { map } from 'rxjs/operators';
import { ConfigService, defaultMetaInfo } from '../../shared';
import { ArticleCardComponent, ArticleCardType, BlockTitleRowComponent, PagerComponent } from 'src/app/shared';
import { AsyncPipe, NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'app-foundation-content',
  templateUrl: './foundation-content.component.html',
  styleUrls: ['./foundation-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AdvertisementAdoceanComponent, PagerComponent, ArticleCardComponent, AsyncPipe, NgIf, NgFor, BlockTitleRowComponent],
})
export class FoundationContentComponent implements OnInit, OnDestroy {
  searchResponse$: Observable<ApiListResult<ArticleSearchResult>> = this.route.data.pipe(map((res) => res['searchResponse']));
  articles$: Observable<ArticleCard[]> = this.searchResponse$.pipe(map((res) => res.data.map((sr) => searchResultToArticleCard(sr))));
  isMobile$: Observable<boolean>;
  ads: AdvertisementsByMedium = {} as AdvertisementsByMedium;
  ArticleCardType = ArticleCardType;
  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly configService: ConfigService,
    private readonly cd: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService
  ) {}

  ngOnInit(): void {
    this.isMobile$ = this.configService.isMobile$;
    this.setMetaData();

    this.adStore.advertisemenets$
      .pipe(takeUntil(this.destroy$))
      .pipe(map((ads: Advertisement[]) => this.adStore.separateAdsByMedium(ads)))
      .subscribe((ads: AdvertisementsByMedium) => {
        this.ads = ads;
        this.cd.markForCheck();
      });
  }

  trackByFn(index: number, item: ArticleCard): string {
    return item?.id ?? item.slug ?? index?.toString();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage(this.seo.currentUrl, this.route.snapshot);
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical, { addHostUrl: false });
    }
    const title = `Alapkőtartalom | Magyar Nemzet`;
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
      description: `Alapkőtartalom oldal | ${defaultMetaInfo.description}`,
    });
  }
}
