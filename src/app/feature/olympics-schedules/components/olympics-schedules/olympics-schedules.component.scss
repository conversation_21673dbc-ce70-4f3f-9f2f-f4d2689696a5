@use '../../../../../scss/shared.scss' as *;

:host {
  display: block;
  ::ng-deep {
    .content-element {
      margin-bottom: 0 !important;
    }
  }

  .wrapper {
    gap: 24px;
    @include media-breakpoint-down(md) {
      padding: 0 $mobile-side-padding;
    }
  }
  .main-content {
    margin-top: 0;
    .left-column {
      margin-top: 32px;
    }
  }

  .left-column {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .title {
      color: #020617;
      font-size: 32px;
      font-weight: 700;
      line-height: 40px;

      @include media-breakpoint-down(md) {
        font-size: 24px;
        line-height: 32px;
        letter-spacing: 0.24px;
        margin-top: -8px;
        margin-bottom: 8px;
      }
    }

    kesma-olimpia-hungarian-competitions {
      margin-bottom: 24px;

      @include media-breakpoint-down(md) {
        margin-bottom: 16px;
      }
    }

    .load-more-btn {
      width: 300px;
      height: 32px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      color: #0c7c84;
      font-family: var(--kui-font-gothic-a1);
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      border-radius: 2px;
      border: 1px solid #0c7c84;
      transition: 0.3s;

      @include media-breakpoint-down(md) {
        width: 280px;
        margin-bottom: 24px;
      }

      &:hover {
        border-color: #020617;
        background: #020617;
        color: #f8fafc;

        kesma-icon {
          color: #f8fafc;
        }
      }
    }

    kesma-olimpia-navigator {
      margin: 16px 0 24px;

      @include media-breakpoint-down(md) {
        margin: 4px 0 16px;
      }
    }
  }
}
