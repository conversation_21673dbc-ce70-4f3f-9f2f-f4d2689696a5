import { ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, inject, OnInit } from '@angular/core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  Layout,
  LayoutPageType,
  OlimpiaHungarianCompetitions,
  OlimpiaHungarianCompetitionsComponent,
  OlimpiaNavigatorComponent,
  OlimpiaPageBannerComponent,
  OlimpicPortalEnum,
  PAGE_TYPES,
} from '@trendency/kesma-ui';
import { ActivatedRoute } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { map, tap } from 'rxjs/operators';
import { formatDate } from 'date-fns';
import { Observable } from 'rxjs';
import { OlympicsHeaderComponent } from 'src/app/shared/components/olympics-header/olympics-header.component';
import { AsyncPipe, NgFor, NgIf, SlicePipe } from '@angular/common';
import { LayoutComponent } from 'src/app/feature/layout/components/layout/layout.component';

@Component({
  selector: 'app-sport-olympics-schedules',
  templateUrl: './olympics-schedules.component.html',
  styleUrl: './olympics-schedules.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    OlympicsHeaderComponent,
    OlimpiaPageBannerComponent,
    OlimpiaHungarianCompetitionsComponent,
    NgIf,
    NgFor,
    OlimpiaNavigatorComponent,
    AdvertisementAdoceanComponent,
    LayoutComponent,
    AsyncPipe,
    SlicePipe,
  ],
})
export class OlympicsSchedulesComponent implements OnInit {
  adverts?: AdvertisementsByMedium;
  adPageType = PAGE_TYPES.column_sport_all_articles_and_sub_pages;
  groupedSchedules: OlimpiaHungarianCompetitions[][];
  showAllDates = false;
  olympicImportantSidebarLayout$: Observable<Layout> = this.route.data.pipe(map(({ data }) => data['olympicImportantSidebar']));
  readonly OlimpicPortalEnum = OlimpicPortalEnum;
  protected readonly LayoutPageType = LayoutPageType;
  private readonly destroyRef = inject(DestroyRef);

  constructor(
    private readonly route: ActivatedRoute,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.data
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        tap(() => this.initAds())
      )
      .subscribe(({ data }) => {
        this.groupedSchedules = this.groupSchedulesByStartDate(data.schedules);
      });
  }

  /**
   * Groups OlimpiaHungarianCompetitions by their start date.
   *
   * @param olympicsSchedules An array of OlimpiaHungarianCompetitions to be grouped.
   * @returns An array of arrays where each inner array contains OlimpiaHungarianCompetitions with the same start date.
   */
  groupSchedulesByStartDate(olympicsSchedules: OlimpiaHungarianCompetitions[]): OlimpiaHungarianCompetitions[][] {
    const schedules = olympicsSchedules.map((schedule) => {
      return {
        ...schedule,
        startDate: new Date(schedule.startDate!),
      };
    });

    return schedules.reduce((acc: OlimpiaHungarianCompetitions[][], obj: OlimpiaHungarianCompetitions) => {
      const key = formatDate(obj.startDate!, 'yyyy-MM-dd');
      const index = acc.findIndex((arr) => formatDate(arr[0].startDate!, 'yyyy-MM-dd') === key);

      if (index === -1) {
        acc.push([obj]);
      } else {
        acc[index].push(obj);
      }

      return acc;
    }, []);
  }

  protected initAds(): void {
    this.resetAds();
    this.adStore.advertisemenets$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads, this.adPageType);

      this.cdr.detectChanges();
    });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }
}
