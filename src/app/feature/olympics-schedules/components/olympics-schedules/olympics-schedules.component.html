<section>
  <div class="wrapper">
    <app-olympics-header></app-olympics-header>
  </div>
  <div class="wrapper with-aside main-content">
    <div class="left-column">
      <kesma-olimpia-page-banner [link]="'/olimpia-2024/magyar-csapat'"> </kesma-olimpia-page-banner>

      <h2 class="title">A magyar csapat versenyszámai</h2>

      <ng-container *ngFor="let dailySchedules of groupedSchedules | slice: 0 : 4">
        <kesma-olimpia-hungarian-competitions
          [styleID]="OlimpicPortalEnum.OlimpicMNO"
          [isDetailPage]="true"
          [headerDateOverride]="dailySchedules[0].startDate!.toString()"
          [data]="dailySchedules"
        ></kesma-olimpia-hungarian-competitions>
      </ng-container>

      <ng-container *ngIf="groupedSchedules?.length > 4 && !showAllDates">
        <button class="load-more-btn" (click)="showAllDates = true">
          <span>Mu<PERSON>s többet</span>
        </button>
      </ng-container>

      <ng-container *ngIf="showAllDates">
        <ng-container *ngFor="let dailySchedules of groupedSchedules | slice: 4">
          <kesma-olimpia-hungarian-competitions
            [styleID]="OlimpicPortalEnum.OlimpicMANDINER"
            [isDetailPage]="true"
            [headerDateOverride]="dailySchedules[0].startDate!.toString()"
            [data]="dailySchedules"
          ></kesma-olimpia-hungarian-competitions>
        </ng-container>
      </ng-container>

      <kesma-olimpia-navigator [styleID]="OlimpicPortalEnum.OlimpicMNO"></kesma-olimpia-navigator>

      <div class="ads">
        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.roadblock_3 as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg)',
            padding: 'var(--ad-padding)',
          }"
        >
        </kesma-advertisement-adocean>
      </div>
    </div>

    <aside>
      <app-layout
        *ngIf="olympicImportantSidebarLayout$ | async as layout"
        [adPageType]="adPageType"
        [configuration]="layout.content"
        [layoutType]="LayoutPageType.SIDEBAR"
        [structure]="layout.struct"
        class="olympic-important-sidebar"
        secondary_pageType="column_sport_all_articles_and_sub_pages"
      ></app-layout>
    </aside>
  </div>

  <div class="ads">
    <kesma-advertisement-adocean
      *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
      [style]="{
        margin: 'var(--ad-margin)',
        background: 'var(--ad-bg)',
        padding: 'var(--ad-padding)',
      }"
      [ad]="ad"
    >
    </kesma-advertisement-adocean>
  </div>
</section>
