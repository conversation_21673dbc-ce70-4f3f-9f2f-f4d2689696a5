import { ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { catchError, map } from 'rxjs/operators';
import { forkJoin, take, throwError } from 'rxjs';
import { OlympicsSchedulesService } from '../services/olympics-schedules.service';
import { Layout, OlimpiaHungarianCompetitions } from '@trendency/kesma-ui';
import { OlympicImportantSidebarService } from '../../../shared';

export const olympicsSchedulesResolver: ResolveFn<{
  schedules: OlimpiaHungarianCompetitions[];
  olympicImportantSidebar: Layout | null;
}> = () => {
  const router = inject(Router);
  const olympicsSchedulesService = inject(OlympicsSchedulesService);

  return forkJoin({
    schedules: olympicsSchedulesService.getOlympicsSchedules().pipe(
      map(({ data }) => data),

      catchError((err) => {
        router.navigate(['/404'], { skipLocationChange: true }).then();
        return throwError(() => err);
      })
    ),
    olympicImportantSidebar: inject(OlympicImportantSidebarService).getLayout().pipe(take(1)),
  });
};
