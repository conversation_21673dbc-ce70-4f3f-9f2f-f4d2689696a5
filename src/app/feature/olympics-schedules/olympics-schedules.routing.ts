import { Routes } from '@angular/router';
import { olympicsSchedulesResolver } from './resolvers/olympics-schedules.resolver';
import { PagerValidatorGuard } from '../../shared';
import { OlympicsSchedulesComponent } from './components/olympics-schedules/olympics-schedules.component';

export const OLYMPICS_SCHEDULES_ROUTES: Routes = [
  {
    path: '',
    component: OlympicsSchedulesComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    resolve: { data: olympicsSchedulesResolver },
    canActivate: [PagerValidatorGuard],
  },
];
