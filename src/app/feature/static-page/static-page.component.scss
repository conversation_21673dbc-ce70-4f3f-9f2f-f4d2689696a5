@use 'shared' as *;

.eb-wrapper {
  max-width: $layout-max-width;
  width: 100%;
  margin-inline: auto;
  @include media-breakpoint-down(sm) {
    width: calc(100% - #{$mobile-side-padding * 2});
  }

  .eb-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    height: 160px;
    background-image: url('/assets/images/eb/eb-static-bg-desktop.jpg');
    background-position: top center;
    background-size: cover;
    background-repeat: no-repeat;
    overflow: hidden;
    padding: 5px;

    &-overlay {
      position: absolute;
      width: 1272px;
      min-width: 1272px;
      height: 81px;
      bottom: 0;
      right: 0;
    }

    &-logo {
      width: 514px;
      height: 56px;
    }

    @include media-breakpoint-down(sm) {
      background-image: url('/assets/images/eb/eb-static-bg-mobile.jpg');
      background-position: center center;
      height: 140px;
      background-size: 177%;
      &-overlay {
        display: none;
      }
      &-logo {
        width: 215px;
        height: 23px;
      }
    }
  }

  ::ng-deep {
    mno-eb-podcast-block {
      @include media-breakpoint-up(md) {
        margin-top: -30px;
      }
    }
  }
}

.wrapper.olympics-2024 {
  max-width: $layout-max-width;

  @include media-breakpoint-down(sm) {
    padding: 0 $mobile-side-padding;
  }

  ::ng-deep {
    @include media-breakpoint-up(md) {
      kesma-olimpia-podcast-list {
        margin-top: -30px;
      }
    }

    app-olimpia-results-block,
    kesma-olimpia-article-list {
      margin-top: -20px;
      display: block;
    }
  }
}

.static-page {
  margin-top: 25px;
  padding-bottom: 45px;

  @include media-breakpoint-down(md) {
    margin: 25px 16px 0px;
  }

  &-title {
    font-size: 60px;
    font-weight: 700;
    line-height: 74px;
    font-family: var(--kui-font-secondary);
    margin-bottom: 40px;
    word-wrap: break-word;

    @include media-breakpoint-down(xs) {
      font-size: 30px;
      font-weight: 700;
      line-height: 40px;
    }
  }
}
