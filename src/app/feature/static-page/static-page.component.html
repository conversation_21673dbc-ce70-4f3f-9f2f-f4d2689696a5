<section class="block static-page" *ngIf="!customStaticPageType || customStaticPageType === CustomStaticPageType.StaticPage">
  <div class="with-aside wrapper">
    <div class="static-page-container left-column">
      <div class="heading-line">
        <h1 class="static-page-title">{{ title }}</h1>
      </div>
      <ng-container *ngFor="let element of body">
        <ng-container [ngSwitch]="element.type">
          <div *ngIf="element">
            <app-article-text *ngSwitchCase="'Basic.Wysiwyg.Wysiwyg'" [data]="element"></app-article-text>
          </div>
        </ng-container>
      </ng-container>
    </div>
    <aside>
      <app-sidebar [adPageType]="adPageType"></app-sidebar>
    </aside>
  </div>
</section>

<section *ngIf="customStaticPageType === CustomStaticPageType.CustomPage">
  <div class="wrapper" [class.eb-wrapper]="isEb2024" [class.olympics-2024]="isOlympics2024">
    <div class="static-page-container-no-side">
      <ng-container *ngIf="isEb2024">
        <div class="eb-header">
          <h1><img src="/assets/images/eb/logo_EB_2024.svg" alt="Labdarúgó EB 2024" class="eb-header-logo" /></h1>
          <img src="/assets/images/eb/eb-static-bg-desktop-overlay.svg" alt="" class="eb-header-overlay" />
        </div>
      </ng-container>
      <app-olympics-header *ngIf="isOlympics2024"></app-olympics-header>

      <ng-container *ngIf="slug === 'cpac-hungary-2024'">
        <app-cpac [hasMargin]="true"></app-cpac>
      </ng-container>
      <app-layout [adPageType]="adPageType" [structure]="layoutApiData.struct" [configuration]="layoutApiData.content"> </app-layout>
    </div>
  </div>
</section>
