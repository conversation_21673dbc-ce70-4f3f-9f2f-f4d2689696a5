import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { Layout, RedirectService } from '@trendency/kesma-ui';
import { forkJoin, Observable, take } from 'rxjs';
import { OlympicsHungarianTeamService } from '../services/olympics-hungarian-team.service';
import { OlympicImportantSidebarService } from 'src/app/shared/services/olympic-important-sidebar.service';
import { tap } from 'rxjs/operators';

// eslint-disable-next-line max-len
export const olympicsParticipantsResolver: ResolveFn<
  Observable<{
    olympicsParticipants: any;
    olympicImportantSidebar: Layout | null;
  }>
> = (route) => {
  const YEAR = 2024;
  const MAXRESULTSPERPAGE = 20;
  const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;
  const COUNTRYCODE = 'HUN';

  const redirectService = inject(RedirectService);

  return forkJoin({
    olympicsParticipants: inject(OlympicsHungarianTeamService)
      .getOlympicParticipants(YEAR, currentPage, MAXRESULTSPERPAGE, COUNTRYCODE)
      .pipe(
        tap(({ data }) => {
          if (redirectService.shouldBeRedirect(currentPage, data)) {
            redirectService.redirectOldUrl('olimpia-2024/magyar-csapat', false, 302);
          }
        })
      ),
    olympicImportantSidebar: inject(OlympicImportantSidebarService).getLayout().pipe(take(1)),
  });
};
