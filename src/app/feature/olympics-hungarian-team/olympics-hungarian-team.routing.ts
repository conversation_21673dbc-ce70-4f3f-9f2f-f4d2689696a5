import { Routes } from '@angular/router';
import { OlympicsHungarianTeamComponent } from './components/olympics-hungarian-team/olympics-hungarian-team.component';
import { olympicsParticipantsResolver } from './resolvers/olympics-hungarian-team.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const OLYMPICS_HUNGARIAN_TEAM_ROUTES: Routes = [
  {
    path: '',
    component: OlympicsHungarianTeamComponent,
    resolve: { data: olympicsParticipantsResolver },
    canActivate: [PageValidatorGuard],
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
  },
];
