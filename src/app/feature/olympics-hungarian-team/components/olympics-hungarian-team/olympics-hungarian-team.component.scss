@use 'shared' as *;

:host {
  ::ng-deep {
    .content-element {
      margin-bottom: 0 !important;
    }
  }
}
.wrapper {
  @include media-breakpoint-down(md) {
    padding: 0 $mobile-side-padding;
  }
}

.main-content {
  margin-top: 0;
  .left-column {
    margin-top: 32px;
  }
}

.left-column {
  row-gap: 24px;

  .table-title {
    line-height: 40px;
    margin: 24px 0;
  }

  .participants {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    margin-bottom: 40px;
    row-gap: 16px;

    @include media-breakpoint-down(lg) {
      grid-template-columns: repeat(1, 1fr);
    }
  }

  kesma-olimpia-page-banner {
    width: 100%;
  }
}

mno-pager {
  margin-bottom: 24px;
}

kesma-olimpia-navigator {
  margin-top: 16px;
  margin-bottom: 24px;
}

kesma-advertisement-adocean {
  margin-top: 24px;
  margin-bottom: 40px;
}
