<section>
  <div class="wrapper">
    <app-olympics-header></app-olympics-header>
  </div>
  <div class="wrapper with-aside main-content">
    <div class="left-column" *ngIf="olympicsParticipants$ | async as data">
      <kesma-olimpia-page-banner></kesma-olimpia-page-banner>
      <h1 class="table-title">Magyar olimpiai csapat</h1>
      <ng-container *ngTemplateOutlet="pager"></ng-container>
      <div class="participants">
        @for (participant of data.data; track participant.slug) {
          <kesma-olimpia-participant [styleID]="OlimpicPortalEnum.OlimpicMNO" [data]="participant"></kesma-olimpia-participant>
        }
      </div>
      <ng-container *ngTemplateOutlet="pager"></ng-container>
      <kesma-olimpia-navigator [navigationLink]="['/', 'olimpia-2024']" [styleID]="OlimpicPortalEnum.OlimpicMNO"> </kesma-olimpia-navigator>

      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_1 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>
    </div>

    <aside>
      <app-layout
        *ngIf="olympicImportantSidebarLayout$ | async as layout"
        [adPageType]="adPageType"
        [configuration]="layout.content"
        [layoutType]="LayoutPageType.SIDEBAR"
        [structure]="layout.struct"
        class="olympic-important-sidebar"
        secondary_pageType="column_sport_all_articles_and_sub_pages"
      ></app-layout>
    </aside>
  </div>
</section>

<ng-template #pager>
  <ng-container *ngIf="limitable$ | async as limitable">
    <mno-pager
      *ngIf="limitable?.pageMax! > 0"
      [rowAllCount]="limitable?.rowAllCount!"
      [rowOnPageCount]="limitable?.rowOnPageCount!"
      [isListPager]="true"
      [hasFirstLastButton]="false"
      [hasSkipButton]="true"
      [allowAutoScrollToTop]="true"
      [maxDisplayedPages]="3"
    >
    </mno-pager>
  </ng-container>
</ng-template>
