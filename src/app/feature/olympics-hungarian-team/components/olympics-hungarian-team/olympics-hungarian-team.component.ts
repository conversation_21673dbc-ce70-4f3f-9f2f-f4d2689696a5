import { AsyncPipe, NgIf, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ALL_BANNER_LIST,
  Layout,
  LayoutPageType,
  OlimpiaNavigatorComponent,
  OlimpiaPageBannerComponent,
  OlimpiaParticipantComponent,
  OlimpicPortalEnum,
  PAGE_TYPES,
  SecondaryFilterAdvertType,
} from '@trendency/kesma-ui';
import { map, Observable, Subject, takeUntil } from 'rxjs';
import { LayoutComponent } from 'src/app/feature/layout/components/layout/layout.component';
import { PagerComponent } from 'src/app/shared';
import { OlympicsHeaderComponent } from 'src/app/shared/components/olympics-header/olympics-header.component';

@Component({
  selector: 'app-olympics-hungarian-team',
  templateUrl: './olympics-hungarian-team.component.html',
  styleUrl: './olympics-hungarian-team.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AdvertisementAdoceanComponent,
    PagerComponent,
    LayoutComponent,
    OlimpiaParticipantComponent,
    OlimpiaNavigatorComponent,
    AsyncPipe,
    NgIf,
    NgTemplateOutlet,
    OlimpiaPageBannerComponent,
    OlympicsHeaderComponent,
  ],
})
export class OlympicsHungarianTeamComponent implements OnInit, OnDestroy {
  adPageType = PAGE_TYPES.column_sport_all_articles_and_sub_pages;
  adverts?: AdvertisementsByMedium;
  readonly OlimpicPortalEnum = OlimpicPortalEnum;
  readonly LayoutPageType = LayoutPageType;
  readonly unsubscribe$: Subject<boolean> = new Subject();
  olympicsParticipants$: Observable<Layout> = this.route.data.pipe(map(({ data }) => data['olympicsParticipants']));
  olympicImportantSidebarLayout$: Observable<Layout> = this.route.data.pipe(map(({ data }) => data['olympicImportantSidebar']));
  limitable$: Observable<Layout> = this.route.data.pipe(map(({ data }) => data['olympicsParticipants']['meta']['limitable']));

  constructor(
    private readonly route: ActivatedRoute,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    protected readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initAds();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
    this.adStoreAdo.setArticleParentCategory('');
  }

  initAds(): void {
    this.resetAds();
    this.adStoreAdo.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStoreAdo.separateAdsByMedium(
        ads,
        this.adPageType,
        ALL_BANNER_LIST,
        SecondaryFilterAdvertType.REPLACEABLE,
        PAGE_TYPES.column_sport_all_articles_and_sub_pages
      );

      this.cdr.detectChanges();
    });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }
}
