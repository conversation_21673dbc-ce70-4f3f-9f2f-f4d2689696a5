import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  ApiListResult,
  ArticleCard,
  backendDateToDate,
  GalleriesResponse,
  NEWSLETTER_COMPONENT_TYPE,
  Tag,
  toBool,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { Observable, Subject } from 'rxjs';
import { filter, map, takeUntil } from 'rxjs/operators';
import { ConfigService, defaultMetaInfo } from '../../shared';
import { PageType } from '../video-podcast-list-page/video-podcast.definitions';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { ArticleCardComponent, ArticleCardType, PagerComponent } from 'src/app/shared';
import { AsyncPipe, NgFor, NgIf, SlicePipe } from '@angular/common';
import { ModuleHeadingComponent } from 'src/app/shared/components/module-heading/module-heading.component';
import { PageNewsletterBannerComponent } from 'src/app/shared/components/page-newsletter-banner/page-newsletter-banner.component';

@Component({
  selector: 'app-galleries',
  templateUrl: './galleries.component.html',
  styleUrls: ['./galleries.component.scss'],
  imports: [
    AdvertisementAdoceanComponent,
    SidebarComponent,
    PagerComponent,
    ArticleCardComponent,
    AsyncPipe,
    NgIf,
    NgFor,
    SlicePipe,
    ModuleHeadingComponent,
    PageNewsletterBannerComponent,
  ],
})
export class GalleriesComponent implements OnInit, OnDestroy {
  articles: ArticleCard[];
  slug: string;
  rowAllCount: number;
  rowOnPageCount: number;
  rowFrom: number;
  titleRow = {
    text: '',
  };
  tag: Tag;
  private readonly destroy$ = new Subject<void>();

  readonly ArticleCardType = ArticleCardType;
  ads: AdvertisementsByMedium = {} as AdvertisementsByMedium;
  pageNumber = 1;
  isMobile$: Observable<boolean>;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly cd: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly analyticsService: AnalyticsService,
    private readonly configService: ConfigService
  ) {}

  ngOnInit(): void {
    this.isMobile$ = this.configService.isMobile$;
    this.route.data.pipe(takeUntil(this.destroy$)).subscribe((res: any) => {
      if (!res?.galleries) {
        return;
      }
      const { data: galleries, meta } = res.galleries as ApiListResult<GalleriesResponse>;
      this.articles = (galleries ?? []).map(
        ({ slug, title, highlightedImageUrl, count, publicDate, pre_title, photographer, isAdult }) =>
          ({
            slug,
            publishDate: backendDateToDate(publicDate),
            thumbnail: {
              url: highlightedImageUrl,
            },
            length: count,
            articleSource: photographer,
            title,
            preTitle: pre_title,
            isAdultsOnly: toBool(isAdult),
          }) as ArticleCard
      );
      this.slug = this.route?.snapshot?.params?.['slug'];
      this.titleRow.text = 'Galériák';
      const limitable = meta?.limitable;
      this.rowAllCount = limitable?.rowAllCount ?? 0;
      this.rowOnPageCount = limitable?.rowOnPageCount ?? 0;
      this.rowFrom = limitable?.rowFrom ?? 0;

      const canonical = createCanonicalUrlForPageablePage('galeriak', this.route.snapshot);
      if (canonical) {
        this.seo.updateCanonicalUrl(canonical);
      }
      const title = 'Galériák | Magyar Nemzet';
      this.seo.setMetaData({
        ...defaultMetaInfo,
        title,
        ogTitle: title,
      });
      this.cd.detectChanges();
    });

    this.adStore.advertisemenets$
      .pipe(takeUntil(this.destroy$))
      .pipe(map((ads: Advertisement[]) => this.adStore.separateAdsByMedium(ads)))
      .subscribe((ads: AdvertisementsByMedium) => {
        this.ads = ads;
        this.cd.markForCheck();
      });
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .pipe(
        filter((params) => 'page' in params),
        map((params) => params['page'])
      )
      .subscribe((page: string) => {
        this.pageNumber = parseInt(page, 10);
        this.cd.markForCheck();
      });
  }

  trackByFn(index: number, item: ArticleCard): string {
    return item?.id ?? item.slug ?? index?.toString();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSubscriptionClick(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.LAYOUT);
  }

  protected readonly PageType = PageType;
}
