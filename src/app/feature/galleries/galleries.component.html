<section class="wrapper">
  <p *ngIf="rowAllCount === 0; else firstResult" class="galleries-notfound text-center">nincs tal<PERSON>lat</p>

  <ng-template #firstResult>
    <app-module-heading
      [articles]="articles"
      [title]="titleRow.text"
      [showTitle]="pageNumber === 1"
      [color]="'gray'"
      [isGallery]="true"
      [styleID]="ArticleCardType.Gallery"
      [isMobile]="(isMobile$ | async) === true"
    >
    </app-module-heading>
  </ng-template>

  <div class="galleries-ad" *ngIf="ads?.desktop?.roadblock_1 as ad">
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  </div>

  <div class="galleries-ad-sm" *ngIf="ads?.mobile?.mobilrectangle_1 as ad">
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  </div>
</section>

<div class="wrapper with-aside galleries-list-container">
  <div class="galleries-list d-flex flex-column left-column">
    <app-page-newsletter-banner (subscribeClick)="onSubscriptionClick()"></app-page-newsletter-banner>
    <div class="d-flex galleries-list-inner">
      <ng-container *ngFor="let article of articles | slice: 3; let i = index; trackBy: trackByFn">
        <article
          mno-article-card
          [data]="article"
          [styleID]="ArticleCardType.Gallery"
          [showThumbnail]="!!article?.thumbnailUrl || !!article?.thumbnailUrl43 || !!article?.thumbnail?.url"
          [asGallery]="true"
        ></article>
        <ng-container *ngIf="i === 3">
          <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
          <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
        </ng-container>
        <ng-container *ngIf="i === 9">
          <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
          <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
        </ng-container>
      </ng-container>
    </div>
    <mno-pager
      *ngIf="rowAllCount > 0"
      [rowAllCount]="rowAllCount"
      [rowOnPageCount]="rowOnPageCount"
      [isListPager]="true"
      [hasSkipButton]="true"
      [allowAutoScrollToTop]="true"
      [maxDisplayedPages]="5"
    ></mno-pager>

    <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_4 as ad" [ad]="ad"> </kesma-advertisement-adocean>
    <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_4 as ad" [ad]="ad"> </kesma-advertisement-adocean>
  </div>
  <app-sidebar></app-sidebar>
</div>
