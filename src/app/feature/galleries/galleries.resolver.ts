import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ApiService } from 'src/app/shared/services/api.service';
import { ApiListResult, GalleriesResponse, RedirectService } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class GalleriesResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ApiListResult<GalleriesResponse>> {
    const pageIndex = route.queryParams['page'] ? route.queryParams['page'] - 1 : 0;
    return this.apiService.getGalleries(pageIndex, 17).pipe(
      tap(({ data }) => {
        if (this.redirectService.shouldBeRedirect(pageIndex, data)) {
          this.redirectService.redirectOldUrl(`galeriak`, false, 302);
        }
      }),
      catchError((error: HttpErrorResponse) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });
        return throwError(error);
      })
    );
  }
}
