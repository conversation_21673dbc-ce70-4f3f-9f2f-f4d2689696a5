@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: center;
}

mno-block-title-row::ng-deep {
  .block-title {
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;
    letter-spacing: 0.015em;
    text-align: left;
    margin: 24px 0;
  }
}

.galleries-list {
  gap: 24px;

  &-inner {
    gap: 24px;
    flex-wrap: wrap;
  }

  [mno-article-card]::ng-deep {
    .article-title:not(:hover) {
      color: var(--kui-slate-950);
    }

    .article-label:not(:hover) {
      color: var(--kui-blue-900);
    }
  }

  @include media-breakpoint-up(md) {
    [mno-article-card] {
      width: calc(50% - 12px);
    }
  }

  kesma-advertisement-adocean {
    width: 100%;
  }
}

mno-block-title-row::ng-deep {
  .block-title {
    color: var(--kui-slate-950);

    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 26px;
    letter-spacing: 0.3px;
  }

  margin-bottom: 25px;
}

mno-block-title-row + article + .galleries-ad {
  margin-top: 24px;
}
