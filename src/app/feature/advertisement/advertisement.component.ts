import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Observable } from 'rxjs';
import { Advertisement, AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService } from '@trendency/kesma-ui';
import { Async<PERSON>ipe, NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'app-advertisement-page',
  templateUrl: './advertisement.component.html',
  styleUrls: ['./advertisement.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [AdvertisementAdoceanComponent, NgIf, NgFor, AsyncPipe],
})
export class AdvertisementComponent implements OnInit {
  public advertisementList$: Observable<Advertisement[]>;

  constructor(private readonly adStoreAdo: AdvertisementAdoceanStoreService) {}

  ngOnInit(): void {
    this.advertisementList$ = this.adStoreAdo.advertisemenets$;
  }
}
