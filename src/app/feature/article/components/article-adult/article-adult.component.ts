import { ChangeDetectionStrategy, Component, EventEmitter, Output } from '@angular/core';
import { Router } from '@angular/router';
import { StorageService } from '@trendency/kesma-core';
import { Location } from '@angular/common';
import { IconComponent } from 'src/app/shared';

export type HistoryState = Readonly<{
  navigationId: number;
}>;

@Component({
  selector: 'app-adult',
  templateUrl: './article-adult.component.html',
  styleUrls: ['./article-adult.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent],
})
export class ArticleAdultComponent {
  @Output() isUserAdult = new EventEmitter<boolean>();

  constructor(
    private readonly storageService: StorageService,
    private readonly location: Location,
    private readonly router: Router
  ) {}

  public handleUserChoice(isAdult: boolean): void {
    this.isUserAdult.emit(isAdult);
    this.storageService.setSessionStorageData('isAdultChoice', isAdult);

    if (isAdult) {
      return;
    }

    // If this is higher than 1 we can go back, because we have location history.
    const hasLocation = +((this.location.getState() as HistoryState).navigationId || 0);

    if (hasLocation > 1) {
      this.location.back();
    } else {
      this.router.navigate(['/']).then();
    }
  }
}
