@use 'shared' as *;

:host {
  width: 100%;
  display: block;
  background: var(--kui-black);
  text-align: center;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;

  &,
  & p {
    color: var(--kui-slate-50);
  }
}

.adult {
  max-width: 720px;
  margin: 0 auto;
  padding: 16px 0 80px;
  gap: 24px;
  font-size: 16px;
  line-height: 21px;

  @include media-breakpoint-down(md) {
    gap: 12px;
    padding-left: 24px;
    padding-right: 24px;
  }

  &-mno-logo {
    width: 201px;
    margin-bottom: 80px;
    @include media-breakpoint-down(md) {
      width: 165px;
      margin-bottom: 8px;
    }
  }

  &-warning-circle {
    width: 100px;
    height: 100px;
    border: 8px solid var(--kui-red-500);
    font-weight: 900;
    border-radius: 50%;
    font-size: 50px;
    padding-top: 8px;

    @include media-breakpoint-down(md) {
      width: 70px;
      height: 70px;
      border: 6px solid var(--kui-red-500);
      font-size: 36px;
      padding-top: 4px;
    }
  }

  &-lead {
    font-weight: 700;
  }

  &-text {
    @include media-breakpoint-down(md) {
      margin-bottom: 32px;
    }
  }

  &-button-container {
    gap: 12px;
  }

  .btn {
    @include media-breakpoint-up(lg) {
      font-size: 16px;
      line-height: 22px;
    }
  }
}

a:link,
a:visited,
a:active {
  color: var(--kui-blue-500);
}
