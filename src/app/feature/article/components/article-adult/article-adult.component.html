<section class="adult d-flex flex-column align-items-center">
  <img alt="Magyar <PERSON>" class="adult-mno-logo" src="/assets/images/logo-mno-slightblue.svg" />
  <div class="adult-warning-circle d-flex align-items-center justify-content-center">18</div>
  <h1 class="adult-title d-flex">Figyelem!</h1>

  <p class="adult-lead">
    Ez a tartalom olyan elemeket tartalmazhat, amely<PERSON> a hatályos jogszabályok kategóriái szerint kiskorúakra károsak lehetnek. Ha azt szeretné, hogy az ilyen
    tartalmakhoz erről a számítógépről kiskorú ne férhessen hozzá, használjon szűrőprogramot! A javasolt szűrőprogram elérhető
    <a class="adult-program-link" href="https://mte.hu/gyermekbarat-internet/" target="_blank">ide kattintva.</a>
  </p>

  <p class="adult-text">
    Ha ön elmúlt 18 éves, kattintson az "Elmúltam 18 éves" gombra és a tartalom az ön számára elérhető lesz. Ha ön nem múlt el 18 éves, kattintson a "Nem múltam
    el 18 éves" gombra; ez a tartalom az ön számára nem lesz elérhető.
  </p>

  <div class="adult-button-container d-flex flex-column">
    <a (click)="handleUserChoice(true)" class="btn btn-ghost">
      Elmúltam 18 éves
      <i color="white" mno-icon="chevron-right"></i>
    </a>
    <a (click)="handleUserChoice(false)" class="btn btn-ghost btn-ghost-white-transparent">
      Nem múltam el 18 éves
      <i color="white" mno-icon="chevron-right"></i>
    </a>
  </div>
</section>
