import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { ArticleBodyDetailType, ArticleBodyInfoBoxDetails, ArticleBodyInfoBoxElementData } from '@trendency/kesma-ui';
import { IconComponent } from 'src/app/shared';

@Component({
  selector: 'app-info-block',
  templateUrl: './info-block.component.html',
  styleUrls: ['./info-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent],
})
export class InfoBlockComponent implements OnInit {
  @Input() data: ArticleBodyInfoBoxElementData;
  title: string;
  content: string;
  type: string;

  ngOnInit(): void {
    this.data.details.forEach((detail: ArticleBodyInfoBoxDetails) => {
      switch (detail.type) {
        case ArticleBodyDetailType.InfoBoxTitle:
          this.title = detail.value as string;
          break;
        case ArticleBodyDetailType.InfoBoxDescription:
          this.content = detail.value as string;
          break;
        case ArticleBodyDetailType.InfoBoxType:
          this.type = (detail.value as { value: string })?.value;
          break;
      }
    });
  }
}
