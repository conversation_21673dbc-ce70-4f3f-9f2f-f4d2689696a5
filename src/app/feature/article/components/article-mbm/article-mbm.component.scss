@use 'shared' as *;

.article-mbm {
  padding: 27px 0 15px;
  border-top: 1px solid rgba(var(--kui-slate-500), 0.2);

  .article-mbm-top {
    display: flex;
    align-items: center;
    margin-bottom: 13px;

    @include media-breakpoint-down(sm) {
      display: block;
    }
  }

  .article-mbm-time {
    font-weight: 500;
    font-size: 18px;
    color: var(--kui-white);
    height: 27px;
    width: 53px;
    background-color: var(--kui-blue-400);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1px 4px 0;

    @include media-breakpoint-down(sm) {
      float: left;
      margin: 5px 10px 0 0;
    }
  }

  .article-mbm-title {
    font-size: 20px;
    line-height: 36px;
    font-family: var(--kui-font-secondary);
    color: var(--kui-slate-950);
    font-weight: 400;
    margin-left: 10px;

    @include media-breakpoint-down(sm) {
      margin-left: 0;
    }
  }

  img {
    @include media-breakpoint-down(sm) {
      margin-left: -15px;
      margin-right: -15px;
      width: calc(100% + 30px);
      max-width: none;
    }
  }

  figcaption {
    margin-top: 10px;
    color: var(--kui-slate-500);
    text-align: right;
  }

  p {
    font-weight: 300;
    line-height: 24px;
    margin-bottom: 30px;
  }

  ul,
  ol {
    padding-left: 17px;
  }

  ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }
}
