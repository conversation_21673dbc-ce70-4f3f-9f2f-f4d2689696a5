import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, NgS<PERSON>Default, NgTemplateOutlet } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, HostBinding, Input, OnChanges, SimpleChanges } from '@angular/core';
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser';
import { RouterLink } from '@angular/router';
import { Advertisement, AdvertisementAdoceanComponent, Article, ArticleCard, ArticleLanguage, buildArticleUrl, Sponsorship, Tag } from '@trendency/kesma-ui';
import { DateFnsModule } from 'ngx-date-fns';
import { ArticlePageImageComponent, SocialShareComponent, TagListComponent } from 'src/app/shared';

@Component({
  selector: 'app-article-header',
  templateUrl: './article-header.component.html',
  styleUrls: ['./article-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    RouterLink,
    NgI<PERSON>,
    <PERSON><PERSON><PERSON>,
    ArticlePageImageComponent,
    TagListComponent,
    NgTemplateOutlet,
    SocialShareComponent,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    DateFnsModule,
    AdvertisementAdoceanComponent,
  ],
})
export class ArticleHeaderComponent implements OnChanges, AfterViewInit {
  @Input() article: Article;
  @Input() languages: ArticleLanguage[] = [];
  @Input() url = '';
  @Input() displayCover = true;
  @Input() sponsorship?: Sponsorship;
  @Input() isMobile: boolean | null = false;
  @Input() foundationTagSlug?: string;
  @Input() foundationTagTitle?: string;
  @Input() isExceptionAdvertEnabled?: boolean;
  @Input() medium_rectangle_1_top?: Advertisement;
  @Input() roadblock_1_top?: Advertisement;
  @Input() isVideo?: boolean;
  embedPrAdvert?: SafeHtml;

  @HostBinding('class.news-reception') isSponsored = false;

  photoCaption = '';
  articleLinks: (string | string[])[];
  tags: Tag[] = [];

  constructor(private readonly sanitizer: DomSanitizer) {}

  get articleCard(): ArticleCard {
    return this.article as ArticleCard;
  }

  ngAfterViewInit(): void {
    this.updateProps();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['languages']) {
      this.buildLinks();
    }
    if (changes['sponsorship']) {
      this.setSponsorship();
    }
    if (changes['article']) {
      this.updateCaption();
      this.updateTags();

      this.embedPrAdvert = this.sanitizer.bypassSecurityTrustHtml(this.article?.embedPrAdvert ?? '');
    }
  }

  private updateProps(): void {
    if (!this.article) {
      return;
    }
    this.buildLinks();
    this.setSponsorship();
    this.updateCaption();
    this.updateTags();
  }

  private updateTags(): void {
    const tagsWithFoundationTag = this.foundationTagSlug
      ? [
          {
            title: this.foundationTagTitle,
            slug: this.foundationTagSlug,
          },
          ...(this.article?.tags ?? []),
        ]
      : (this.article?.tags.filter((t) => t?.slug !== this.foundationTagSlug) ?? []);
    this.tags = tagsWithFoundationTag.slice(0, this.isMobile ? 1 : 10);
  }

  private buildLinks(): void {
    this.articleLinks = this.languages.map(
      ({ articlePublishDate: publishDate, articleSlug: slug, columnSlug }) =>
        buildArticleUrl({
          publishDate,
          slug,
          columnSlug,
        } as unknown as Article) ?? ''
    );
  }

  private setSponsorship(): void {
    this.isSponsored = !!this.sponsorship;
  }

  private updateCaption(): void {
    this.photoCaption = (this.article.thumbnailInfo?.caption ?? this.article.thumbnailInfo?.title ?? '').replace(/\.$/, '');
    this.photoCaption = this.photoCaption ? this.photoCaption + '.' : '';
  }
}
