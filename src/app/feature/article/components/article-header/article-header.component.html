<a *ngIf="sponsorship" [href]="sponsorship.url ?? '#'" [target]="sponsorship ? '_blank' : '_self'" class="article-header">
  <mno-article-page-image *ngIf="!!article.thumbnail && displayCover && sponsorship" [data]="article" [showCaption]="false"></mno-article-page-image>
  <div class="wrapper">
    <div class="sponsor-line">
      Szponzorált tartalom <span class="sponsor-name">{{ sponsorship?.title }}</span>
    </div>
  </div>
</a>

<div [class.wrapper]="!!sponsorship" class="article-header">
  <div *ngIf="!sponsorship" class="d-flex justify-content-between">
    <mno-tag-list [data]="article?.tags"></mno-tag-list>
    <ng-container *ngTemplateOutlet="languageBlock"></ng-container>
  </div>

  <h1 class="title">{{ article.title }}</h1>
  @if (!isVideo) {
    <h2 class="lead">{{ article.lead || article.excerpt }}</h2>
    <kesma-advertisement-adocean
      class="mt-15"
      [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
      *ngIf="roadblock_1_top as ad"
      [ad]="ad"
    ></kesma-advertisement-adocean>
    <kesma-advertisement-adocean
      class="mt-15"
      [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
      *ngIf="medium_rectangle_1_top as ad"
      [ad]="ad"
    ></kesma-advertisement-adocean>

    <div *ngIf="embedPrAdvert" [innerHTML]="embedPrAdvert" class="embed-pr-advert"></div>
  }

  <div class="info-line">
    <div class="d-flex flex-column">
      <div class="info-block">
        <ng-container *ngTemplateOutlet="authorBlock"></ng-container>
        <mno-tag-list *ngIf="sponsorship" [data]="tags"></mno-tag-list>
        <span *ngIf="article.articleSource" class="source">Forrás: {{ article.articleSource }}</span>
        <span *ngIf="!sponsorship" class="publishdate">{{ article.publishDate | dfnsFormat: 'Pp' }}</span>
      </div>
      <div *ngIf="isSponsored && (article.thumbnailInfo?.caption || article.thumbnailInfo?.title || article.thumbnailInfo?.photographer)" class="photo-data">
        <ng-container *ngIf="photoCaption">Fotó: {{ photoCaption }}</ng-container>
        {{ article.thumbnailInfo?.photographer }}
      </div>
    </div>

    <div class="social-icons">
      <mno-social-share [isMobile]="isMobile" [link]="[url]" [title]="article?.title" color="dark"></mno-social-share>
    </div>
  </div>

  <ng-content></ng-content>

  <mno-article-page-image *ngIf="!!article.thumbnail && displayCover && !sponsorship" [data]="article"></mno-article-page-image>
</div>

<ng-template #languageBlock>
  <div *ngIf="(languages?.length ?? 0) > 0" class="language-line">
    <a *ngFor="let language of languages; let i = index" [ngSwitch]="language?.languageTitle" [routerLink]="articleLinks[i]" class="language">
      <ng-container *ngSwitchCase="'English'"><i class="icon icon-flag en"></i>in english</ng-container>
      <ng-container *ngSwitchCase="'Angol'"><i class="icon icon-flag en"></i>in english</ng-container>
      <ng-container *ngSwitchCase="'Deutsch'"><i class="icon icon-flag de"></i>auf Deutsch</ng-container>
      <ng-container *ngSwitchCase="'Német'"><i class="icon icon-flag de"></i>auf Deutsch</ng-container>
      <ng-container *ngSwitchCase="'Français'"><i class="icon icon-flag fr"></i>en français</ng-container>
      <ng-container *ngSwitchCase="'Francia'"><i class="icon icon-flag fr"></i>en français</ng-container>
      <ng-container *ngSwitchDefault><i class="icon icon-flag hu"></i>magyar</ng-container>
    </a>
  </div>
</ng-template>

<ng-template #authorBlock>
  <div *ngIf="article?.publicAuthor" class="author-container">
    <ng-container *ngIf="article?.publicAuthorSlug; else noLink">
      <a [routerLink]="['/', 'szerzo', article?.publicAuthorSlug]" class="author">{{ article?.publicAuthor }}</a>
    </ng-container>
    <ng-template #noLink>
      <span class="author">{{ article?.publicAuthor }}</span>
    </ng-template>
  </div>
</ng-template>
