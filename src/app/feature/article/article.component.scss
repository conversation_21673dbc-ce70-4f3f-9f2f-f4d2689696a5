@use 'shared' as *;

:host {
  display: block;
  max-width: 100%;

  kesma-advertisement-adocean {
    overflow: hidden;
  }

  mno-opinion-card {
    max-width: 100%;
  }

  @include media-breakpoint-down(sm) {
    .w-full {
      padding-inline: 16px;
    }
  }
}

.olympics-header {
  @include media-breakpoint-down(sm) {
    padding: 0 $mobile-side-padding;
  }
}

.eb-mobile {
  display: none;
  @include media-breakpoint-down(sm) {
    display: block;
  }
}

.eb-desktop {
  display: block;
  @include media-breakpoint-down(sm) {
    display: none;
  }
}

.eb-body {
  .eb-sidebar {
    margin-bottom: 20px;

    ::ng-deep {
      .content-element {
        margin-bottom: 0;
      }
    }
  }

  ::ng-deep {
    mno-eb-podcast-block {
      @include media-breakpoint-up(md) {
        margin-top: -46px; // wrapper margin 25px + article block top margin: 21px
      }
    }
  }
}

.article-block {
  padding-top: 21px;

  &.is-opinion {
    background-color: var(--kui-blue-50);
  }

  .wrapper {
    width: 100%;
    display: flex;
    max-width: $article-max-width;
    margin: 25px auto;
    gap: 24px;

    &.w-100 {
      max-width: 100%;

      .article {
        max-width: inherit;
      }
    }

    @include media-breakpoint-down(sm) {
      flex-wrap: wrap;
      max-width: 100vw;
    }

    .article {
      max-width: calc(100% - 300px);
      gap: 24px;

      @include media-breakpoint-down(sm) {
        width: 100%;
        max-width: 100vw;
        padding: 0 $mobile-side-padding;
      }
    }

    > .sidebar {
      width: 300px;
      min-width: 300px;

      @include media-breakpoint-down(sm) {
        margin: 0;
        width: 100%;
        max-width: 100vw;
        padding: 0 24px;
      }
    }
  }

  app-article-recommended {
    [mno-article-card] {
      .article-card {
        padding-top: 39px;
        padding-bottom: 39px;
        border-bottom: 1px solid rgba(var(--kui-slate-800), 0.2);
        border-top: 1px solid rgba(var(--kui-slate-800), 0.2);
      }
    }
  }
}

.article-lead,
.article-subtitle {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0;
  text-align: left;
  margin: 0;
  padding: 0;

  &.has-initial {
    &:first-letter {
      display: inline-block;
      background: var(--kui-blue-900);
      border: 1px solid var(--kui-blue-900);
      font-family: var(--kui-font-secondary);
      color: var(--kui-blue-50);
      font-size: 56px;
      font-weight: 700;
      line-height: 44px;
      letter-spacing: 0;
      text-align: center;
      float: left;
      padding: 24px;
      margin: 0 24px 0 0;
      min-width: 84px;
      min-height: 84px;
    }
  }
}

.article-subtitle {
  color: var(--kui-slate-900);

  font-size: 16px;
  font-weight: 700;
  line-height: 21px; /* 131.25% */

  @include media-breakpoint-down(sm) {
    color: var(--kui-slate-950);
  }
}

.is-sorozatveto {
  .wrapper {
    @include media-breakpoint-down(md) {
      flex-wrap: wrap;
      max-width: 100vw;

      .article {
        width: 100%;
        max-width: 100vw;
        padding: 0 16px;
      }
    }
  }

  .article-lead {
    font-weight: 700;
    line-height: 21px;

    &.has-initial {
      &:first-letter {
        background: var(--kui-slate-950) !important;
        border-color: var(--kui-slate-950) !important;
      }
    }
  }
}

.minute-to-minute-top {
  border: 1px solid var(--kui-blue-500);
  color: var(--kui-blue-500);
  padding: 8px;
  font-size: 16px;
  font-weight: 700;
  line-height: 21px; /* 131.25% */
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
  border-radius: 2px;

  .minute-top-counter {
    border-left: 1px solid var(--kui-blue-500);
    padding-left: 16px;
  }
}

.minute-to-minute-container {
  .minute-to-minute-block {
    padding: 12px 24px 12px 0;
    border-bottom: 1px solid var(--kui-slate-950);
    border-right: 1px solid var(--kui-slate-950);

    .minute-to-minute-header {
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      gap: 4px;

      .minute-to-minute-date {
        width: 100%;
        display: flex;
        color: var(--kui-blue-500);
        border-bottom: 1px solid var(--kui-blue-500);
        padding: 0 0 4px 0;
        margin-bottom: 24px;

        font-size: 16px;
        font-weight: 700;
        line-height: 21px;
        letter-spacing: 0;
      }

      .minute-to-minute-title {
        display: flex;
        font-size: 20px;
        font-weight: 700;
        line-height: 26px;
        letter-spacing: 0.015em;
        text-align: left;
        margin-bottom: 16px;
      }
    }

    .minute-to-minute-body {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      letter-spacing: 0;
      text-align: left;
    }
  }
}

.sorozatveto-container {
  display: flex;
  justify-content: center;
  gap: 64px;
  margin: 32px auto;
  max-width: $article-max-width;
  width: 100%;

  @include media-breakpoint-down(sm) {
    gap: 0;
    padding: 0 15px;
  }
}

.pager {
  display: block;
  margin-bottom: 50px;
}

@media print {
  app-advertisement,
  app-article-recommended,
  app-recommended,
  app-you-may-also-like,
  app-dossier-box,
  app-article-gallery,
  [mno-article-card],
  .author-pic {
    display: none;
  }
}

.recommendation {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  width: 100%;
  gap: 16px;
  @include media-breakpoint-down(sm) {
    grid-template-columns: 1fr;
  }
}

.sorozatveto {
  &-highlight {
    background-size: cover;
    background-position: center;
    width: 100%;
  }

  &-main-opinions {
    display: grid;
    gap: 32px;
    grid-template-columns: 1fr 1fr;
    @include media-breakpoint-down(sm) {
      grid-template-columns: 1fr;
    }
  }

  &-opinion-card {
    display: flex;
    position: relative;
    flex-direction: column;
    gap: 8px;
    padding: 16px 16px 16px 0;
    border-right: 1px solid var(--kui-slate-950);
    border-bottom: 1px solid var(--kui-slate-950);

    &-header {
      mno-opinion-author {
        .author-name {
          font-size: 24px;
          font-weight: 500;
          line-height: 32px;
          letter-spacing: 0.01em;
        }
      }
    }

    &-link {
      display: block;
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      z-index: 1;
      transition: all 0.2s ease-in-out;
      opacity: 0.2;

      &:hover {
        background-color: var(--kui-blue-500);
      }
    }

    &-lead {
      p {
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        letter-spacing: 0em;
      }

      mno-wysiwyg-box::ng-deep {
        p {
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;
          letter-spacing: 0em;
        }

        .highlight {
          left: 0;
          width: 100%;
        }
      }
    }
  }
}

::ng-deep {
  @include media-breakpoint-down(xs) {
    mno-dossier-card.style-recommendationDossier {
      [mno-article-card].style-NoImgTitleBadge {
        .article-title {
          line-height: 32px !important;
        }
      }
    }

    app-recommendation-block,
    .recommendation {
      [mno-article-card].style-Img4TopTagsTitleLeadSmallBorder {
        .article-title {
          line-height: 32px !important;
        }
      }
    }

    mno-most-read {
      [mno-article-card].style-RelatedArticle {
        .article-title {
          line-height: 32px !important;
        }
      }
    }
  }
}

.article-recommender {
  &-title {
    color: var(--kui-slate-950);
    font-size: 14px;
    font-weight: 700;
    line-height: 18px; /* 128.571% */
    border-bottom: 1px solid var(--kui-slate-200);
    padding-bottom: 6px;
    margin-bottom: 4px;
  }

  .list-plus-darkblue {
    li {
      display: flex;
      align-items: center;
      gap: 8px;

      .icon-wrapper {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
}

.eb-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  &-container {
    max-width: 1056px;
    width: 100%;

    @include media-breakpoint-down(sm) {
      padding: 0 16px;
    }
  }
}

kesma-elections-box {
  margin: 30px 0;
}

.full-width {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

.lead {
  font-size: 20px;
  font-weight: 400;
  line-height: 24px;
  overflow-wrap: break-word;
}

.embed-pr-advert {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 10px auto;
}

.mt-15 {
  margin-top: 15px;
  display: block;
}
