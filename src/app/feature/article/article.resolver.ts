import { Inject, Injectable, Optional } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { RESPONSE, UtilService } from '@trendency/kesma-core';
import { forkJoin, Observable, of, take, throwError } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import type { Response } from 'express';
import { PortalConfigService } from 'src/app/shared/services/portal-config.service';
import { environment } from '../../../environments/environment';
import {
  ApiResponseMetaList,
  ApiResult,
  Article,
  ArticleReview,
  ArticleSearchResult,
  Author,
  backendDateToDate,
  buildArticleUrl,
  PortalConfigSetting,
  SorozatvetoOpinionCard,
} from '@trendency/kesma-ui';
import { ApiService, ArticleService, CacheService, CategoryService, EbImportantSidebarService } from '../../shared';
import { ArticleResolverData } from './article.definitions';
import { articleReviewsToSorozatvetoOpinionCard, hidePublicAuthorEmail } from './article.util';
import { ReviewService } from 'src/app/shared/services/review.service';

@Injectable({
  providedIn: 'root',
})
export class ArticleResolverService {
  constructor(
    private readonly apiService: ApiService,
    private readonly articleService: ArticleService,
    private readonly categoryService: CategoryService,
    private readonly reviewService: ReviewService,
    private readonly router: Router,
    private readonly utilsService: UtilService,
    private readonly portalConfigService: PortalConfigService,
    private readonly ebImportantSidebar: EbImportantSidebarService,
    private readonly cacheService: CacheService,
    @Inject(RESPONSE) @Optional() private readonly response: Response
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ArticleResolverData> {
    const foundationTagSlug = route.params['slug'];

    const brand = route.params['brand'];
    const previewHash = route.params['previewHash'];
    const slug = route.params['previewHash'] ? 'cikk-elolnezet' : route.params['articleSlug'];
    const previewType = route.params['previewType'] ? route.params['previewType'] : 'accepted';
    const isYear = !isNaN(route.params['year']);
    const year = isYear && route.params['year'] ? route.params['year'] : '';
    const month = isYear && route.params['month'] ? route.params['month'] : '';
    const categorySlug = brand ?? route.params['categorySlug'];
    const URL = foundationTagSlug ? `${foundationTagSlug}` : `${categorySlug}/${year}/${month}/${slug}`;

    if ((!year || !month) && !previewHash && !foundationTagSlug) {
      this.router
        .navigate(['/', '404'], {
          skipLocationChange: true,
        })
        .then();
    }

    let request$: Observable<any>;
    if (previewHash) {
      request$ = this.articleService.getArticlePreview(slug, previewHash, previewType).pipe(
        switchMap(this.getArticleWithReviews.bind(this)),
        map((article) => ({ article }))
      );
    } else {
      request$ = (foundationTagSlug ? this.apiService.getArticlesByFoundationTag(foundationTagSlug) : of({})).pipe(
        switchMap((searchResult: ApiResult<ArticleSearchResult[], ApiResponseMetaList> | any) => {
          let fCategorySlug, fYear, fMonth, fArticleSlug, firstArticleWithFoundationTag;
          if (foundationTagSlug) {
            firstArticleWithFoundationTag = searchResult?.data?.[0];

            if (firstArticleWithFoundationTag) {
              fCategorySlug = firstArticleWithFoundationTag?.columnSlug;
              fYear = firstArticleWithFoundationTag?.publishDate?.getUTCFullYear() ?? null;
              fMonth = (firstArticleWithFoundationTag?.publishDate?.getUTCMonth() || 0) + 1;
              fArticleSlug = firstArticleWithFoundationTag?.slug;
            }
          }

          if (!(foundationTagSlug && firstArticleWithFoundationTag) && !categorySlug) {
            return of(null);
          }
          const ebImportantSidebar$ = this.ebImportantSidebar.getLayout(categorySlug).pipe(take(1));

          return forkJoin({
            article: (foundationTagSlug && firstArticleWithFoundationTag
              ? this.articleService.getArticle(fCategorySlug ?? '', fYear ?? '', fMonth ?? '', fArticleSlug ?? '')
              : this.articleService.getArticle(categorySlug, year, month, slug)
            )
              .pipe(
                tap(({ meta }) => this.cacheService.setCache(meta)),
                map(({ data, meta }) => ({
                  meta,
                  data: {
                    ...data,
                    publishDate: backendDateToDate((data.publishDate ?? '') as unknown as string) as Date,
                    rawPublishDate: data.publishDate as unknown as string,
                    lastUpdated: backendDateToDate((data.lastUpdated ?? '') as unknown as string) as Date,
                    rawLastUpdated: data.publishDate as unknown as string,
                    isInterviewType: data.primaryColumn?.slug === 'interview' || (data.secondaryColumns ?? []).some(({ slug }) => slug === 'interview'),
                  },
                }))
              )
              .pipe(switchMap(this.getArticleWithReviews.bind(this))),
            url: of(URL),
            recommendations: this.articleService.getArticleRecommendations(foundationTagSlug ? searchResult?.data?.[0]?.slug : slug).pipe(
              catchError(() =>
                of({
                  data: {},
                  meta: {},
                } as any)
              )
            ),
            sorozatvetoArticles: this.categoryService.getCategoryArticles('sorozatveto', 1, 6).pipe(catchError(() => of({ data: [], meta: {} } as any))),
            velemenyvaroArticles: this.categoryService.getCategoryArticles('velemenyvaro', 1, 6).pipe(catchError(() => of({ data: [], meta: {} } as any))),
            foundationTagSlug: of(foundationTagSlug),
            foundationTagTitle: of(foundationTagSlug ? searchResult?.data?.[0]?.foundationTagTitle : undefined),
            slug: of(foundationTagSlug ? searchResult?.data?.[0]?.slug : slug),
            ebImportantSidebarLayout: ebImportantSidebar$,
          });
        })
      );
    }

    return request$.pipe(
      tap((res) => {
        const isYear = !isNaN(route.params['year']);
        const isMonth = !isNaN(route.params['month']);
        const categorySlug = route.params['categorySlug'];
        const article = res?.article;

        if (!isYear && !isMonth && !categorySlug?.length) return;
        if (!article || !article.data?.slug) return;

        if (article.data?.slug !== slug) {
          const url = buildArticleUrl(article.data);
          if (this.utilsService.isBrowser()) {
            this.router.navigate(url);
          } else {
            this.response.status(301);
            this.response.setHeader('location', `${environment.siteUrl}${url.join('/').slice(1)}`);
          }
        }
      }),
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });

        return throwError(error);
      })
    );
  }

  private getArticleWithReviews({ data, meta }: ApiResult<Article>): Observable<
    ApiResult<
      ArticleReview & {
        reviews: SorozatvetoOpinionCard[];
      }
    >
  > {
    if (!this.portalConfigService.isConfigSet(PortalConfigSetting.CONTENT_ARTICLE_REVIEWABLE_CHECKBOX)) {
      return of({
        data: {
          ...(hidePublicAuthorEmail(data) as unknown as ArticleReview),
          reviews: [] as SorozatvetoOpinionCard[],
          opinioners: [] as Author[],
          sorozatvetoArticles: [],
          velemenyvaroArticles: [],
        },
        meta,
      });
    }

    return this.reviewService.getReviews(data?.id as string).pipe(
      map((reviews) => ({ article: { data, meta }, reviews })),
      map(({ article, reviews }) => ({
        data: {
          ...(hidePublicAuthorEmail(article.data) as unknown as ArticleReview),
          reviews: articleReviewsToSorozatvetoOpinionCard(reviews),
          opinioners: this.getOpinioners(reviews),
        },
        meta,
      }))
    );
  }

  private getOpinioners(reviews: ArticleReview[]): Author[] {
    return reviews.map((review) => ({
      name: review.publicAuthorName || review.publicAuthor?.authorData?.fullName || '',
      avatarUrl:
        review.publicAuthorImageUrl ||
        review.publicAuthorAvatarThumbnailUrl ||
        review.publicAuthor?.avatarThumbnail ||
        `${environment?.siteUrl}/assets/images/mn-avatar.png`,
    }));
  }
}
