import {
  ApiListResult,
  ApiResponseMeta,
  ApiResult,
  Article,
  ArticleReview,
  BackendArticleSearchResult,
  BackendRecommendationsData,
  Layout,
  SorozatvetoOpinionCard,
  SponsoredTag,
} from '@trendency/kesma-ui';

export interface ArticleResolverData {
  article: ApiResult<Article & ArticleReview & { reviews: SorozatvetoOpinionCard[] }, ApiResponseMeta & { sponsoredTag?: SponsoredTag }>;
  sorozatvetoArticles: ApiListResult<BackendArticleSearchResult>;
  velemenyvaroArticles: ApiListResult<BackendArticleSearchResult>;
  url: string;
  recommendations: ApiR<PERSON>ult<BackendRecommendationsData>;
  authorArticles: ApiListResult<Article>;
  foundationTagSlug?: string;
  foundationTagTitle?: string;
  ebImportantSidebarLayout?: Layout | null;
}
