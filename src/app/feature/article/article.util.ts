import { environment } from '../../../environments/environment';
import { Article, ArticleReview, SorozatvetoOpinionCard } from '@trendency/kesma-ui';

export const hidePublicAuthorEmail = <
  C extends {
    publicAuthor?: string;
    publicAuthorEmail?: string;
  } = Article,
>(
  article: C
): C => {
  const { publicAuthor, publicAuthorEmail } = article;

  return {
    ...article,
    publicAuthorEmail: publicAuthor ? publicAuthorEmail : undefined,
  };
};

export const articleReviewsToSorozatvetoOpinionCard = (articleReviews: ArticleReview[]): SorozatvetoOpinionCard[] =>
  articleReviews.map((review) => ({
    author: {
      name: review.publicAuthorName || review.publicAuthor?.authorData?.fullName || '',
      avatarUrl:
        review.publicAuthorImageUrl ||
        review.publicAuthorAvatarThumbnailUrl ||
        review.publicAuthor?.avatarThumbnail ||
        `${environment?.siteUrl}/assets/images/mn-avatar.png`,
    },
    lead: review.excerpt || review.lead,
    body: review.body?.[0]?.details?.[0]?.value ?? '',
    slug: review.slug,
  }));
