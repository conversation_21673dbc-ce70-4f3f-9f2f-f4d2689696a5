import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ApiService } from 'src/app/shared/services/api.service';

@Injectable({
  providedIn: 'root',
})
export class BrandingResolverService {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<any> | Promise<any> | any {
    const slug = route.params['articleSlug'];
    const previewHash = route.queryParams['previewHash'];

    // eslint-disable-next-line rxjs/finnish
    const request: Observable<any> = previewHash
      ? this.apiService.getBrandingPreview(slug, previewHash)
      : forkJoin({
          article: this.apiService.getBrandingArticle(route.data['routeType'], route.params['year'], route.params['month'], slug),
          recommendations: this.apiService.getBrandingRecommendations(slug),
        });

    return request.pipe(
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });
        return throwError(error);
      })
    );
  }
}
