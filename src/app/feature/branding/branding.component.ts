import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { ArticleSchema, IMetaData, SchemaOrgService, SeoService, UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  Article,
  ArticlePageImageComponent,
  getStructuredDataForArticle,
  PAGE_TYPES,
  Tag,
} from '@trendency/kesma-ui';
import { isObject } from 'lodash-es';
import { Observable, of, Subject } from 'rxjs';
import { switchMap, takeUntil } from 'rxjs/operators';
import {
  ArticleRecommendation,
  Author,
  ExternalRecommendation,
  IArticle,
  IArticleResponse,
  IComponentData,
  Suggestion,
} from 'src/app/shared/definitions/app.definitions';
import { FormatDate } from '../../shared';
import { environment } from '../../../environments/environment';
import { ArticleTextComponent, SocialShareComponent, TagListComponent } from 'src/app/shared';
import { NgForOf, NgIf, NgSwitch, NgSwitchCase } from '@angular/common';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { DateFnsModule } from 'ngx-date-fns';

// declare global {
//   interface Window {
//     dataLayer: GTMDataLayer[];
//   }
// }

@Component({
  selector: 'app-branding',
  templateUrl: './branding.component.html',
  styleUrls: ['./branding.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [
    AdvertisementAdoceanComponent,
    RouterLink,
    TagListComponent,
    SocialShareComponent,
    ArticlePageImageComponent,
    NgIf,
    NgSwitch,
    NgSwitchCase,
    SidebarComponent,
    ArticleTextComponent,
    NgForOf,
    DateFnsModule,
  ],
  providers: [FormatDate],
})
export class BrandingComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('dataTrigger') readonly dataTrigger: ElementRef;

  public articleResponse: IArticleResponse;
  public recommendationData: ArticleRecommendation;
  public internalRecommendation: IArticle[];

  public externalRecommendation: ExternalRecommendation[];

  public articleSource: string;
  public tag: string;
  public category: string;
  public categorySlug: string;
  public title: string;
  public type: string;
  public lead: string;
  public publishDate: string;
  public tags: Tag[];
  public suggestions: Suggestion[];
  public avatar?: string;
  public description?: string;
  public lastUpdated: string;
  public subTitle: string;
  public thumbnail: string;
  public body: IComponentData[];
  public publicAuthor: string;
  public id: string;
  adverts: AdvertisementsByMedium;
  adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  articleUrl = new URL(this.seo.currentUrl).pathname;
  routeType: string;
  readonly allowedBrands = ['auto-motor', 'mindmegette', 'lakaskultura', 'orszagszerte', 'unilife'];

  private readonly destroy$ = new Subject<boolean>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly utilsService: UtilService,
    private readonly formatDate: FormatDate,
    private readonly analyticsService: AnalyticsService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly schema: SchemaOrgService
  ) {}

  get isNeedToModifyRobots(): boolean {
    return !!this.allowedBrands.includes(this.routeType);
  }

  public get article(): Article {
    return {
      ...this.articleResponse,
      thumbnail: this.thumbnail,
    } as unknown as Article;
  }

  ngOnInit(): void {
    this.route.data
      .pipe(
        // eslint-disable-next-line rxjs/no-unsafe-takeuntil
        takeUntil(this.destroy$),
        switchMap((res): Observable<Advertisement[] | null> => {
          this.articleResponse = res['data'].article ? res['data'].article.data : res['data'].data;
          this.recommendationData = res['data']?.recommendations?.data;
          this.suggestions = res['data'].recommendations ? res['data'].recommendations?.data?.externalRecommendation : null;
          this.suggestions = this.filterSuggestionsByCount(this.suggestions, 6) as any;
          this.routeType = res['routeType'];
          if (!this.articleResponse) {
            return of(null);
          }
          const { year, month, articleSlug } = this.route.snapshot.params;
          const { routeType } = this.route.snapshot.data;
          const canonicalUrl = `${this.seo.hostUrl}/brand/${routeType}/${year}/${month}/${articleSlug}`;
          this.seo.updateCanonicalUrl(canonicalUrl || '', { addHostUrl: false, skipSeoMetaCheck: true });
          this.adStore.getAdvertisementMeta(this.articleResponse?.tags as Tag[]);
          this.adPageType = `column_${this.articleResponse.primaryColumn?.slug}`;
          this.adStore.setArticleParentCategory(this.adPageType);
          const {
            primaryColumn,
            title,
            type,
            lead,
            tags,
            publishDate,
            lastUpdated,
            avatar,
            description,
            subTitle,
            thumbnailUrl,
            thumbnail,
            body,
            publicAuthor,
            id,
            articleSource,
          } = this.articleResponse || {};
          this.category = primaryColumn?.title;
          this.categorySlug = primaryColumn?.slug;
          this.title = title;
          this.id = id as any;
          this.type = typeof type === 'object' ? (type as any)?.name : type;
          this.lead = lead;
          this.tags = tags as Tag[];
          this.publishDate = publishDate;
          this.lastUpdated = lastUpdated as any;
          this.avatar = isObject(publicAuthor) ? (publicAuthor as Author).avatar : avatar || 'assets//images//magyar-nemzet-logo-circle.png';
          this.description = description;
          this.subTitle = subTitle;
          this.thumbnail = thumbnailUrl || (thumbnail as any);

          this.body = body;
          this.publicAuthor = isObject(publicAuthor) ? (publicAuthor as Author).name : publicAuthor;
          this.articleSource = articleSource;
          if (!this.recommendationData) {
            return of(null);
          }
          this.externalRecommendation = this.recommendationData?.externalRecommendation;

          this.internalRecommendation = this.recommendationData.brandingBoxes as any;
          this.setMetaData();
          this.scrollTopAfterLoad();
          this.applySchemaOrg({ ...this.articleResponse, thumbnail: this.thumbnail });
          return this.adStore.advertisemenets$;
        })
      )
      .subscribe((adverts) => {
        this.adverts = this.adStore.separateAdsByMedium(adverts as Advertisement[]);
        this.adStore.onArticleLoaded();
        this.changeRef.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
    this.adStore.onArticleDestroy();
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.route.data.pipe(takeUntil(this.destroy$)).subscribe(() => {
        if (this.utilsService.isBrowser() && 'IntersectionObserver' in window) {
          this.triggerDataLayer();
        }
      });
    }, 1000);
  }

  private filterSuggestionsByCount(suggestions: Suggestion[], elementCount: number): Suggestion[] | null {
    if (!suggestions) {
      return null;
    }
    return suggestions.slice(0, elementCount);
  }

  private setMetaData(): void {
    if (!this.articleResponse) {
      return;
    }

    const robots = 'index, follow';
    const { title, lead, thumbnail, publishDate } = this.articleResponse;

    const metaData: IMetaData = {
      title: `${title} | Magyar Nemzet`,
      description: lead,
      ogTitle: `${title} | Magyar Nemzet`,
      ogImage: thumbnail || this.articleResponse.thumbnailUrl,
      ogType: 'article',
      articlePublishedTime: publishDate,
      robots: this.isNeedToModifyRobots ? robots + ', max-image-preview:large' : robots,
    };

    this.seo.setMetaData(metaData);
  }

  private scrollTopAfterLoad(): void {
    if (this.utilsService.isBrowser()) {
      window.scrollTo(0, 0);
    }
  }

  private triggerDataLayer(): void {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(({ isIntersecting }) => {
        if (isIntersecting) {
          this.sendEcommerceEvent();
          // this.gtmDataLayer();
          observer.unobserve(this.dataTrigger.nativeElement);
        }
      });
    });
    observer.observe(this.dataTrigger.nativeElement);
  }

  // private gtmDataLayer() {
  //   window.dataLayer.push({ ecommerce: null }); // Ürítjük az előző ecommerce objektumot
  //   window.dataLayer.push({
  //     event: 'purchase',
  //     ecommerce: {
  //       transaction_id: `T${this.id}`,
  //       items: [
  //         {
  //           item_name: this.title,
  //           item_id: this.id,
  //           item_category: this.category,
  //           article_author: this.publicAuthor,
  //           article_source: this.articleSource,
  //           article_title: this.title,
  //           page_category: this.category,
  //           article_dates: this.formatDate.transform(
  //             this.publishDate,
  //             'dateTime'
  //           ),
  //           last_update_dates: this.formatDate.transform(
  //             this.lastUpdated,
  //             'dateTime'
  //           ),
  //           quantity: 1, // Termék darabszáma
  //         },
  //       ],
  //     },
  //   });
  // }

  private sendEcommerceEvent(): void {
    this.analyticsService.sendEcommerceEvent({
      id: `T${this.id}`,
      title: this.title,
      articleSlug: this.route.snapshot.params['articleSlug'] ? this.route.snapshot.params['articleSlug'] : 'cikk-elonezet',
      category: this.category,
      articleSource: this.articleSource ? this.articleSource : 'no source',
      publishDate: this.formatDate.transform(this.publishDate, 'dateTime'),
      lastUpdatedDate: this.formatDate.transform(this.lastUpdated ? this.lastUpdated : this.publishDate, 'dateTime'),
    });
  }

  private applySchemaOrg(articleResponse: IArticleResponse): void {
    const newsArticle: ArticleSchema = getStructuredDataForArticle(articleResponse as unknown as Article, this.seo.currentUrl, environment?.siteUrl ?? '');
    this.schema.removeStructuredData();
    this.schema.insertSchema(newsArticle);
  }
}
