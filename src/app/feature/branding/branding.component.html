<section class="block article-block">
  <div class="wrapper">
    <div class="article">
      <div class="article-header">
        <div class="short-lead" *ngIf="subTitle">
          {{ subTitle }}
        </div>
        <h1 class="title">
          {{ title }}
        </h1>

        <h2 class="mbm-subtitle">{{ lead }}</h2>

        <div class="info-line">
          <div class="d-flex flex-column">
            <div class="info-block">
              <div *ngIf="publicAuthor" class="author-container">
                <a [routerLink]="['/', 'szerzo', article?.publicAuthorSlug]" class="author">{{ article?.publicAuthor }}</a>
              </div>

              <mno-tag-list [data]="tags" *ngIf="tags?.length"></mno-tag-list>

              <span *ngIf="article.articleSource" class="source">Forrás: {{ article.articleSource }}</span>
              <span class="publishdate">{{ article.publishDate | dfnsFormat: 'Pp' }}</span>
            </div>
            <div *ngIf="article.thumbnailInfo?.caption || article.thumbnailInfo?.title || article.thumbnailInfo?.photographer" class="photo-data">
              {{ article.thumbnailInfo?.photographer }}
            </div>
          </div>

          <div class="social-icons">
            <mno-social-share [link]="[articleUrl]" [title]="article?.title" color="dark"></mno-social-share>
          </div>
        </div>

        <kesma-article-page-image *ngIf="thumbnail" [data]="article"></kesma-article-page-image>
      </div>

      <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_1 as ad" [ad]="ad"> </kesma-advertisement-adocean>
      <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_1 as ad" [ad]="ad"> </kesma-advertisement-adocean>

      <ng-container *ngFor="let element of body">
        <ng-container [ngSwitch]="element.type">
          <!--app-article-recommended *ngSwitchCase="'ContentPage.Article'" [data]='element'>
          </app-article-recommended-->
          <app-article-text *ngSwitchCase="'Basic.Wysiwyg.Wysiwyg'" [data]="element"></app-article-text>

          <!--app-article-gallery *ngSwitchCase="'Media.Gallery.Gallery'" [data]='element'></app-article-gallery-->
        </ng-container>
      </ng-container>

      <div *ngIf="articleResponse" #dataTrigger></div>

      <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
      <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
      <!--app-recommended *ngIf="internalRecommendation" [data]='internalRecommendation'></app-recommended-->

      <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
      <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>

      <!--app-you-may-also-like *ngIf="externalRecommendation" [data]='externalRecommendation'></app-you-may-also-like-->

      <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_4 as ad" [ad]="ad"> </kesma-advertisement-adocean>
      <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_4 as ad" [ad]="ad"> </kesma-advertisement-adocean>
    </div>
    <div class="sidebar">
      <app-sidebar [adPageType]="adPageType"></app-sidebar>
    </div>
  </div>
</section>
