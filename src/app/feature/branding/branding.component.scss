@use 'shared' as *;

$black-4: #131313;
$white-6: #e6e6e6;
$black-3: #555555;
$blue-5: #1877f2;
$grey-12: #818181;

.wrapper {
  gap: 24px;
}

.article-block {
  @include media-breakpoint-down(md) {
    margin: 0 16px;
  }

  .wrapper {
    max-width: 1056px;
    display: flex;
    margin-top: 25px;

    @include media-breakpoint-down(md) {
      flex-wrap: wrap;
    }

    .article {
      max-width: 100%;

      @include media-breakpoint-down(sm) {
        width: 100%;
      }

      .article-header {
        color: $black-4;

        .mbm-subtitle {
          font-size: 20px;
          font-weight: 400;
          line-height: 24px;
          overflow-wrap: break-word;

          @include media-breakpoint-down(xs) {
            margin-bottom: 20px;
            margin-top: 15px;
          }
        }

        .short-lead {
          font-size: 20px;
          font-weight: 500;
          line-height: 34px;
          text-transform: uppercase;
          margin-bottom: 15px;

          @include media-breakpoint-down(xs) {
            font-size: 18px;
            font-weight: 500;
            line-height: 30px;
            text-transform: none;
          }
        }

        .title {
          font-size: 32px;
          line-height: 40px;
          margin-bottom: 8px;

          @include media-breakpoint-down(xs) {
            font-size: 30px;
            font-weight: 700;
            line-height: 40px;
          }
        }

        .info-line {
          margin-bottom: 30px;
          margin-top: 8px;
          border-top: 1px solid $white-6;
          border-bottom: 1px solid $white-6;
          display: flex;
          flex-wrap: wrap;
          padding: 6px;
          justify-content: space-between;

          &.dossier {
            span {
              font-weight: 400;
              color: $black-3;
              display: inline-flex;
              margin-left: 10px;
            }
          }

          .fb-icon {
            display: inline-flex;
            width: 30px;
            height: 31px;
            border-radius: 2px;
            background-color: $blue-5;
            justify-content: center;
            align-items: center;

            .icon-facebook {
              width: 8px;
              height: 17px;
              @include icon('icon-facebook.svg');
            }
          }

          .left {
            text-transform: uppercase;
            color: var(--kui-red);
            font-size: 16px;
            font-weight: 500;
            display: flex;
            height: 30px;
            align-items: center;

            @include media-breakpoint-down(xs) {
              font-size: 12px;
            }

            .author-pic {
              width: 30px;
              height: 30px;
              background-size: cover;
              background-position: center;
              background-repeat: no-repeat;
              margin-right: 10px;
            }
          }

          .right {
            height: 30px;
            align-items: center;
            display: flex;
            font-size: 14px;
            line-height: 18px;
            justify-content: flex-end;

            @include media-breakpoint-down(xs) {
              font-size: 12px;
            }

            .icon-last-change {
              width: 15px;
              height: 15px;
              @include icon('reload.svg');
              margin-right: 5px;
              margin-left: 16px;
              position: relative;
              top: -1px;
            }
          }

          .source {
            height: 30px;
            align-items: center;
            display: flex;
            font-size: 14px;
            line-height: 18px;

            @include media-breakpoint-down(xs) {
              font-size: 12px;
              width: 100%;
            }
          }
        }
      }
    }

    .sidebar {
      width: 300px;

      @include media-breakpoint-down(md) {
        width: 100%;
      }

      app-layout,
      mno-newspaper-box-sidebar,
      mno-promo-block,
      app-whead-kulturnemzet {
        width: 100%;
        max-width: 100%;
      }
    }
  }

  app-article-recommended {
    app-article-card {
      .article-card {
        padding-top: 39px;
        padding-bottom: 39px;
        border-bottom: 1px solid rgba($grey-12, 0.2);
        border-top: 1px solid rgba($grey-12, 0.2);
      }
    }
  }

  app-article-text {
    display: block;
    margin-top: 40px;
  }
}
