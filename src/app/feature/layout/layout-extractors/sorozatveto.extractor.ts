import {
  DataExtractorFunction,
  LayoutDataExtractor,
  LayoutDataExtractorService,
  LayoutElementContent,
  LayoutElementContentSorozatveto,
  SorozatvetoArticleCard,
} from '@trendency/kesma-ui';
import { inject, Injectable } from '@angular/core';
import { SorozatvetoLayoutService } from '../../../shared';

@Injectable()
export class SorozatvetoExtractor implements LayoutDataExtractorService<SorozatvetoArticleCard | null> {
  private readonly sorozatvetoLayoutService = inject(SorozatvetoLayoutService);

  extractData: DataExtractorFunction<SorozatvetoArticleCard | null> = (element: LayoutElementContent) => {
    if (!element.config) {
      return;
    }
    const articleId = (element.config as LayoutElementContentSorozatveto['config']).selectedArticles?.[0]?.id;
    if (articleId) {
      this.sorozatvetoLayoutService.loadReviews(articleId);
    }
    const legacyExtractor = new LayoutDataExtractor();
    const opinions = legacyExtractor.getSorozatvetoData(element as LayoutElementContentSorozatveto);
    return {
      data: opinions,
      meta: {
        extractedBy: SorozatvetoExtractor.name,
      },
    };
  };
}
