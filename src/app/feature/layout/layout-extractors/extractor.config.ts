import { ArticleArrayExtractor, DataExtractor, LayoutElementContentType, OpinionArrayExtractor } from '@trendency/kesma-ui';
import { SorozatvetoExtractor } from './sorozatveto.extractor';

export const MNO_EXTRACTORS_CONFIG: DataExtractor<unknown>[] = [
  {
    extractor: ArticleArrayExtractor,
    supportedContentTypes: [LayoutElementContentType.RELATED_ARTICLES],
  },
  {
    extractor: SorozatvetoExtractor,
    supportedContentTypes: [LayoutElementContentType.Sorozatveto],
  },
  {
    extractor: OpinionArrayExtractor,
    supportedContentTypes: [LayoutElementContentType.BAYER_BLOG],
  },
];
