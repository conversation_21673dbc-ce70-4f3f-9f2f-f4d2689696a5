<kesma-layout
  [adPageType]="adPageType"
  [blockTitleRef]="blockTitles"
  [blockTitleWrapperRef]="blockTitleWrapper"
  [breakingNews]="breakingNews"
  [configuration]="configuration"
  [contentComponentInnerWrapperRef]="contentComponentsInnerWrapper"
  [contentComponentWrapperRef]="contentComponentsWrapper"
  [contentComponentsRef]="contentComponents"
  [layoutType]="layoutType ?? LayoutPageTypes.HOME"
  [structure]="structure"
  [editorFrameSize]="editorFrameSize"
></kesma-layout>

<ng-template #blockTitles let-layoutElement="layoutElement" let-layoutType="layoutType">
  <ng-container *ngIf="layoutElement.contentType !== LayoutElementContentType.MOST_VIEWED && layoutElement.contentType !== LayoutElementContentType.FreshNews">
    <mno-block-title-row
      [data]="layoutElement.blockTitle ?? { text: 'Blokk cím' }"
      [headingLevel]="layoutType === LayoutPageTypes.COLUMN && layoutElement.id === firstBlockTitle ? 1 : 2"
      [isLayout]="true"
      [isRow]="true"
      [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
    ></mno-block-title-row>
  </ng-container>
</ng-template>

<ng-template #contentComponents let-desktopWidth="desktopWidth" let-extractor="extractor" let-index="index" let-layoutElement="layoutElement">
  <ng-container *ngIf="layoutElement?.config || layoutElement?.configurable === false">
    <ng-container *ngIf="layoutElement.contentType === 'ad'">
      <kesma-advertisement-adocean
        *ngIf="layoutElement.ad"
        [ad]="layoutElement.ad"
        [isHidden]="layoutElement.contentType !== 'ad' && !layoutElement.ad"
      ></kesma-advertisement-adocean>
    </ng-container>

    <ng-container
      *ngIf="
        layoutElement.contentType === LayoutElementContentType.Opinion &&
        (!layoutElement.secondaryContentType || layoutElement.secondaryContentType === LayoutElementContentType.Opinion)
      "
    >
      <ng-container *ngIf="layoutElement.styleId === OpinionCardType.ArticleCardTopTagsTitleLeadBadge.valueOf(); else normalOpinion">
        <article
          *ngIf="layoutElement.extractorData?.[index] as data"
          [data]="data"
          [inLayout]="true"
          [isOpinion]="true"
          [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
          [showArticleLabels]="showLabels"
          [styleID]="ArticleCardType.Img16TopTagsTitleLeadBadge"
          mno-article-card
        ></article>
      </ng-container>
      <ng-template #normalOpinion>
        <mno-opinion-card
          *ngIf="layoutElement.extractorData?.[index] as data"
          [data]="data"
          [inLayout]="true"
          [isMobile]="isMobile$ | async"
          [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR || desktopWidth <= 3"
          [showOpinionLabels]="showLabels"
          [styleID]="layoutElement.styleId"
        ></mno-opinion-card>
      </ng-template>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BLOG">
      <mno-blog-recommendation *ngIf="layoutElement.extractorData?.[index] as data" [data]="data" [styleID]="layoutElement.styleId"></mno-blog-recommendation>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Article">
      <article
        *ngIf="layoutElement.extractorData?.[index] as data"
        [attr.data-style-id]="layoutElement.styleId"
        [data]="data"
        [inLayout]="true"
        [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
        [showArticleLabels]="showLabels"
        [styleID]="layoutElement.styleId"
        mno-article-card
      ></article>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Kulturnemzet">
      <app-widget-kulturnemzet [inSidebar]="true" [showLabels]="showLabels"></app-widget-kulturnemzet>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.CultureNation">
      <app-widget-kulturnemzet [showLabels]="showLabels"></app-widget-kulturnemzet>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Quiz">
      <app-quiz *ngIf="layoutElement.extractorData as data" [data]="data"></app-quiz>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.SOCIAL_MEDIA">
      <mno-promo-block
        (subscriptionClick)="onSubscribe()"
        [facebookLink]="socialInfo.facebookLink"
        [instagramLink]="socialInfo.instagramLink"
        [twitterLink]="socialInfo.twitterLink"
        [videaLink]="socialInfo.videaLink"
        [youtubeLink]="socialInfo.youtubeLink"
      ></mno-promo-block>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BrandingBox">
      <app-widget-branding-box *ngIf="layoutElement.extractorData as data" [brand]="layoutElement.brand" [showLabels]="showLabels" [widgetData]="data">
      </app-widget-branding-box>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BrandingBoxEx">
      <app-mno-branding-box-ex [desktopWidth]="desktopWidth" [brand]="layoutElement.brand" [showLabels]="showLabels"></app-mno-branding-box-ex>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.NEWSPAPER">
      <mno-newspaper-box-sidebar></mno-newspaper-box-sidebar>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.VisegradPost">
      <app-widget-visegrad *ngIf="layoutElement.extractorData as data" [widgetData]="data"></app-widget-visegrad>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Sorozatveto">
      <mno-sorozatveto-layout
        *ngIf="layoutElement.extractorData as data"
        [data]="data"
        [opinionCards]="(sorozatvetoLayoutService.sorozatvetoCache[data.id] | async) ?? []"
        [styleID]="layoutElement.styleId"
      ></mno-sorozatveto-layout>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.FastNews">
      <article
        *ngIf="layoutElement.extractorData as data"
        [data]="data"
        [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
        [showArticleLabels]="showLabels"
        [styleID]="layoutElement.styleId"
        mno-article-card
      ></article>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.PrBlock">
      <article
        *ngIf="layoutElement.extractorData?.[index] as data"
        [data]="data"
        [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
        [showArticleLabels]="showLabels"
        [styleID]="layoutElement.styleId"
        mno-article-card
      ></article>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.PodcastBlock">
      <mno-recommender-block
        *ngIf="layoutElement.extractorData as data"
        [buttonUrl]="layoutElement.btnUrl"
        [data]="data"
        [styleID]="RecommenderBlockType.Podcast"
        [desktopWidth]="layoutType === LayoutPageTypes.SIDEBAR ? 1 : desktopWidth"
      ></mno-recommender-block>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Gallery">
      <ng-container *ngIf="layoutElement.extractorData?.length > 1; else singleGallery">
        <mno-recommender-block
          [data]="layoutElement.extractorData"
          [styleID]="RecommenderBlockType.Gallery"
          [desktopWidth]="layoutType === LayoutPageTypes.SIDEBAR ? 1 : desktopWidth"
        ></mno-recommender-block>
      </ng-container>
      <ng-template #singleGallery>
        <article
          *ngIf="layoutElement.extractorData?.[0] as gallery"
          [asGallery]="true"
          [data]="gallery"
          [showArticleLabels]="showLabels"
          [styleID]="ArticleCardType.Gallery"
          mno-article-card
        ></article>
      </ng-template>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Wysiwyg">
      <mno-wysiwyg-box [htmlArray]="layoutElement.extractorData"></mno-wysiwyg-box>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Dossier">
      @if (layoutElement.extractorData?.[0]?.sponsorship) {
        <app-dossier-sponsoration-header [data]="layoutElement.extractorData?.[0]"></app-dossier-sponsoration-header>
      }
      <mno-dossier-card
        [class.smaller-thumbnail]="desktopWidth <= 4"
        [data]="layoutElement.extractorData?.[0]"
        [styleID]="layoutElement.styleId"
        [showArticleLabels]="layoutType !== LayoutPageTypes.HOME"
      >
      </mno-dossier-card>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Image">
      <ng-container *ngIf="asArticleImage(layoutElement.extractorData) as data">
        <ng-container *ngIf="data.url; else imageBlock">
          <a [href]="data.url">
            <ng-container *ngTemplateOutlet="imageBlock"></ng-container>
          </a>
        </ng-container>
        <ng-template #imageBlock>
          <mno-article-page-image [data]="data"></mno-article-page-image>
        </ng-template>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Vote">
      @if (layoutElement.extractorData; as extractorData) {
        @if ((voteCache[extractorData?.data?.id] | async) || extractorData; as voteData) {
          <mno-voting (vote)="onVotingSubmit($event, voteData)" *ngIf="voteData.data" [data]="voteData.data" [voteId]="voteData.votedId" />
        }
      }
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.SPONSORED_VOTE">
      <ng-container *ngIf="layoutElement.extractorData as extractorData">
        @if (layoutElement.extractorData; as extractorData) {
          @if ((voteCache[extractorData?.data?.id] | async) || extractorData; as voteData) {
            <kesma-sponsored-voting [data]="voteData?.data" [voteId]="voteData?.votedId" />
          }
        }
      </ng-container>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.koponyeg">
      <kesma-koponyeg [data]="KoponyegDefinitions" [type]="KoponyegType"></kesma-koponyeg>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.Breaking">
      !!! BREAKING CARD !!!
      <!--
      <mno-breaking-article-card [hasImage]="layoutElement.hasImage"
                                        [styleID]="layoutElement.styleId"
                                        [data]="getData('getBreakingBlockData', layoutElement, extractor)">
      </mno-breaking-article-card>
      -->
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT">
      <mno-recommender-block
        [data]="layoutElement.extractorData"
        [styleID]="RecommenderBlockType.Video"
        [desktopWidth]="layoutType === LayoutPageTypes.SIDEBAR ? 1 : desktopWidth"
      ></mno-recommender-block>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT && index === 0">
      @if (layoutElement.extractorData; as data) {
        <app-podcast-box [articles]="data" [desktopWidth]="layoutType === LayoutPageTypes.SIDEBAR ? 1 : desktopWidth"> </app-podcast-box>
      }
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.HtmlEmbed">
      <kesma-html-embed *ngIf="layoutElement.config?.htmlContent as data" [data]="data"></kesma-html-embed>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.FreshNews">
      <app-fresh-news-adapter
        [autoFill]="layoutElement?.config?.autoFill"
        [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR || layoutElement?.columnCount < 4"
        [title]="layoutElement?.config?.blockHeaderTitle"
      >
      </app-fresh-news-adapter>
    </ng-container>

    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.MOST_VIEWED">
      <mno-most-read [data]="layoutElement.extractorData" [styleID]="layoutElement.styleId" [title]="layoutElement.blockTitle?.text ?? 'Legolvasottabb'">
      </mno-most-read>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.NEWS_FEED">
      <ng-container *ngIf="layoutElement.extractorData?.[0] as data">
        <mno-dossier-card
          [data]="data"
          [styleID]="DossierCardTypes.newsFeed"
          [title]="layoutElement.config.blockHeaderTitle"
          [showArticleLabels]="layoutType !== LayoutPageTypes.HOME"
        ></mno-dossier-card>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.MinuteToMinute">
      <ng-container *ngIf="layoutElement.extractorData?.[0] as data">
        <mno-dossier-card
          [data]="data"
          [showArticleLabels]="layoutType !== LayoutPageTypes.HOME"
          [styleID]="DossierCardTypes.minuteByMinute"
        ></mno-dossier-card>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.OPINION_NEWSLETTER">
      <mno-opinion-newsletter-box [infoLink]="opinionInfoLink" [styleId]="layoutElement.styleId"></mno-opinion-newsletter-box>
    </ng-container>
    <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarSearch">
      <kesma-real-estate-bazaar-search-block
        [defaultLocation]="layoutElement.config.defaultLocation"
        [defaultType]="layoutElement.config.defaultType"
        [showAdvertiseButton]="layoutElement.config.showAdvertiseButton"
        [showBudapestLocations]="layoutElement.config.showBudapestLocations"
        [showCountyLocations]="layoutElement.config.showBudapestLocations"
        [showNewBuildButton]="layoutElement.config.showNewBuildButton"
        [showOtherLocations]="layoutElement.config.showOtherLocations"
        [utmSource]="layoutElement.config.utmSource"
      ></kesma-real-estate-bazaar-search-block>
    </ng-container>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.IngatlanbazarConfigurable">
    <kesma-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [data]="realEstateData"
      [itemsToShow]="layoutElement.itemsToShow"
      [showHeader]="layoutElement.showHeader"
    ></kesma-real-estate-bazaar-block>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.INGATLANBAZAR">
    <kesma-real-estate-bazaar-block
      (initEvent)="handleRealEstateInitEvent()"
      [data]="realEstateData"
      [itemsToShow]="1"
      [showHeader]="true"
    ></kesma-real-estate-bazaar-block>
  </ng-container>
  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX">
    <kesma-sponsored-box [data]="layoutElement.config"></kesma-sponsored-box>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.BAYER_BLOG && isBayerBlogEnabled">
    <mno-bayer-blog-block
      *ngIf="layoutElement.extractorData as data"
      [authorData]="bayerZsolt$ | async"
      [data]="data"
      [desktopWidth]="desktopWidth"
      [isMobile]="isMobile$ | async"
      [isSidebar]="layoutType === LayoutPageTypes.SIDEBAR"
      [showOpinionLabels]="showLabels"
      [styleID]="layoutElement.styleId"
    ></mno-bayer-blog-block>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.DID_YOU_KNOW">
    <app-variable-sponsored-did-you-know-wrapper [id]="layoutElement.config?.selectedDidYouKnowBox?.[0]?.id"></app-variable-sponsored-did-you-know-wrapper>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.SponsoredQuiz">
    <ng-container *ngIf="layoutElement.extractorData as sponsoredQuizData">
      <app-sponsored-quiz [data]="sponsoredQuizData"> </app-sponsored-quiz>
    </ng-container>
  </ng-container>

  <ng-container *ngIf="layoutElement.contentType === LayoutElementContentType.WHERE_THE_BALL_WILL_BE">
    <app-where-the-ball-will-be *ngIf="layoutElement.extractorData?.[index] as data" [data]="data" [styleId]="'column-title-tag-lead-author'">
    </app-where-the-ball-will-be>
  </ng-container>
</ng-template>
