@use 'shared' as *;

:host {
  display: block;
}

/*
.col,
.col-1,
.col-2,
.col-3,
.col-4 {
  &:has(> [mno-article-card]) {
    display: flex;
  }
}*/

.col {
  //  display: flex;
  //  flex-direction: row;
  align-items: stretch;
  justify-content: stretch;
}

kesma-layout::ng-deep {
  .row {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    justify-content: stretch;
  }

  @for $i from 1 through 12 {
    .col-#{$i} {
      @extend .col;
    }
  }

  .horizontal {
    .column-element {
      height: 100%;
      /*      .content-element:only-child {
              height: 100%;
              > *:only-child {
                height: 100%;
              }
            }*/
    }
  }

  mno-dossier-card.smaller-thumbnail.style-recommendationDossier {
    .dossier-thumbnail-container {
      @include media-breakpoint-between(lg, xl) {
        height: auto;
        width: auto;
        min-width: unset;
        min-height: unset;
      }
    }
  }

  kesma-mme-branding-box {
    margin: 30px 0;

    @include media-breakpoint-down(md) {
      margin: 20px 0;
    }
  }
}
