@use '../../../../../scss/shared.scss' as *;

app-layout {
  width: 300px;
  margin-bottom: 20px;

  @include media-breakpoint-down(sm) {
    width: auto;
  }
}

:host {
  app-layout::ng-deep {
    kesma-layout::ng-deep {
      [mno-article-card].style-ImgRightTagsTitleBadgeSmall::ng-deep {
        .article {
          &-label,
          &-title {
            font-size: 16px !important;
          }

          &-label {
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}
