import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Params } from '@angular/router';
import { ApiResult, Article } from '@trendency/kesma-ui';
import { forkJoin, Observable, of, share, switchMap } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ArticleService } from '../../../shared';
import { NewsFeedResolverResult } from '../news-feed.definitions';
import { NewsFeedService } from './news-feed.service';

@Injectable({
  providedIn: 'root',
})
export class NewsFeedResolver {
  constructor(
    private readonly newsFeedService: NewsFeedService,
    private readonly articleService: ArticleService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<NewsFeedResolverResult> {
    const {
      params: { newsfeedSlug: slug },
    } = route as { params: { newsfeedSlug?: string } } & Params;
    const newsFeed$ = this.newsFeedService.getNewsFeed(slug as string).pipe(share());

    return forkJoin([
      newsFeed$,
      newsFeed$.pipe(
        switchMap((result: ApiResult<Article[]>) => {
          const latestArticleSlug = result?.data?.[0]?.slug;
          return this.articleService.getArticleRecommendations(latestArticleSlug as string).pipe(catchError(() => of({} as any)));
        })
      ),
      // relatedArticles: this.newsFeedService.getRelatedArticles(newsFeed$),
    ]).pipe(
      map(([result, recommendations]) => ({
        result,
        recommendations,
      }))
    );
  }
}
