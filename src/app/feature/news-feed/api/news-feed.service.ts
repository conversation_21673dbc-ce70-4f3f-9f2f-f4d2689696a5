import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { ApiResult, Article } from '@trendency/kesma-ui';
import { Router } from '@angular/router';
import { catchError, map, Observable, throwError } from 'rxjs';
import { NewsFeedApiResponseMeta } from '../news-feed.definitions';

@Injectable({
  providedIn: 'root',
})
export class NewsFeedService {
  constructor(
    private readonly router: Router,
    private readonly reqService: ReqService
  ) {}

  getNewsFeed(slug: string, pageLimit = 0): Observable<ApiResult<Article[], NewsFeedApiResponseMeta>> {
    const params: IHttpOptions = {
      params: {
        page_limit: pageLimit.toString(),
      },
    };

    return this.reqService.get<ApiResult<Article[], NewsFeedApiResponseMeta>>(`/content-group/news-feed/${slug}`, { ...params }).pipe(
      map(({ data, meta }) => {
        if (!data.length) {
          this.navigateTo404();
        }
        return { data, meta };
      }),
      catchError((error: HttpErrorResponse | Error) => {
        this.navigateTo404(error);
        return throwError(() => error);
      })
    );
  }

  navigateTo404(error?: HttpErrorResponse | Error): void {
    this.router
      .navigate(['/404'], {
        state: { errorResponse: JSON.stringify(error || {}) },
        skipLocationChange: true,
      })
      .then();
  }
}
