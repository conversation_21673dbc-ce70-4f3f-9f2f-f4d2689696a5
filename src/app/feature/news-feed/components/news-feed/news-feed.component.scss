@use '../../../../../scss/shared.scss' as *;

:host {
  display: block;
  max-width: 100%;

  > kesma-advertisement-adocean.fullwidth-ad-zone {
    margin-top: 26px;
  }
}

app-article-header::ng-deep {
  .publishdate {
    display: none !important;
  }
}

.newsfeed-top {
  border: 1px solid var(--kui-slate-300);
  padding: 8px;
  font-size: 16px;
  font-weight: 700;
  line-height: 21px; /* 131.25% */
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  border-radius: 2px;

  .newsfeed-top-date {
    border-left: 1px solid var(--kui-slate-300);
    padding-left: 16px;
  }
}

.article-block {
  padding-top: 21px;

  &.is-opinion {
    background-color: var(--kui-blue-50);
  }

  .wrapper {
    width: 100%;
    display: flex;
    max-width: $article-max-width;
    margin: 25px auto;
    gap: 32px;

    @include media-breakpoint-down(sm) {
      flex-wrap: wrap;
      max-width: 100vw;
    }

    .article {
      max-width: 100%;
      gap: 24px;

      @include media-breakpoint-down(sm) {
        width: 100%;
        max-width: 100vw;
        padding: 0 $mobile-side-padding;
      }

      mno-promo-block {
        margin: 12px 0;
      }
    }

    > .sidebar {
      width: 300px;
      min-width: 300px;

      @include media-breakpoint-down(sm) {
        margin: 0;
        width: 100%;
        max-width: calc(100vw - 24px);
        padding: 0 24px;
      }
    }
  }

  app-article-recommended {
    [mno-article-card] {
      .article-card {
        padding-top: 39px;
        padding-bottom: 39px;
        border-bottom: 1px solid rgba(var(--kui-slate-800), 0.2);
        border-top: 1px solid rgba(var(--kui-slate-800), 0.2);
      }
    }
  }
}

.newsfeed-article {
  &-container {
    display: flex;
    flex-direction: column;
    gap: 24px;

    border-right: 1px solid var(--kui-slate-950);
    border-bottom: 1px solid var(--kui-slate-950);
    padding-right: 24px;
    padding-bottom: 24px;
  }

  &-header {
    padding: 8px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--kui-slate-300);

    mno-opinion-author::ng-deep {
      .author-name {
        color: var(--kui-slate-950);
        font-size: 16px;
        font-weight: 500;
        line-height: 22px; /* 137.5% */
      }

      .author-avatar {
        width: 24px;
        height: 24px;
      }
    }
  }

  &-meta {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color: var(--Slate-950, #020617);
    gap: 4px;

    &-date {
      font-size: 14px;
      font-weight: 400;
      line-height: 18px; /* 128.571% */
      border-left: 1px solid var(--kui-slate-300);
      padding-left: 8px;
    }

    mno-tag-list::ng-deep {
      margin-bottom: 0;
    }
  }
}

.article-title {
  font-size: 20px;
  font-weight: 700;
  line-height: 26px; /* 130% */
  letter-spacing: 0.3px;
}

.article-lead {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0;
  text-align: left;
  margin: -8px 0 0 0;
  padding: 0;
}

.article-subtitle {
  color: var(--kui-slate-900);

  font-size: 16px;
  font-weight: 700;
  line-height: 21px; /* 131.25% */

  @include media-breakpoint-down(sm) {
    color: var(--kui-slate-950);
  }
}

.pager {
  display: block;
  margin-bottom: 50px;
}

.recommendation {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  width: 100%;
  gap: 16px;
  @include media-breakpoint-down(sm) {
    grid-template-columns: 1fr;
  }
}
