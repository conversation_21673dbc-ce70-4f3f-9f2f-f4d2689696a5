import { ApiResponseMetaList, ApiR<PERSON>ult, Article, ArticleCard, RecommendationsData } from '@trendency/kesma-ui';

export type NewsFeedDossier = Readonly<{
  slug: string;
  title: string;
  description: string;
  newsFeedDescription: string;
  newsFeedTitle: string;
  coverImage: NewsFeedDosserImage;
  createdAt: string;
}>;

export type NewsFeedDosserImage = Readonly<{
  caption?: string;
  photographer?: string;
  source?: string;
  thumbnail?: string;
}>;

export interface NewsFeedApiResponseMeta extends ApiResponseMetaList {
  newsFeed: NewsFeedDossier;
}

export interface NewsFeedRouteData {
  data: NewsFeedResolverResult;
  newsfeedSlug: string;
}

export type NewsFeedResolverResult = {
  result: ApiResult<Article[], NewsFeedApiResponseMeta>;
  recommendations: ApiResult<RecommendationsData>;
  relatedArticles?: Array<ArticleCard[]>;
};
