<section
  class="article-wrapper"
  *ngIf="styleID !== articleHeaderStyle.ArticleStyle; else articleStyle"
  [style.background-image]="'url(' + data?.thumbnail?.url + ')'"
>
  <span class="article-wrapper-block-category">SOROZATVETŐ</span>
  <div class="article-wrapper-block">
    <ng-container [ngTemplateOutlet]="articleHeader"></ng-container>
    <div class="article-wrapper-block-container">
      <div class="article-opinion" *ngIf="opinionCards">
        <app-sorozatveto-opinion-card-header [opinioners]="opinioners"></app-sorozatveto-opinion-card-header>
        <app-sorozatveto-opinion-card-body
          [opinionCard]="opinionCards[activeOpinionIndex]"
          [styleID]="opinionStyle.OpinionCardLayoutStyle"
        ></app-sorozatveto-opinion-card-body>
        <app-sorozatveto-opinion-card-footer [opinionCount]="(opinioners?.length || 1) - 1" [articleLink]="articleLink"></app-sorozatveto-opinion-card-footer>
        <a [routerLink]="articleLink" class="article-card-navigate-button"><i class="icon-right-layout-arrow article-card-navigate-button-icon"></i></a>
        <button class="article-opinion-btn previous" (click)="previous()">
          <i class="icon-left-arrow-blue"></i>
        </button>
        <button class="article-opinion-btn next" (click)="next()">
          <i class="icon-right-arrow-blue"></i>
        </button>
      </div>
      <ng-container *ngFor="let card of notHighlightedOpinions; let i = index">
        <app-sorozatveto-highlighted-opinion-card-shadow class="opinion-shadow" [index]="i + 1"></app-sorozatveto-highlighted-opinion-card-shadow>
      </ng-container>
    </div>
    <ng-container button *ngTemplateOutlet="moreOpinionButton; context: { showOnMobile: true }"></ng-container>
  </div>
</section>

<ng-template #articleStyle>
  <app-sorozatveto-article-card [sorozatvetoArticle]="data"></app-sorozatveto-article-card>
</ng-template>

<ng-template #articleHeader>
  <app-sorozatveto-article-header [sorozatvetoArticle]="data" [styleID]="0" [articleLink]="articleLink">
    <ng-container button *ngTemplateOutlet="moreOpinionButton; context: { $implicit: true }"></ng-container>
  </app-sorozatveto-article-header>
</ng-template>

<ng-template #moreOpinionButton let-hideOnMobile let-showOnMobile="showOnMobile">
  <button class="article-header-navigate-button" [class.hide-on-mobile]="hideOnMobile" [class.show-on-mobile]="showOnMobile" [routerLink]="articleLink">
    Tovább a véleménycikkre
  </button>
  <!-- TODO: navigation -->
</ng-template>
