import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { FormatDatePipe } from '@trendency/kesma-core';
import { ArticleReview, ArticleReviewBody, Author, SorozatvetoArticleCard, SorozatvetoOpinionCard } from '@trendency/kesma-ui';
import { Observable, shareReplay, Subject, takeUntil } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { ReviewService } from '../../shared';
import { SorozatvetoArticleHeaderEnum, SorozatVetoOpinionCardEnum } from './sorozatveto.definitions';
import { RouterLink } from '@angular/router';
import { NgFor, NgIf, NgTemplateOutlet } from '@angular/common';
import { SorozatvetoOpinionCardHeaderComponent } from './components/sorozatveto-opinion-card-header/sorozatveto-opinion-card-header.component';
import { SorozatvetoOpinionCardBodyComponent } from './components/sorozatveto-opinion-card-body/sorozatveto-opinion-card-body.component';
import { SorozatvetoOpinionCardFooterComponent } from './components/sorozatveto-opinion-card-footer/sorozatveto-opinion-card-footer.component';
import { SorozatvetoHighlightedOpinionCardShadowComponent } from './components/sorozatveto-highlighted-opinion-card-shadow/sorozatveto-highlighted-opinion-card-shadow.component';
import { SorozatvetoArticleCardComponent } from './components/sorozatveto-article-card/sorozatveto-article-card.component';
import { SorozatvetoArticleHeaderComponent } from './components/sorozatveto-article-header/sorozatveto-article-header.component';

@Component({
  selector: 'app-sorozatveto',
  templateUrl: './sorozatveto.component.html',
  styleUrls: ['./sorozatveto.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    RouterLink,
    NgTemplateOutlet,
    NgIf,
    NgFor,
    SorozatvetoOpinionCardHeaderComponent,
    SorozatvetoOpinionCardBodyComponent,
    SorozatvetoOpinionCardFooterComponent,
    SorozatvetoHighlightedOpinionCardShadowComponent,
    SorozatvetoArticleCardComponent,
    SorozatvetoArticleHeaderComponent,
  ],
})
export class SorozatvetoComponent implements OnInit, OnDestroy {
  @Input() data: SorozatvetoArticleCard;

  opinionCards: SorozatvetoOpinionCard<ArticleReviewBody[]>[];
  activeOpinionIndex = 0;
  articleHeaderStyle = SorozatvetoArticleHeaderEnum;

  opinionStyle = SorozatVetoOpinionCardEnum;
  getAllReviews$: Observable<ArticleReview[]>;
  private readonly destroy$ = new Subject<void>();
  #styleID: SorozatvetoArticleHeaderEnum;

  constructor(
    private readonly reviewService: ReviewService,
    private readonly cd: ChangeDetectorRef,
    private readonly formatDate: FormatDatePipe
  ) {}

  get styleID(): SorozatvetoArticleHeaderEnum {
    return this.#styleID;
  }

  @Input() set styleID(styleID: SorozatvetoArticleHeaderEnum) {
    this.#styleID = styleID;
  }

  get notHighlightedOpinions(): SorozatvetoOpinionCard<ArticleReviewBody[]>[] {
    return this.opinionCards?.length <= 3 ? this.opinionCards : this.opinionCards?.slice(0, 3);
  }

  get opinioners(): Author[] {
    return this.opinionCards?.map((opinion) => opinion?.author) || [];
  }

  get articleLink(): string | string[] {
    return [
      '/',
      (this.data.columnSlug || this.data?.category?.slug || this.data?.brandingType) as string,
      this.formatDate.transform(this.data?.publishDate as Date, 'year') ?? '0',
      this.formatDate.transform(this.data?.publishDate as Date, 'month') ?? '0',
      this.data.slug ?? '',
    ];
  }

  ngOnInit(): void {
    this.getAllReviews$ = this.reviewService
      .getArticleReviews(this.data.id as string)
      .pipe(shareReplay({ bufferSize: 1, refCount: true }), takeUntil(this.destroy$))
      .pipe(map(({ data }) => data));
    this.updateOpinionCardsWithAllReviews();
  }

  next(): void {
    this.activeOpinionIndex = this.opinionCards.length > this.activeOpinionIndex + 1 ? this.activeOpinionIndex + 1 : 0;
    this.cd.markForCheck();
  }

  previous(): void {
    this.activeOpinionIndex = 0 <= this.activeOpinionIndex - 1 ? this.activeOpinionIndex - 1 : this.opinionCards.length - 1;
    this.cd.markForCheck();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  updateOpinionCardsWithAllReviews(): void {
    this.getAllReviews$.pipe(take(1)).subscribe((reviews) => {
      this.opinionCards = (reviews ?? [])
        .filter((r) => !!r)
        .map((review: ArticleReview) => ({
          ...review,
          author: {
            name: /*review.author?.name ||*/ review.publicAuthorName || review.publicAuthor?.authorData?.fullName || '',
            avatar: review.publicAuthor?.avatarFullSizeUrl, // review.author?.avatar,
            avatarUrl:
              /*review.author?.avatarUrl ||*/
              review.publicAuthorAvatarThumbnailUrl || review.publicAuthor?.avatarThumbnail || review.publicAuthor?.avatarFullSizeUrl,
          },
          lead: review.excerpt || review.lead || review.title,
          body: this.styleID === SorozatvetoArticleHeaderEnum.LayoutStyle ? [] : review.body,
        }));

      this.activeOpinionIndex = this.opinionCards?.findIndex((opinion) => opinion?.id == this.data?.opinionCards?.[0]?.id);

      this.cd.markForCheck();
    });
  }
}
