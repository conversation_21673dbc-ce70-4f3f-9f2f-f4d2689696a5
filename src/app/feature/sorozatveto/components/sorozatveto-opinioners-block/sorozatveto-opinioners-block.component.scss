@use 'shared' as *;

:host {
  display: block;

  @include media-breakpoint-down(xs) {
    app-sorozatveto-article-block-title {
      max-width: 250px;
      margin: 0 auto;
    }
  }
}

.opinioners-container {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  column-gap: 64px;
  row-gap: 32px;

  @include media-breakpoint-down(sm) {
    grid-template-columns: repeat(2, 1fr);
    justify-content: center;
    column-gap: 32px;
  }
}

.opinioner-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 16px;
  text-align: center;

  &-avatar {
    border-radius: 50%;
    width: 80px;
    aspect-ratio: 1;
  }

  &-name {
    color: var(--kui-orange-600);
    font-family: var(--kui-font-primary);
    font-size: 12px;
    font-weight: 700;
    line-height: 150%;
    letter-spacing: 0.72px;
  }
}
