import { NgFor } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { Author } from '@trendency/kesma-ui';
import { SorozatvetoArticleBlockTitleComponent } from '../sorozatveto-article-block-title/sorozatveto-article-block-title.component';

@Component({
  selector: 'app-sorozatveto-opinioners-block',
  templateUrl: './sorozatveto-opinioners-block.component.html',
  styleUrls: ['./sorozatveto-opinioners-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, RouterLink, SorozatvetoArticleBlockTitleComponent],
})
export class SorozatvetoOpinionersBlockComponent {
  @Input() set opinioners(opinioners: Author[]) {
    this._opinioners = opinioners;
  }

  get opinioners(): Author[] {
    return this._opinioners;
  }

  private _opinioners: Author[];
}
