import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { BreakpointObserver } from '@angular/cdk/layout';
import { map, Observable, tap } from 'rxjs';
import { Author } from '@trendency/kesma-ui';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-sorozatveto-opinion-card-header',
  templateUrl: './sorozatveto-opinion-card-header.component.html',
  styleUrls: ['./sorozatveto-opinion-card-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, Ng<PERSON><PERSON>, <PERSON>F<PERSON>, NgTemplateOutlet],
})
export class SorozatvetoOpinionCardHeaderComponent {
  isMobile = false;
  mobileWidthMatched$: Observable<boolean> = this.breakpointObserver.observe('(max-width: 768px)').pipe(
    map((bs) => bs.matches),
    tap((value: boolean) => {
      this.isMobile = value;
    })
  );

  constructor(private readonly breakpointObserver: BreakpointObserver) {}

  @HostBinding('class') headerContainer = 'header-container';

  @Input() set opinioners(opinioners: Author[]) {
    this._opinioners = opinioners;
  }

  get opinioners(): Author[] {
    return this._opinioners ?? [];
  }

  private _opinioners: Author[];

  isDropdownOpened = false;

  get slicedOpinioners(): Author[] {
    if (this.opinioners.length >= 4 && !this.isMobile) {
      return this.opinioners.slice(0, 4);
    } else if (this.opinioners.length >= 3 && this.isMobile) {
      return this.opinioners.slice(0, 3);
    } else {
      return this.opinioners;
    }
  }

  get opinionerList(): Author[] {
    return this.opinioners;
  }

  openDropDown(): void {
    this.isDropdownOpened = !this.isDropdownOpened;
  }
}
