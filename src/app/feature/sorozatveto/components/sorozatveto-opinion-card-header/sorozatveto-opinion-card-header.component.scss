@use 'shared' as *;

:host {
  display: block;

  &.header-container {
    background-color: var(--kui-blue-400);
    text-align: center;
  }

  .pre-title {
    font-size: 14px;
    letter-spacing: 0.84px;

    @include media-breakpoint-down(sm) {
      font-size: 12px;
      letter-spacing: 0.72px;
    }
  }
}

.opinion-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  position: relative;
  height: 60px;

  &-container {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &-title {
    color: var(--kui-white);
    font-weight: 700;
    font-size: 12px;
    font-family: var(--kui-font-primary);
    letter-spacing: 0.72px;
    line-height: 170%;
  }

  &-image {
    height: 24px;
    width: 24px;
    object-fit: cover;
    border-radius: 24px;

    @include media-breakpoint-down(sm) {
      height: 20px;
      width: 20px;
    }
  }

  &-icon {
    height: 24px;
    width: 24px;
    cursor: pointer;
  }
}

.opinioners-dropdown-content {
  display: none;
  background-color: var(--kui-white);
  max-height: 220px;
  overflow: auto;
  position: absolute;
  top: 60px;
  left: 0;
  right: 0;
  z-index: 3;

  &.opened {
    display: block;
  }

  &-item {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 12px 20px;

    &-name {
      font-size: 14px;
      font-weight: 700;
      line-height: 150%;
      font-family: var(--kui-font-primary);

      @include media-breakpoint-down(sm) {
        font-size: 12px;
      }
    }
  }

  &-image {
    height: 34px;
    width: 34px;
    margin-right: 8px;
    border-radius: 24px;
  }
}
