<ng-container *ngIf="mobileWidthMatched$ | async"></ng-container>
<div class="opinion-card-header">
  <div class="opinion-card-header-container">
    <span class="opinion-card-header-title pre-title">Véleményezők: </span>
    <ng-container *ngIf="!isDropdownOpened">
      <ng-container *ngFor="let opinioner of slicedOpinioners">
        <ng-container
          [ngTemplateOutletContext]="{ $implicit: { opinioner: this.opinioner, inDropdown: false } }"
          [ngTemplateOutlet]="opinionerThumbnail"
        ></ng-container>
      </ng-container>
      <ng-container *ngIf="opinioners.length > 4 && !isMobile">
        <span class="opinion-card-header-title"> +{{ opinioners.slice(4).length }}</span>
      </ng-container>
      <ng-container *ngIf="opinioners.length > 3 && isMobile">
        <span class="opinion-card-header-title"> +{{ opinioners.slice(3).length }}</span>
      </ng-container>
    </ng-container>
  </div>

  <button (click)="openDropDown()" type="button">
    <i [class.icon-dropdown-close]="isDropdownOpened" [class.icon-dropdown-open]="!isDropdownOpened" class="opinion-card-header-icon"></i>
  </button>
  <div [class.opened]="isDropdownOpened" class="opinioners-dropdown-content">
    <ng-container *ngFor="let opinioner of opinionerList">
      <ng-container [ngTemplateOutletContext]="{ $implicit: { opinioner: this.opinioner } }" [ngTemplateOutlet]="opinionerTemp"></ng-container>
    </ng-container>
  </div>
</div>

<ng-template #opinionerTemp let-element>
  <div class="opinioners-dropdown-content-item">
    <ng-container
      [ngTemplateOutletContext]="{ $implicit: { opinioner: element?.opinioner, inDropdown: true } }"
      [ngTemplateOutlet]="opinionerThumbnail"
    ></ng-container>
    <span class="opinioners-dropdown-content-item-name">{{ element?.opinioner?.name | uppercase }} </span>
    <!-- TODO: navigation -->
  </div>
</ng-template>

<ng-template #opinionerThumbnail let-element>
  <img
    [alt]="element?.opinioner?.name ?? ''"
    [class.opinion-card-header-image]="!element?.inDropdown"
    [class.opinioners-dropdown-content-image]="element?.inDropdown"
    [src]="element?.opinioner?.avatarUrl ?? 'assets/images/icons/avatar.svg'"
    loading="lazy"
  />
</ng-template>
