import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-sorozatveto-opinion-card-footer',
  templateUrl: './sorozatveto-opinion-card-footer.component.html',
  styleUrls: ['./sorozatveto-opinion-card-footer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink],
})
export class SorozatvetoOpinionCardFooterComponent {
  @HostBinding('class') footerContainer = 'footer-container';
  @Input() opinionCount?: number;
  @Input() articleLink: string | string[];
}
