@use 'shared' as *;

:host {
  display: flex;
  align-items: center;
}

.shadow-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;

  @include media-breakpoint-down(sm) {
    width: 15px;
  }

  &-header {
    @extend %shadow-style;
  }

  &-footer {
    @extend %shadow-style;
  }

  &-body {
    background-color: var(--kui-white);
    height: 100%;
  }
}

%shadow-style {
  background-color: var(--kui-blue-400);
  height: 60px;
}
