import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

@Component({
  selector: 'app-sorozatveto-highlighted-opinion-card-shadow',
  templateUrl: './sorozatveto-highlighted-opinion-card-shadow.component.html',
  styleUrls: ['./sorozatveto-highlighted-opinion-card-shadow.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SorozatvetoHighlightedOpinionCardShadowComponent {
  opacity?: number;
  height?: string;
  blur?: string;

  @Input() set index(i: number) {
    this._opinionIndex = i;

    this.height = i ? `${i * 60}px` : '0px';
    this.blur = i ? `${i * 5}px` : '0px';
    this.opacity = i ? 1 - +`0.${i}` : 1; // TODO: Change it if you have better solution
  }

  get index(): number {
    return this._opinionIndex;
  }

  private _opinionIndex: number;
}
