<ng-container *ngFor="let social of socialLinks">
  <ng-container [ngTemplateOutlet]="socialIcon" [ngTemplateOutletContext]="{ $implicit: { social } }"></ng-container>
</ng-container>

<ng-template #socialIcon let-element>
  <ng-container [ngSwitch]="element?.social">
    <ng-container *ngSwitchCase="socialEnum.FACEBOOK">
      <ng-container [ngTemplateOutlet]="socialElement" [ngTemplateOutletContext]="{ social: { type: element?.social, link: facebookLink } }"></ng-container>
    </ng-container>
  </ng-container>
</ng-template>

<ng-template #socialElement let-social="social">
  <ng-container *ngIf="social?.link; else noLink">
    <a class="social-element" [class.blue]="isArticle$ | async" [href]="social?.link" target="_blank"
      ><ng-container [ngTemplateOutlet]="icon" [ngTemplateOutletContext]="{ $implicit: social }"></ng-container
    ></a>
  </ng-container>
  <ng-template #noLink>
    <button class="social-element" [class.blue]="isArticle$ | async" type="button" (click)="copyToClickBoard()">
      <ng-container [ngTemplateOutlet]="icon" [ngTemplateOutletContext]="{ $implicit: social }"></ng-container>
    </button>
  </ng-template>
</ng-template>

<ng-template #icon let-element>
  <ng-container *ngIf="isArticle$ | async; else noArticlePage">
    <i class="social-icon icon-{{ element.type }}-white"></i>
  </ng-container>
  <ng-template #noArticlePage>
    <i class="social-icon icon-{{ element.type }}"></i>
  </ng-template>
</ng-template>
