import { ChangeDetectionStrategy, Component, inject, Input } from '@angular/core';
import { SorozatvetoSocialEnum } from '../../sorozatveto.definitions';
import { Clipboard } from '@angular/cdk/clipboard';
import { SharedService } from 'src/app/shared/services/shared.service';
import { UtilService } from '@trendency/kesma-core';
import { Async<PERSON>ipe, <PERSON>F<PERSON>, NgIf, NgSwitch, NgSwitchCase, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-sorozatveto-social-icons',
  templateUrl: './sorozatveto-social-icons.component.html',
  styleUrls: ['./sorozatveto-social-icons.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, NgIf, NgFor, NgTemplateOutlet, NgSwitch, NgSwitchCase],
})
export class SorozatvetoSocialIconsComponent {
  @Input() facebookLink = '';
  socialLinks = [SorozatvetoSocialEnum.LINK, SorozatvetoSocialEnum.FACEBOOK];
  socialEnum = SorozatvetoSocialEnum;
  isArticle$ = this.sharedService.isArticlePageObs$;
  private readonly utils = inject(UtilService);

  constructor(
    private readonly clipboard: Clipboard,
    private readonly sharedService: SharedService
  ) {}

  copyToClickBoard(): void {
    if (this.utils.isBrowser()) {
      this.clipboard.copy(window.location.href);
    }
  }
}
