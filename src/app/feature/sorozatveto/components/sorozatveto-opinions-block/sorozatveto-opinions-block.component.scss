@use 'shared' as *;

:host {
  display: block;

  app-sorozatveto-article-block-title {
    text-align: left;
  }

  ::ng-deep {
    swiper-slide {
      padding: 0 5px;
    }

    @include media-breakpoint-down(sm) {
      .article-block-title {
        &:after {
          content: ':';
        }
      }
    }
  }
}

.opinions-container {
  padding-bottom: 20px;
  column-count: 3;

  @include media-breakpoint-down(md) {
    column-count: 2;
  }

  app-sorozatveto-opinion-card-body {
    break-inside: avoid-column;
  }

  @include media-breakpoint-down(xs) {
    display: none;
  }
}

.opinions-carousel {
  width: calc(100% + 30px);
  height: 100%;
  position: relative;
  display: none;
  margin: 0 -15px;

  @include media-breakpoint-down(xs) {
    display: block;
  }

  &-button {
    height: 40px;
    width: 40px;
    background: var(--kui-blue-700);
    position: absolute;
    top: 45%;
    z-index: 2;

    &.left {
      left: -10px;

      @include media-breakpoint-down(sm) {
        left: 5px;
      }
    }

    &.right {
      right: -10px;

      @include media-breakpoint-down(sm) {
        right: 5px;
      }
    }
  }

  &-icon {
    height: 32px;
    width: 32px;
  }
}
