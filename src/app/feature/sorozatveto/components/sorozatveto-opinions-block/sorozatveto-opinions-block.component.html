<app-sorozatveto-article-block-title [blockTitle]="'Összes idézet'"></app-sorozatveto-article-block-title>

<div class="opinions-container">
  <ng-container *ngFor="let opinion of sorozatvetoOpinions">
    <app-sorozatveto-opinion-card-body [opinionCard]="opinion" [styleID]="1"></app-sorozatveto-opinion-card-body>
  </ng-container>
</div>

<div class="opinions-carousel">
  <button *ngIf="!firstSlide" class="opinions-carousel-button left" (click)="changeCurrentSlide('prev')">
    <i class="icon-left-arrow opinions-carousel-icon"></i>
  </button>
  <swiper-container #swiper slides-per-view="1.2" speed="1000" centered-slides="true" allow-touch-move="false">
    <swiper-slide *ngFor="let opinion of sorozatvetoOpinions">
      <app-sorozatveto-opinion-card-body [opinionCard]="opinion" [styleID]="1"></app-sorozatveto-opinion-card-body>
    </swiper-slide>
  </swiper-container>
  <button *ngIf="!lastSlide" class="opinions-carousel-button right" (click)="changeCurrentSlide('next')">
    <i class="icon-right-arrow opinions-carousel-icon"></i>
  </button>
</div>
