import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, Input } from '@angular/core';
import { ArticleReviewBody, SorozatvetoOpinionCard, SwiperBaseComponent } from '@trendency/kesma-ui';
import { SorozatvetoArticleBlockTitleComponent } from '../sorozatveto-article-block-title/sorozatveto-article-block-title.component';
import { SorozatvetoOpinionCardBodyComponent } from '../sorozatveto-opinion-card-body/sorozatveto-opinion-card-body.component';
import { NgForOf, NgIf } from '@angular/common';

@Component({
  selector: 'app-sorozatveto-opinions-block',
  templateUrl: './sorozatveto-opinions-block.component.html',
  styleUrls: ['./sorozatveto-opinions-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SorozatvetoArticleBlockTitleComponent, SorozatvetoOpinionCardBodyComponent, NgForOf, NgIf],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SorozatvetoOpinionsBlockComponent extends SwiperBaseComponent<any> {
  slideIndex = 0;
  firstSlide = true;
  lastSlide = this.sorozatvetoOpinions?.length <= 1;
  #sorozatvetoOpinions: SorozatvetoOpinionCard<ArticleReviewBody[]>[];

  get sorozatvetoOpinions(): SorozatvetoOpinionCard<ArticleReviewBody[]>[] {
    return this.#sorozatvetoOpinions;
  }

  @Input() set sorozatvetoOpinions(opinionCards: SorozatvetoOpinionCard<ArticleReviewBody[]>[]) {
    this.#sorozatvetoOpinions = opinionCards;
  }

  changeCurrentSlide(direction: string): void {
    this.lastSlide = false;
    this.firstSlide = false;

    if (direction === 'prev') {
      this.swipePrev();
      this.slideIndex--;

      if (!this.sorozatvetoOpinions[this.slideIndex - 1]) {
        this.firstSlide = true;
      }
    } else {
      this.swipeNext();
      this.slideIndex++;

      if (!this.sorozatvetoOpinions[this.slideIndex + 1]) {
        this.lastSlide = true;
      }
    }
  }
}
