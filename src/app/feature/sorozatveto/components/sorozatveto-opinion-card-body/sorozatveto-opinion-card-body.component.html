<section class="opinion-card-body">
  <img
    class="opinion-card-body-thumbnail"
    [src]="sorozatVetoOpinionCard?.author?.avatarUrl || '/assets/images/mn-avatar.png'"
    [alt]="sorozatVetoOpinionCard?.author?.name ?? ''"
    loading="lazy"
  />
  <span class="opinion-card-body-title">{{ sorozatVetoOpinionCard?.author?.name | uppercase }}</span>
  <section class="opinion-card-body-lead-container">
    <mno-wysiwyg-box *ngIf="sorozatVetoOpinionCard?.body; else lead" [htmlArray]="blocks" [html]="block"></mno-wysiwyg-box>
    <ng-template #lead>
      <span class="lead">{{ sorozatVetoOpinionCard?.lead }}</span>
    </ng-template>
  </section>
</section>
