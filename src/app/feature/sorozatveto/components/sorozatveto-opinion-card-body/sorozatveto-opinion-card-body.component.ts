import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { SorozatVetoOpinionCardEnum } from '../../sorozatveto.definitions';
import { SorozatvetoOpinionCard } from '@trendency/kesma-ui';
import { NgIf, UpperCasePipe } from '@angular/common';
import { WysiwygBoxComponent } from '../../../../shared';

@Component({
  selector: 'app-sorozatveto-opinion-card-body',
  templateUrl: './sorozatveto-opinion-card-body.component.html',
  styleUrls: ['./sorozatveto-opinion-card-body.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [UpperCasePipe, WysiwygBoxComponent, NgIf],
})
export class SorozatvetoOpinionCardBodyComponent {
  @HostBinding('class') hostClass = '';

  blocks: string[] = [];
  block = '';
  #opinioncard: SorozatvetoOpinionCard;
  #styleID: SorozatVetoOpinionCardEnum;

  @Input() set opinionCard(opinionCard: SorozatvetoOpinionCard) {
    this.#opinioncard = opinionCard;
    const bodyParts = (Array.isArray(opinionCard.body) ? opinionCard.body : []).map(({ details }) => details.map(({ value }) => value));
    this.blocks = ([] as string[]).concat(...bodyParts);
    this.block = typeof opinionCard.body === 'string' ? opinionCard.body : '';
  }

  get styleID(): SorozatVetoOpinionCardEnum {
    return this.#styleID;
  }

  @Input() set styleID(styleID: SorozatVetoOpinionCardEnum) {
    this.#styleID = styleID;
    this.hostClass = SorozatVetoOpinionCardEnum[styleID];
  }

  get sorozatVetoOpinionCard(): SorozatvetoOpinionCard {
    return this.#opinioncard;
  }
}
