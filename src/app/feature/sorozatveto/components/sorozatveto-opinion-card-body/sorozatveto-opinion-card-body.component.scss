@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  justify-content: space-between;
  background-color: var(--kui-white);

  .lead {
    font-size: 14px;
    font-weight: 400;
    line-height: 160%;
    letter-spacing: 0.84px;

    @include media-breakpoint-down(sm) {
      font-size: 12px;
      line-height: 140%;
      letter-spacing: 0.72px;
    }
  }

  &.OpinionCardLayoutStyle {
    .opinion-card-body {
      padding: 24px 28px;

      &-thumbnail {
        @extend %opinion-card-body-thumbnail;
        height: 48px;
        width: 48px;
      }

      &-title {
        @extend %opinion-card-body-title;
      }

      &-lead-container {
        @extend %opinion-body-lead-container;
        font-size: 12px;
        line-height: 16.8px;
        letter-spacing: 1px;
      }

      &-lead {
        @extend %opinion-card-body-lead;
        font-size: 14px;

        @include media-breakpoint-down(sm) {
          font-size: 12px;
        }
      }

      &-social-container {
        @extend %opinion-card-body-social-container;
        padding: 15px 35px;
        background-color: var(--kui-gray-800);
      }
    }
  }

  &.OpinionCardArticleStyle {
    padding: 16px;
    margin-bottom: 20px;
    box-shadow: 0 14px 14px 0 var(--kui-slate-950);

    .opinion-card-body {
      &-thumbnail {
        @extend %opinion-card-body-thumbnail;
        height: 36px;
        width: 36px;
        object-fit: cover;
      }

      &-title {
        @extend %opinion-card-body-title;
      }

      &-lead-container {
        @extend %opinion-body-lead-container;
      }

      &-lead {
        @extend %opinion-card-body-lead;
        font-size: 12px;
      }

      &-social-container {
        @extend %opinion-card-body-social-container;
        padding: 15px 0;
        gap: 16px;
      }

      &-social-icon {
        height: 14px;
        width: 14px;
        margin-right: 5px;
      }
    }
  }
}

.opinion-card-body {
  &-action {
    font-size: 10px;
    font-weight: 400;
    font-family: var(--kui-font-primary);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-action-likes-count {
    font-size: 10px;
    font-weight: 700;
    font-family: var(--kui-font-primary);
    margin-right: 5px;
  }
}

%opinion-card-body-thumbnail {
  border-radius: 50%;
  margin-right: 12px;
  object-fit: cover;

  @include media-breakpoint-down(sm) {
    height: 36px;
    width: 36px;
    margin-right: 8px;
  }
}

%opinion-card-body-title {
  font-size: 16px;
  font-family: var(--kui-font-primary);
  color: var(--kui-orange-600);
  font-weight: 700;
  letter-spacing: 0.96px;
}

%opinion-body-lead-container {
  margin-top: 12px;
  padding: 0 8px;
}

%opinion-card-body-lead {
  font-weight: 400;
  line-height: 160%;
  font-family: var(--kui-font-primary);
  padding-bottom: 28px;
}

%opinion-card-body-social-container {
  display: flex;
  justify-content: space-between;
}
