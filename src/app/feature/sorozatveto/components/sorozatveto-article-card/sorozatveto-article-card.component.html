<ng-container *ngIf="sorozatvetoArticle">
  <app-sorozatveto-article-header [styleID]="1" [sorozatvetoArticle]="sorozatvetoArticle"></app-sorozatveto-article-header>
  <ng-container [ngTemplateOutlet]="bodyContent" [ngTemplateOutletContext]="{ body: sorozatvetoArticle?.body }"></ng-container>
  <app-sorozatveto-opinions-block [sorozatvetoOpinions]="sorozatvetoArticle?.reviews ?? []"></app-sorozatveto-opinions-block>
  <app-sorozatveto-opinioners-block [opinioners]="sorozatvetoArticle?.opinioners ?? []"></app-sorozatveto-opinioners-block>
  <ng-container [ngTemplateOutlet]="articleTags"></ng-container>
</ng-container>

<ng-template #articleTags>
  <section class="article-tag">
    <ng-container *ngFor="let tag of sorozatvetoArticle?.tags">
      <a [routerLink]="['/', 'cimke', tag?.slug]" class="article-tag-title">
        {{ tag.title | uppercase }}
      </a>
    </ng-container>
  </section>
</ng-template>

<ng-template #bodyContent let-body="body">
  <ng-container *ngFor="let element of body">
    <ng-container [ngSwitch]="element.type">
      <app-article-text *ngSwitchCase="'Basic.Wysiwyg.Wysiwyg'" [data]="element"></app-article-text>
    </ng-container>
  </ng-container>
</ng-template>
