@use 'shared' as *;

:host {
  display: block;
  justify-content: space-between;

  ::ng-deep {
    .article-text-formatter {
      p {
        font-size: 14px;
        font-weight: 400;
        line-height: 170%;
        letter-spacing: 0.84px;
      }
    }
  }
}

app-sorozatveto-opinioners-block,
app-sorozatveto-opinions-block {
  margin-top: 30px;
}

.article-tag {
  display: flex;
  flex-direction: row;
  padding: 48px 0;
  flex-wrap: wrap;
  gap: 12px;

  &-title {
    padding: 4px 8px;
    background: var(--kui-gray-800);
    color: var(--kui-black);
    font-family: var(--kui-font-primary);
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.84px;
  }
}
