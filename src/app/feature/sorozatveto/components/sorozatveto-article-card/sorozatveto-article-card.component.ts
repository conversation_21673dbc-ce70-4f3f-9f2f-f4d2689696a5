import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet, UpperCasePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { Author, SorozatvetoArticleCard } from '@trendency/kesma-ui';
import { SorozatvetoArticleHeaderComponent } from '../sorozatveto-article-header/sorozatveto-article-header.component';
import { SorozatvetoOpinionsBlockComponent } from '../sorozatveto-opinions-block/sorozatveto-opinions-block.component';
import { ArticleTextComponent } from 'src/app/shared';
import { SorozatvetoOpinionersBlockComponent } from '../sorozatveto-opinioners-block/sorozatveto-opinioners-block.component';

@Component({
  selector: 'app-sorozatveto-article-card',
  templateUrl: './sorozatveto-article-card.component.html',
  styleUrls: ['./sorozatveto-article-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    NgFor,
    RouterLink,
    NgTemplateOutlet,
    SorozatvetoArticleHeaderComponent,
    SorozatvetoOpinionsBlockComponent,
    ArticleTextComponent,
    NgSwitch,
    NgSwitchCase,
    SorozatvetoOpinionersBlockComponent,
    UpperCasePipe,
  ],
})
export class SorozatvetoArticleCardComponent {
  #sorozatvetoArticle: SorozatvetoArticleCard;

  get sorozatvetoArticle(): SorozatvetoArticleCard {
    return this.#sorozatvetoArticle;
  }

  @Input() set sorozatvetoArticle(sorozatvetoArticle: SorozatvetoArticleCard) {
    this.#sorozatvetoArticle = sorozatvetoArticle;
  }

  get opinioners(): Author[] {
    return this.#sorozatvetoArticle?.opinionCards?.map((opinion) => opinion?.author);
  }
}
