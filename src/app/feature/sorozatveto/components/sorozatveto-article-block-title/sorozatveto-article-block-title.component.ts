import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

@Component({
  selector: 'app-sorozatveto-article-block-title',
  templateUrl: './sorozatveto-article-block-title.component.html',
  styleUrls: ['./sorozatveto-article-block-title.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SorozatvetoArticleBlockTitleComponent {
  @Input() blockTitle?: string;
}
