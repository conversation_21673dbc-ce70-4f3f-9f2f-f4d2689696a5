import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { SorozatvetoArticleHeaderEnum } from '../../sorozatveto.definitions';
import { SorozatvetoArticleCard } from '@trendency/kesma-ui';
import { NgIf, NgSwitch, NgSwitchCase, NgTemplateOutlet } from '@angular/common';
import { SorozatvetoSocialIconsComponent } from '../sorozatveto-social-icons/sorozatveto-social-icons.component';

@Component({
  selector: 'app-sorozatveto-article-header',
  templateUrl: './sorozatveto-article-header.component.html',
  styleUrls: ['./sorozatveto-article-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgSwitch, NgSwitchCase, NgTemplateOutlet, NgIf, SorozatvetoSocialIconsComponent],
})
export class SorozatvetoArticleHeaderComponent {
  articleHeaderEnum = SorozatvetoArticleHeaderEnum;
  facebookLink = 'https://www.facebook.com/magyarnemzet.hu';

  @HostBinding('class') hostClass = '';

  @Input() set styleID(styleID: SorozatvetoArticleHeaderEnum) {
    this.#styleID = styleID;
    this.hostClass = `article-${SorozatvetoArticleHeaderEnum[styleID]}`;
  }

  get styleID(): SorozatvetoArticleHeaderEnum {
    return this.#styleID as SorozatvetoArticleHeaderEnum;
  }

  @Input() set sorozatvetoArticle(article: SorozatvetoArticleCard) {
    this.#sorozatvetoArticle = article;
  }

  @Input() articleLink: string | string[];

  get article(): SorozatvetoArticleCard {
    return this.#sorozatvetoArticle;
  }

  #styleID: SorozatvetoArticleHeaderEnum;
  #sorozatvetoArticle: SorozatvetoArticleCard;
}
