<ng-container [ngSwitch]="styleID">
  <ng-container *ngSwitchCase="articleHeaderEnum.LayoutStyle">
    <ng-container [ngTemplateOutlet]="articleHeading"></ng-container>
    <ng-container [ngTemplateOutlet]="articleAuthorAndSocial"></ng-container>
    <ng-container [ngTemplateOutlet]="articleLead"></ng-container>
    <ng-content select="[button]"></ng-content>
  </ng-container>
  <ng-container *ngSwitchCase="articleHeaderEnum.ArticleStyle">
    <ng-container [ngTemplateOutlet]="articleHeading"></ng-container>
    <ng-container [ngTemplateOutlet]="articleAuthorAndSocial"></ng-container>
    <ng-container [ngTemplateOutlet]="articleLead"></ng-container>
  </ng-container>
</ng-container>

<ng-template #articleHeading>
  <div
    *ngIf="styleID === articleHeaderEnum.ArticleStyle; else noImage"
    class="article-header-title-wrapper"
    [style.background-image]="'url(' + article?.thumbnail + ')'"
  >
    <h2 class="article-header-title">{{ article?.title | uppercase }}</h2>
  </div>
  <ng-template #noImage>
    <h2 class="article-header-title">{{ article?.title | uppercase }}</h2>
  </ng-template>
</ng-template>

<ng-template #articleAuthorAndSocial>
  <section class="article-header-social-container">
    <div class="article-header-author-container">
      <span class="article-header-author">{{ article?.publicAuthor || article?.author?.name | uppercase }}</span>
      <span class="article-header-publish-date" *ngIf="styleID === articleHeaderEnum.ArticleStyle">
        {{ article?.publishDate | date: 'yyyy. MM. dd. HH:mm' }}
      </span>
    </div>
    <app-sorozatveto-social-icons [facebookLink]="facebookLink"></app-sorozatveto-social-icons>
  </section>
</ng-template>

<ng-template #articleLead>
  <p class="article-header-lead">{{ article?.onlineLead || article?.lead }}</p>
</ng-template>
