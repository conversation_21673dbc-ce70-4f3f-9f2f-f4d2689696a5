@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .opinion-shadow {
    @include media-breakpoint-down(sm) {
      &:last-of-type {
        display: none;
      }
    }
  }

  .hide-on-mobile {
    @include media-breakpoint-down(sm) {
      display: none;
    }
  }

  .show-on-mobile {
    display: none;
    @include media-breakpoint-down(sm) {
      display: block;
    }
  }

  .article-header-navigate-button {
    padding: 14px 28px;
    color: var(--kui-white);
    background-color: var(--kui-orange-600);
    font-size: 16px;
    font-weight: 700;
    margin-top: 40px;
    letter-spacing: 0.56px;

    @include media-breakpoint-down(xs) {
      width: 100%;
      margin-top: 15px;
      letter-spacing: 0.64px;
    }
  }
}

.article-wrapper {
  padding: 32px 64px;
  background: rgba(4, 11, 17, 0.85) no-repeat;
  background-blend-mode: multiply;
  background-size: cover;
  display: flex;
  flex-direction: column;

  @include media-breakpoint-down(sm) {
    padding: 32px 15px;
  }

  &-block {
    display: flex;
    flex-direction: row;
    gap: 50px;

    @include media-breakpoint-down(sm) {
      flex-direction: column;
    }
  }

  &-block-container {
    display: flex;
    flex-direction: row;
  }

  &-block-category {
    font-size: 15px;
    font-weight: 600;
    font-family: var(--kui-font-primary);
    color: var(--kui-white);
    margin-bottom: 55px;
    display: block;

    @include media-breakpoint-down(sm) {
      margin-bottom: 25px;
      font-size: 18px;
    }
  }
}

.article-opinion {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 325px;
  min-height: 400px;

  @include media-breakpoint-down(sm) {
    width: 100%;
  }

  &-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;

    &.previous {
      left: 0;
    }

    &.next {
      right: 0;
    }

    i {
      width: 32px;
      height: 32px;
    }
  }
}

.article-card-navigate-button {
  position: absolute;
  display: flex;
  width: 66px;
  height: 66px;
  padding: 22px;
  background: var(--kui-blue-500);
  z-index: 2;
  right: -80px;
  top: 45%;

  @include media-breakpoint-down(sm) {
    display: none;
  }

  &-icon {
    height: 24px;
    width: 24px;
  }
}
