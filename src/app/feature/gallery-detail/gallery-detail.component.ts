import { ChangeDetectionStrategy, Component, inject, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { map, tap } from 'rxjs/operators';
import { FullscreenDirective, IMetaData, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import { Gallery, GalleryData, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { defaultMetaInfo, UrlService } from '../../shared';
import { AsyncPipe, DOCUMENT, NgIf } from '@angular/common';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { ArticleAdultComponent } from '../article/components/article-adult/article-adult.component';
import { SliderGalleryComponent } from 'src/app/shared';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-gallery-detail',
  templateUrl: './gallery-detail.component.html',
  styleUrls: ['./gallery-detail.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, NgIf, ArticleAdultComponent, SliderGalleryComponent, FullscreenDirective],
})
export class GalleryDetailComponent implements OnInit, OnDestroy {
  isFullScreen = false;
  gallery: GalleryData;
  selectedImageIndex = 0;
  referrerArticleUrl?: string;
  firstSlide = true;
  isUserAdultChoice: boolean;
  galleryData$ = this.activatedRoute.data.pipe(
    map(({ gallery: { gallery } }) => gallery),
    tap((gallery: GalleryData) => {
      this.isUserAdultChoice = this.isUserAdultChoiceFromStorage;
      this.gallery = gallery;
      this.setMetaData();
    })
  );
  readonly MOBILE_BREAKPOINT = '(max-width: 767.98px)';
  isMobile$ = this.breakpointObserver.observe([this.MOBILE_BREAKPOINT]).pipe(map((state: BreakpointState) => state.matches));

  readonly document = inject(DOCUMENT);
  readonly urlService = inject(UrlService);

  previousUrl = toSignal(this.urlService.previousUrl$);

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly seo: SeoService,
    private readonly utils: UtilService,
    private readonly storage: StorageService,
    private readonly breakpointObserver: BreakpointObserver
  ) {}

  get isUserAdultChoiceFromStorage(): boolean {
    return this.storage.getSessionStorageData('isAdultChoice', false) ?? false;
  }

  ngOnInit(): void {
    const galleryIndex = this.activatedRoute.snapshot.params['index'];
    this.selectedImageIndex = !isNaN(galleryIndex) ? parseInt(galleryIndex) - 1 : 0;
    if (this.utils.isBrowser()) {
      const referrerArticle = history?.state?.referrerArticle;
      if (referrerArticle) {
        this.referrerArticleUrl = referrerArticle;
      }

      this.document.body.style.overflow = 'hidden';
    }
  }

  onUserAdultChoose(isAdult: boolean): void {
    this.isUserAdultChoice = isAdult;
  }

  navigateToGalleryPage(): void {
    this.isFullScreen = false;
    const navigationUrl = this.referrerArticleUrl || this.previousUrl() || '/';
    this.router.navigateByUrl(navigationUrl);
  }

  onToggleFullScreen(fullScreenDirective: FullscreenDirective): void {
    this.isFullScreen = !this.isFullScreen;
    fullScreenDirective.toggleFullScreen();
  }

  handleSelectedIndexChange(index: number): void {
    this.router.navigate(['/', 'galeria', this.gallery?.slug, index + 1]);
  }

  ngOnDestroy(): void {
    if (this.utils.isBrowser()) {
      this.document.body.style.overflow = 'auto';
    }
  }

  private setMetaData(): void {
    const title = `${this.gallery.title} | Magyar Nemzet`;
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
      ogImage: (this.gallery as unknown as Gallery)?.highlightedImage?.url,
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('galeria', this.activatedRoute.snapshot);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
  }
}
