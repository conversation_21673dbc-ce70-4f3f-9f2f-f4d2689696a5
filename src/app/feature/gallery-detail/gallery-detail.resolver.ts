import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, of, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { GalleryService } from '../../shared';
import { GalleryDetailResponse } from '../galleries/gallery.definitions';

@Injectable({
  providedIn: 'root',
})
export class GalleryDetailResolver {
  constructor(
    private readonly router: Router,
    private readonly galleryService: GalleryService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<GalleryDetailResponse> {
    const slug = route.params['gallerySlug'];

    return forkJoin([
      this.galleryService.getGalleryRecommendationDatas(slug).pipe(
        catchError((_) => {
          return of({ data: [] });
        })
      ),
      this.galleryService.getGalleryDetails(slug),
    ] as [any, any]).pipe(
      map(([recommendations, gallery]: any[]) => ({ recommendations, gallery })),
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });
        return throwError(error);
      })
    );
  }
}
