import { Routes } from '@angular/router';
import { GalleryDetailComponent } from './gallery-detail.component';
import { GalleryDetailResolver } from './gallery-detail.resolver';

export const GALLERY_DETAIL_ROUTES: Routes = [
  {
    path: '',
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    component: GalleryDetailComponent,
    providers: [GalleryDetailResolver],
    data: {
      customAnalyticsPageType: 'Galéria',
    },
    resolve: { gallery: GalleryDetailResolver },
  },
  {
    path: ':index',
    component: GalleryDetailComponent,
    providers: [GalleryDetailResolver],
    data: {
      customAnalyticsPageType: 'Galéria',
    },
    resolve: { gallery: GalleryDetailResolver },
  },
];
