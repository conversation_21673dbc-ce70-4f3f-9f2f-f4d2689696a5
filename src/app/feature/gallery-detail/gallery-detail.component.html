<ng-container *ngIf="galleryData$ | async as gallery">
  <ng-container *ngIf="!isUserAdultChoice && gallery?.isAdult; else galleryContent">
    <app-adult (isUserAdult)="onUserAdultChoose($event)"></app-adult>
  </ng-container>

  <ng-template #galleryContent>
    <section #fullScreen="trFullscreen" class="gallery-container" trFullscreen>
      <mno-slider-gallery
        (onCloseGallery)="navigateToGalleryPage()"
        (onFullSizeGallery)="onToggleFullScreen(fullScreen)"
        (onSelectedIndexChanged)="handleSelectedIndexChange($event)"
        [data]="gallery"
        [fullscreenGallery]="true"
        [isInsideAdultArticleBody]="gallery?.isAdult"
        [isMobile]="isMobile$ | async"
        [selectedImageIndex]="selectedImageIndex"
      ></mno-slider-gallery>
    </section>
  </ng-template>
</ng-container>
