import { Routes } from '@angular/router';
import { OlympicsAthleteProfileComponent } from './components/olympics-athlete-profile/olympics-athlete-profile.component';
import { olympicsAthleteProfileResolver } from './resolvers/olympics-athlete-profile.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const OLYMPICS_ATHLETE_PROFILE_ROUTES: Routes = [
  {
    path: '',
    component: OlympicsAthleteProfileComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    resolve: { data: olympicsAthleteProfileResolver },
    canActivate: [PageValidatorGuard],
  },
];
