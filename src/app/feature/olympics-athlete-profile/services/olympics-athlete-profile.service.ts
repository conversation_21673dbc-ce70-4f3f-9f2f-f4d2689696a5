import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { ApiResult, OlimpiaHungarianTeam } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class OlympicsAthleteProfileService {
  constructor(private readonly reqService: ReqService) {}

  getAthleteProfile(participantSlug: string): Observable<ApiResult<OlimpiaHungarianTeam>> {
    return this.reqService.get(`/sport/olympics/participant/${participantSlug}`);
  }
}
