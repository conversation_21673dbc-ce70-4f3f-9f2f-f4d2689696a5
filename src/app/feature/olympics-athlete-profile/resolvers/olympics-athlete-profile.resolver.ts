import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { forkJoin, share, take, throwError } from 'rxjs';
import { OlympicsAthleteProfileService } from '../services/olympics-athlete-profile.service';
import { ApiService, OlympicImportantSidebarService } from '../../../shared';
import { BackendArticleSearchResult, Layout, LimitableMeta, OlimpiaHungarianTeam, RedirectService, Tag } from '@trendency/kesma-ui';

interface ResolveData {
  data: {
    articles: { data: BackendArticleSearchResult[]; meta: LimitableMeta };
    athleteProfile: OlimpiaHungarianTeam;
  };
  olympicImportantSidebar: Layout | null;
}

export const olympicsAthleteProfileResolver: ResolveFn<ResolveData> = (route: ActivatedRouteSnapshot) => {
  const athleteSlug = route.params['athleteSlug'];
  const router = inject(Router);
  const apiService = inject(ApiService);
  const redirectService = inject(RedirectService);

  return forkJoin({
    data: inject(OlympicsAthleteProfileService)
      .getAthleteProfile(athleteSlug)
      .pipe(
        map(({ data }) => data),
        share(),
        switchMap((member: OlimpiaHungarianTeam) => {
          const page = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;
          const tagSlugs = member?.tags?.map((tag: Tag) => tag?.slug) || [];

          if (!tagSlugs.includes('olimpia-2024')) {
            tagSlugs.push('olimpia-2024');
          }

          return apiService
            .searchByKeyword(page, 5, {
              'tagSlugs[]': tagSlugs,
            })
            .pipe(
              tap(({ data }) => {
                if (redirectService.shouldBeRedirect(page, data)) {
                  redirectService.redirectOldUrl(`olimpia-2024/${athleteSlug}`, false, 302);
                }
              }),
              map(({ data, meta }) => ({ articles: { data: data, meta: meta.limitable }, athleteProfile: member }))
            );
        }),
        catchError((err) => {
          router.navigate(['/404'], { skipLocationChange: true }).then();
          return throwError(() => err);
        })
      ),
    olympicImportantSidebar: inject(OlympicImportantSidebarService).getLayout().pipe(take(1)),
  });
};
