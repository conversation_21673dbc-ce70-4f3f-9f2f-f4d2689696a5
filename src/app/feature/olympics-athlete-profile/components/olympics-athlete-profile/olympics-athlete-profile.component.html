<section>
  <div class="wrapper">
    <app-olympics-header></app-olympics-header>
  </div>
  <div class="wrapper with-aside main-content">
    <div class="left-column">
      <div>
        <ng-container [ngTemplateOutlet]="olimpiaPageBannerTemplate"></ng-container>
        <app-olympics-athlete-profile-card [data]="athleteProfile"></app-olympics-athlete-profile-card>
      </div>

      <div class="athlete-video">
        <kesma-article-video [data]="athleteVideo" *ngIf="athleteProfile?.videoUrl"></kesma-article-video>
      </div>

      <div class="related-articles">
        <div class="related-articles-title">Még több Olimpia</div>

        @for (article of articles; track article.id) {
          <article
            mno-article-card
            [data]="article"
            [styleID]="(isMobile$ | async) ? ArticleCardType.ImgRightTagsTitleLeadBadge : ArticleCardType.DateImgRightTagsTitleLeadWide"
            [asResult]="!!(isMobile$ | async)"
          ></article>
        }

        <mno-pager
          *ngIf="limitable?.pageMax! > 0"
          [rowAllCount]="limitable?.rowAllCount!"
          [rowOnPageCount]="limitable?.rowOnPageCount!"
          [isListPager]="true"
          [hasFirstLastButton]="false"
          [hasSkipButton]="true"
          [allowAutoScrollToTop]="true"
          [maxDisplayedPages]="3"
        >
        </mno-pager>
      </div>

      <div class="navigators">
        <ng-container [ngTemplateOutlet]="olimpiaPageBannerTemplate"></ng-container>
        <kesma-olimpia-navigator [styleID]="OlimpicPortalEnum.OlimpicMNO"></kesma-olimpia-navigator>
      </div>
    </div>

    <aside>
      <app-layout
        *ngIf="olympicImportantSidebarLayout$ | async as layout"
        [adPageType]="adPageType"
        [configuration]="layout.content"
        [layoutType]="LayoutPageType.SIDEBAR"
        [structure]="layout.struct"
        class="olympic-important-sidebar"
        secondary_pageType="column_sport_all_articles_and_sub_pages"
      ></app-layout>
    </aside>
  </div>

  <div class="ads">
    <kesma-advertisement-adocean
      *ngIf="adverts?.desktop?.roadblock_3 as ad"
      [ad]="ad"
      [style]="{
        margin: 'var(--ad-margin)',
        background: 'var(--ad-bg)',
        padding: 'var(--ad-padding)',
      }"
    >
    </kesma-advertisement-adocean>

    <kesma-advertisement-adocean
      *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
      [style]="{
        margin: 'var(--ad-margin)',
        background: 'var(--ad-bg)',
        padding: 'var(--ad-padding)',
      }"
      [ad]="ad"
    >
    </kesma-advertisement-adocean>
  </div>
</section>

<ng-template #olimpiaPageBannerTemplate>
  <kesma-olimpia-page-banner [link]="'/olimpia-2024/magyar-csapat'">
    <div class="olimpia-page-banner">
      <img src="assets/images/hun-flag2.svg" alt="Magyar csapat logó" class="hun-flag" />
      <span>Magyar olimpiai csapat</span>
      <kesma-icon name="icon-olimpia-chevron-right-mno" [size]="20"></kesma-icon>
    </div>
  </kesma-olimpia-page-banner>
</ng-template>
