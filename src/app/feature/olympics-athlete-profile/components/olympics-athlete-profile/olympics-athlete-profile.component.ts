import { ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, inject, OnInit } from '@angular/core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ArticleCard,
  ArticleVideoComponent,
  IconComponent,
  Layout,
  LayoutPageType,
  LimitableMeta,
  OlimpiaHungarianTeam,
  OlimpiaNavigatorComponent,
  OlimpiaPageBannerComponent,
  OlimpicPortalEnum,
  PAGE_TYPES,
  VideoComponentObject,
} from '@trendency/kesma-ui';
import { ActivatedRoute } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { tap } from 'rxjs/operators';
import { map, Observable } from 'rxjs';
import { ConfigService } from '../../../../shared';
import { OlympicsHeaderComponent } from 'src/app/shared/components/olympics-header/olympics-header.component';
import { ArticleCardComponent, ArticleCardType, PagerComponent } from 'src/app/shared';
import { LayoutComponent } from 'src/app/feature/layout/components/layout/layout.component';
import { AsyncPipe, NgIf, NgTemplateOutlet } from '@angular/common';
import { OlympicsAthleteProfileCardComponent } from '../olympics-athlete-profile-card/olympics-athlete-profile-card.component';

@Component({
  selector: 'app-sport-olympics-athlete-profile',
  templateUrl: './olympics-athlete-profile.component.html',
  styleUrl: './olympics-athlete-profile.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    OlympicsHeaderComponent,
    ArticleVideoComponent,
    ArticleCardComponent,
    PagerComponent,
    OlimpiaNavigatorComponent,
    IconComponent,
    AdvertisementAdoceanComponent,
    LayoutComponent,
    OlimpiaNavigatorComponent,
    AsyncPipe,
    NgIf,
    NgTemplateOutlet,
    OlympicsAthleteProfileCardComponent,
    OlimpiaPageBannerComponent,
  ],
})
export class OlympicsAthleteProfileComponent implements OnInit {
  athleteProfile: OlimpiaHungarianTeam;
  articles: ArticleCard[];
  limitable?: LimitableMeta;
  adverts?: AdvertisementsByMedium;
  adPageType = PAGE_TYPES.column_sport_all_articles_and_sub_pages;
  isMobile$: Observable<boolean>;
  athleteVideo: VideoComponentObject;
  olympicImportantSidebarLayout$: Observable<Layout> = this.route.data.pipe(map(({ data }) => data['olympicImportantSidebar']));
  readonly ArticleCardType = ArticleCardType;
  readonly OlimpicPortalEnum = OlimpicPortalEnum;
  protected readonly LayoutPageType = LayoutPageType;
  private readonly destroyRef = inject(DestroyRef);

  constructor(
    private readonly route: ActivatedRoute,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly cdr: ChangeDetectorRef,
    private readonly configService: ConfigService
  ) {}

  ngOnInit(): void {
    this.isMobile$ = this.configService.isMobile$;

    this.route.data
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        tap(() => this.initAds())
      )
      .subscribe(({ data }) => {
        const athleteData = data['data'];
        this.athleteProfile = athleteData['athleteProfile'];
        this.athleteVideo = {
          id: '',
          isActive: true,
          isDeleted: false,
          isPublic: true,
          lead: '',
          slug: '',
          title: '',
          videaUrl: this.athleteProfile?.videoUrl || '',
        };
        this.articles = athleteData['articles']['data'];
        this.limitable = athleteData['articles']['meta'];

        this.cdr.markForCheck();
      });
  }

  protected initAds(): void {
    this.resetAds();
    this.adStore.advertisemenets$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads, this.adPageType);

      this.cdr.detectChanges();
    });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }
}
