@use '../../../../../scss/shared.scss' as *;

:host {
  display: block;

  ::ng-deep {
    .content-element {
      margin-bottom: 0 !important;
    }
  }
  .wrapper {
    gap: 24px;
    @include media-breakpoint-down(md) {
      padding: 0 $mobile-side-padding;
    }
  }

  .main-content {
    margin-top: 0;
    .left-column {
      margin-top: 32px;
    }
  }

  .left-column {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  kesma-olimpia-page-banner {
    ::ng-deep {
      .page-banner {
        padding: 12px 8px 12px 20px;
      }

      .link {
        &,
        &:hover {
          border-bottom: none !important;
        }
      }
    }
    .olimpia-page-banner {
      display: flex;
      gap: 4px;
      align-items: center;

      @include media-breakpoint-down(sm) {
        gap: 0;
      }

      .hun-flag {
        width: 32px;
        height: 24px;
        border-radius: 4px;
        border: 2px solid var(--kui-white);

        @include media-breakpoint-down(sm) {
          width: 24px;
          height: 18px;
          border-radius: 3px;
          margin-right: 4px;
        }
      }

      span {
        font-size: 16px;
        font-weight: 700;
        line-height: 20px;
        letter-spacing: 0.16px;
        color: #fffefe;

        @include media-breakpoint-down(sm) {
          line-height: 21px;
        }
      }

      kesma-icon {
        color: var(--kui-white);
      }
    }
  }

  kesma-article-video {
    max-width: 596px;
    display: block;
    margin: 24px auto;

    @include media-breakpoint-down(sm) {
      margin: 16px auto;
    }
  }

  .related-articles {
    display: flex;
    flex-direction: column;
    gap: 24px;

    @include media-breakpoint-down(sm) {
      margin-bottom: 24px;
      gap: 16px;
    }

    &-title {
      color: #143d5d;
      font-size: 20px;
      font-weight: 700;
      line-height: 24px;
      margin-bottom: 16px;
      letter-spacing: 0.3px;

      @include media-breakpoint-down(sm) {
        line-height: 26px;
        margin-bottom: -8px;
      }
    }

    article {
      padding-bottom: 8px;
      border-bottom: 1px dashed #cbd5e1;
    }

    mno-pager {
      margin-top: 16px;
    }
  }

  .navigators {
    display: flex;
    flex-direction: column;
    gap: 8px;

    @include media-breakpoint-down(sm) {
      gap: 4px;
    }

    kesma-olimpia-page-banner {
      &::ng-deep {
        .olimpia-logo {
          display: none;
        }
      }
    }
  }
}
