import { NgIf, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, OlimpiaHungarianTeam } from '@trendency/kesma-ui';

@Component({
  selector: 'app-olympics-athlete-profile-card',
  templateUrl: './olympics-athlete-profile-card.component.html',
  styleUrl: './olympics-athlete-profile-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgTemplateOutlet, NgIf],
})
export class OlympicsAthleteProfileCardComponent extends BaseComponent<OlimpiaHungarianTeam> {}
