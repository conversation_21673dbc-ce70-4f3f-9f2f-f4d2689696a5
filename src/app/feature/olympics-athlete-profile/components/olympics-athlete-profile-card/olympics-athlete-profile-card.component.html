<div class="athlete-profile">
  <div class="left-side">
    <img
      [src]="data?.avatar?.thumbnailUrl || 'assets/images/placeholder.svg'"
      [class.placeholder]="!data?.avatar?.thumbnailUrl"
      [alt]="data?.avatar?.altText"
      class="athlete-profile-img"
    />
    <h2 class="athlete-profile-name mobile">{{ data?.name }}</h2>
  </div>

  <div class="right-side">
    <h2 class="athlete-profile-name desktop">{{ data?.name }}</h2>

    <div class="athlete-profile-details" *ngIf="data?.sports?.[0] || data?.competitionRaces">
      <ng-container
        [ngTemplateOutlet]="detailTmp"
        [ngTemplateOutletContext]="{ label: 'sportág:', data: data?.sports?.[0], isHighlighted: true, isMain: true }"
      ></ng-container>
      <ng-container
        [ngTemplateOutlet]="detailTmp"
        [ngTemplateOutletContext]="{ label: 'versenyszám:', data: data?.competitionRaces, isHighlighted: true }"
      ></ng-container>
    </div>

    <div class="athlete-profile-details" *ngIf="data?.age || data?.club || data?.coach">
      <ng-container [ngTemplateOutlet]="detailTmp" [ngTemplateOutletContext]="{ label: 'életkor:', data: data?.age }"></ng-container>
      <ng-container [ngTemplateOutlet]="detailTmp" [ngTemplateOutletContext]="{ label: 'klub:', data: data?.club }"></ng-container>
      <ng-container [ngTemplateOutlet]="detailTmp" [ngTemplateOutletContext]="{ label: 'edző:', data: data?.coach }"></ng-container>
    </div>

    <div class="athlete-profile-details" *ngIf="data?.results">
      <ng-container [ngTemplateOutlet]="detailTmp" [ngTemplateOutletContext]="{ label: 'eredmények:', data: data?.results }"></ng-container>
    </div>
  </div>
</div>

<ng-template #detailTmp let-label="label" let-data="data" let-isHighlighted="isHighlighted" let-isMain="isMain">
  <div class="athlete-profile-detail" *ngIf="data">
    <div class="athlete-profile-detail-label">{{ label }}</div>
    <div class="athlete-profile-detail-value" [class.highlighted]="isHighlighted" [class.main]="isMain">{{ data }}</div>
  </div>
</ng-template>
