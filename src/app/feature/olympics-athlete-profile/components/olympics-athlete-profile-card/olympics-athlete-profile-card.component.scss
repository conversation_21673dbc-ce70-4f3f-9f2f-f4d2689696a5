@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  border-bottom: 2px solid #74d3cf;

  @include media-breakpoint-down(sm) {
    border: none;
  }

  .athlete-profile {
    display: flex;
    align-items: center;
    gap: 16px;

    @include media-breakpoint-down(lg) {
      flex-direction: column;
      align-items: flex-start;
    }

    &-img {
      width: 300px;
      height: 300px;
      object-fit: cover;

      &.placeholder {
        @include media-breakpoint-down(lg) {
          object-fit: contain;
        }
      }

      @include media-breakpoint-down(sm) {
        width: 160px;
        height: 160px;
      }
    }

    .left-side {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 0 0 auto;

      @include media-breakpoint-down(sm) {
        width: 100%;
        border-bottom: 2px solid #74d3cf;
      }
    }

    &-name {
      color: #2d2c3a;
      font-size: 26px;
      font-weight: 700;
      line-height: 32px;

      @include media-breakpoint-down(sm) {
        font-size: 18px;
        line-height: 24px;
      }

      &.mobile {
        @include media-breakpoint-up(md) {
          display: none;
        }
      }

      &.desktop {
        @include media-breakpoint-down(sm) {
          display: none;
        }
      }
    }

    .right-side {
      display: flex;
      flex-direction: column;
      gap: 16px;

      @include media-breakpoint-down(sm) {
        gap: 8px;
      }
    }

    &-details {
      display: flex;
      gap: 2px;
      flex-direction: column;
    }

    &-detail {
      display: flex;
      align-items: center;
      gap: 16px;

      &-label {
        width: 96px;
        text-align: right;
        color: #6f6d95;
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        flex: 0 0 auto;

        @include media-breakpoint-down(sm) {
          width: 152px;
        }
      }

      &-value {
        color: #2d2c3a;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;

        &.highlighted {
          color: #0c7c84;
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;

          &.main {
            font-weight: 600;
          }
        }
      }
    }
  }
}
