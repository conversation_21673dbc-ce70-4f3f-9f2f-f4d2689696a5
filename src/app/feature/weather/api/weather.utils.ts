import { BackendWeatherForecast, WeatherForecast } from '@trendency/kesma-ui';

export const FILTERED_CITIES = ['Győr', 'Budapest', 'Miskolc', 'Debrecen', 'Pécs', 'Szeged', 'Zalaegerszeg'];

const WEATHER_ICON_MAP = {
  valtozoan_felhos: 'clouds',
  zapor: 'cloud-rain',
  eso: 'cloud-hail',
  derult: 'sunny',
  borult: 'clouds',
  hozapor: 'cloud-snow',
  havazas: 'cloud-snow',
  kod: 'cloud-hurricane',
  felhoszakadas: 'cloud-hail',
  havas_eso: 'cloud-hail',
  onos_eso: 'cloud-hail',
  hofuvas: 'cloud-snow',
  szitalas: 'cloud-rain',
  hoszallingozas: 'cloud-snow',
  kodszitalas: 'cloud-hurricane',
  szeles: 'wind-sun',
  kanikula: 'sunny',
  zivatar: 'cloud-rain',
  jegeso: 'cloud-hail',
  vihar: 'cloud-thunder-heavy',
  fagy: 'snowflake',
} as const;

export const translateWeatherIcon = (filename: string): string => {
  const iconName = filename.replace(/^([\w_]+)_(ejjel|nappal)\.svg$/, '$1');
  return WEATHER_ICON_MAP[iconName as keyof typeof WEATHER_ICON_MAP];
};

export const backendWeatherForecastToWeatherForecast = (forecast: BackendWeatherForecast): WeatherForecast =>
  forecast && {
    ...forecast,
    rain: parseInt(forecast.rain, 10),
    wind: parseInt(forecast.wind, 10),
    minTemperature: parseInt(forecast.minTemperature, 10),
    maxTemperature: parseInt(forecast.maxTemperature, 10),
    date: new Date(forecast.date),
    icon2: translateWeatherIcon(forecast.icon2),
  };
