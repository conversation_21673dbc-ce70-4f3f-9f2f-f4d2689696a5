import { ChangeDetectionStrategy, Component, computed, inject, OnInit, signal } from '@angular/core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  ApiListResult,
  BackendArticle,
  CityWeatherCurrent,
  createCanonicalUrlForPageablePage,
  IconComponent,
  KesmaSwipeComponent,
  NEWSLETTER_COMPONENT_TYPE,
  SwipeBreakpoints,
  WeatherCity,
  WeatherDailyShort,
  WeatherData,
  WeatherForecast,
  WeatherUv,
} from '@trendency/kesma-ui';
import { SeoService } from '@trendency/kesma-core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import {
  ApiService,
  ArticleCardComponent,
  ArticleCardType,
  ConfigService,
  defaultMetaInfo,
  ExternalRecommendationsComponent,
  PromoBlockComponent,
} from 'src/app/shared';
import { toSignal } from '@angular/core/rxjs-interop';
import { catchError, map } from 'rxjs/operators';
import { of, take } from 'rxjs';
import { FILTERED_CITIES } from './api/weather.utils';
import { MNOWeatherData } from './api/weather.definitions';
import { DecimalPipe, SlicePipe } from '@angular/common';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { DateFnsModule } from 'ngx-date-fns';
import { FormsModule } from '@angular/forms';
import { WeatherHeroComponent } from './components/weather-hero/weather-hero.component';

@Component({
  selector: 'app-weather',
  imports: [
    IconComponent,
    KesmaSwipeComponent,
    SlicePipe,
    ArticleCardComponent,
    SidebarComponent,
    DateFnsModule,
    FormsModule,
    WeatherHeroComponent,
    DecimalPipe,
    RouterLink,
    PromoBlockComponent,
    AdvertisementAdoceanComponent,
    ExternalRecommendationsComponent,
  ],
  templateUrl: './weather.component.html',
  styleUrl: './weather.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WeatherComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);
  private readonly apiService = inject(ApiService);
  private readonly analyticsService = inject(AnalyticsService);
  private readonly advertisementAdoceanStoreService = inject(AdvertisementAdoceanStoreService);
  private readonly configService = inject(ConfigService);

  readonly recommendedArticleStyle = ArticleCardType.Img4TopTagsTitleLeadSmallBorder;
  readonly recommendedArticleStyleMobile = ArticleCardType.ImgRightTagsTitleLeadWide;

  readonly isMobile = toSignal(this.configService.isMobile$);

  readonly socialInfo = {
    facebookLink: 'https://www.facebook.com/magyarnemzet.hu',
    instagramLink: 'https://www.instagram.com/magyarnemzet.hu/?hl=en',
    youtubeLink: 'https://www.youtube.com/@magyarnemzet_hivatalos',
    twitterLink: 'https://twitter.com/MagyarNemzetOn',
    videaLink: 'https://videa.hu/csatornak/magyar-nemzet-404',
  };

  readonly citiesForSelect: WeatherCity[] = [
    'Budapest',
    'Debrecen',
    'Eger',
    'Győr',
    'Kaposvár',
    'Kecskemét',
    'Miskolc',
    'Nyíregyháza',
    'Pécs',
    'Salgótarján',
    'Szeged',
    'Székesfehérvár',
    'Szekszárd',
    'Szolnok',
    'Szombathely',
    'Tatabánya',
    'Veszprém',
    'Zalaegerszeg',
  ];

  readonly breakpoints: SwipeBreakpoints = {
    default: {
      itemCount: 2,
      gap: '16px',
    },
    400: {
      itemCount: 3,
    },
    600: {
      itemCount: 4,
    },
  };

  readonly selectedCity = signal<WeatherCity>('Budapest');

  readonly adverts = signal<AdvertisementsByMedium | undefined>(undefined);

  readonly mapCities = computed<CityWeatherCurrent[]>(() => {
    return this.weatherData()?.current?.filter(({ city }) => FILTERED_CITIES.includes(city)) as CityWeatherCurrent[];
  });
  readonly selectedWeather = computed<WeatherData>(() => {
    const currentWeather = this.weatherData()?.current.find(({ city }) => this.selectedCity() === city);
    const forecast = this.weatherData()?.forecast[this.selectedCity()];
    const uv = this.weatherData()?.uv.find(({ city }) => this.selectedCity() === city);
    const text = this.weatherData()?.text;
    return {
      current: currentWeather as CityWeatherCurrent,
      forecast: forecast as WeatherForecast[],
      text: text as WeatherDailyShort[],
      uv: uv as WeatherUv,
    };
  });
  readonly selectableCities = computed(() => {
    return this.citiesForSelect.filter((cityName) => this.weatherData()?.current?.some(({ city }) => cityName === city));
  });

  readonly weatherData = toSignal<MNOWeatherData>(this.route.data.pipe(map(({ data }) => data)));
  readonly weatherArticles = toSignal(
    this.apiService.getCategoryArticles('idojaras', 0, 4).pipe(catchError(() => of({ data: [] } as unknown as ApiListResult<BackendArticle>)))
  );

  ngOnInit(): void {
    const canonical = createCanonicalUrlForPageablePage('idojaras');
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = 'Időjárás | Magyar Nemzet';
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    });
    this.initAds();
  }

  onSubscribe(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.ARTICLE_END);
  }

  protected initAds(): void {
    this.advertisementAdoceanStoreService.advertisemenets$
      .pipe(take(1))
      .subscribe((ads: Advertisement[]) => this.adverts.set(this.advertisementAdoceanStoreService.separateAdsByMedium(ads)));
  }
}
