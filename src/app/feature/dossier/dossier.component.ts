import { ChangeDetectionStrategy, Component, computed, inject, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AnalyticsService,
  ApiListResult,
  BasicDossier,
  DossierCardTypes,
  NEWSLETTER_COMPONENT_TYPE,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import { ConfigService, defaultMetaInfo, DOSSIERS_PER_PAGE } from '../../shared';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { BlockTitleRowComponent, DossierCardComponent, PagerComponent } from 'src/app/shared';
import { SlicePipe } from '@angular/common';
import { PageNewsletterBannerComponent } from 'src/app/shared/components/page-newsletter-banner/page-newsletter-banner.component';

@Component({
  selector: 'app-dossier',
  templateUrl: './dossier.component.html',
  styleUrls: ['./dossier.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AdvertisementAdoceanComponent,
    SidebarComponent,
    PagerComponent,
    SlicePipe,
    BlockTitleRowComponent,
    DossierCardComponent,
    PageNewsletterBannerComponent,
  ],
})
export class DossierComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly seo = inject(SeoService);
  private readonly adStore = inject(AdvertisementAdoceanStoreService);

  private readonly analyticsService = inject(AnalyticsService);
  private readonly configService = inject(ConfigService);
  readonly DossierCardTypes = DossierCardTypes;
  readonly DOSSIERS_PER_PAGE = DOSSIERS_PER_PAGE;

  private readonly data = toSignal<ApiListResult<BasicDossier>>(
    this.route.data.pipe(
      map(({ data }) => data as ApiListResult<BasicDossier>),
      takeUntilDestroyed()
    )
  );
  readonly adverts = toSignal(
    this.adStore.advertisemenets$.pipe(
      map((ads) => this.adStore.separateAdsByMedium(ads)),
      takeUntilDestroyed()
    )
  );
  readonly isMobile = toSignal(this.configService.isMobile$);

  readonly titleRow = { text: 'Aktuális témák' };

  readonly rowAllCount = computed(() => this.data()?.meta?.limitable?.rowAllCount ?? 0);
  readonly dossierTop = computed(() => this.data()?.data[0]);
  readonly dossiersBottom = computed(
    () =>
      this.data()
        ?.data?.slice(1)
        .filter((item) => item?.articles?.length) ?? []
  );

  ngOnInit(): void {
    const canonical = createCanonicalUrlForPageablePage('dossziek', this.route.snapshot);
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }

    const title = 'Dossziék | Magyar Nemzet';
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    });
  }

  onSubscriptionClick(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.LAYOUT);
  }
}
