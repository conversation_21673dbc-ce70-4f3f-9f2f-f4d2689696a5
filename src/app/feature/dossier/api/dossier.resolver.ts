import { ResolveFn, Router } from '@angular/router';
import { DOSSIERS_PER_PAGE, DossierService } from '../../../shared';
import { inject } from '@angular/core';
import { catchError, map, tap } from 'rxjs/operators';
import { Observable, throwError } from 'rxjs';
import { ApiListResult, BasicDossier, RedirectService } from '@trendency/kesma-ui';

export const dossierResolver: ResolveFn<Observable<ApiListResult<BasicDossier>>> = (route) => {
  const dossierService = inject(DossierService);
  const redirectService = inject(RedirectService);
  const router = inject(Router);

  const pageIndex = route.queryParams['page'] ? route.queryParams['page'] - 1 : 0;
  return dossierService.getAllDossiers(pageIndex, DOSSIERS_PER_PAGE).pipe(
    tap(({ data }) => {
      if (redirectService.shouldBeRedirect(pageIndex, data)) {
        redirectService.redirectOldUrl('dossziek', false, 302);
      }
    }),
    map(({ data, meta }) => {
      return {
        data: data.map((dossier) => ({
          ...dossier,
          mainArticle: (dossier as any).mainArticle ?? dossier.articles?.[0],
        })),
        meta,
      };
    }),
    catchError((err: Error) => {
      router
        .navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(err) },
          skipLocationChange: true,
        })
        .then();
      return throwError(() => err);
    })
  );
};
