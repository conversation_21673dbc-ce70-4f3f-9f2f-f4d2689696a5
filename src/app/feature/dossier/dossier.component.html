<section class="dossier block">
  @if (adverts()?.desktop?.roadblock_1; as ad) {
    <div class="dossier-ad">
      <kesma-advertisement-adocean [ad]="ad"> </kesma-advertisement-adocean>
    </div>
  }
  @if (adverts()?.mobile?.mobilrectangle_1; as ad) {
    <div class="dossier-ad-sm">
      <kesma-advertisement-adocean [ad]="ad"> </kesma-advertisement-adocean>
    </div>
  }

  <div class="wrapper">
    @if (dossierTop()) {
      <div class="dossier-content">
        <mno-block-title-row [data]="titleRow" [isFullWidth]="true" [headingLevel]="1" class="desktop-only"></mno-block-title-row>
        <mno-dossier-card
          [data]="dossierTop()"
          [styleID]="isMobile() ? DossierCardTypes.recommendationDossier : DossierCardTypes.recommendationDossier"
          [maxArticleCount]="3"
          [calculateArticleCount]="true"
          [hideMainArticle]="true"
        ></mno-dossier-card>
        <app-page-newsletter-banner (subscribeClick)="onSubscriptionClick()"></app-page-newsletter-banner>
        @for (dossier of dossiersBottom() | slice: 0; track dossier.slug; let i = $index; let isLast = $last) {
          <mno-dossier-card
            [data]="dossier"
            [styleID]="DossierCardTypes.recommendationDossier"
            [maxArticleCount]="3"
            [calculateArticleCount]="true"
            [hideMainArticle]="true"
          ></mno-dossier-card>
          @if (!isLast) {
            <hr class="list-separator" />
          }
          @if (i === 3) {
            <div class="dossier-ad">
              @if (adverts()?.desktop?.roadblock_2; as ad) {
                <kesma-advertisement-adocean [ad]="ad"> </kesma-advertisement-adocean>
              }
              @if (adverts()?.mobile?.mobilrectangle_2; as ad) {
                <kesma-advertisement-adocean [ad]="ad"> </kesma-advertisement-adocean>
              }
            </div>
          }
        }
        @if (rowAllCount() > DOSSIERS_PER_PAGE) {
          <mno-pager
            [rowAllCount]="rowAllCount()"
            [rowOnPageCount]="DOSSIERS_PER_PAGE"
            [isListPager]="true"
            [hasSkipButton]="true"
            [allowAutoScrollToTop]="true"
            [maxDisplayedPages]="5"
          ></mno-pager>
        }
        @if (adverts()?.desktop?.roadblock_3; as ad) {
          <kesma-advertisement-adocean [ad]="ad"> </kesma-advertisement-adocean>
        }
        @if (adverts()?.mobile?.mobilrectangle_3; as ad) {
          <kesma-advertisement-adocean [ad]="ad"> </kesma-advertisement-adocean>
        }
      </div>
    }
    <app-sidebar></app-sidebar>
  </div>
</section>
