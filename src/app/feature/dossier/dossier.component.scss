@use 'shared' as *;

.dossier {
  padding: 40px 0 80px;
  @include media-breakpoint-down(sm) {
    padding: 0 $mobile-side-padding;
  }

  .wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    @include media-breakpoint-down(sm) {
      flex-direction: column;
    }

    mno-block-title-row::ng-deep h1 {
      font-size: 20px;
      font-weight: 700;
      line-height: 26px;
      letter-spacing: 0.015em;
    }

    app-dossier-card {
      display: block;

      @include media-breakpoint-down(sm) {
        margin-bottom: 50px;
      }
    }

    .dossier-content {
      width: calc(100% - 358px);
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: stretch;
      gap: 24px;

      @include media-breakpoint-down(sm) {
        width: 100%;
      }

      .dossier-ad {
        padding: 50px 0;
        margin: 40px 0;
        border-top: 1px solid var(--kui-slate-300);
        border-bottom: 1px solid var(--kui-slate-300);

        @include media-breakpoint-down(sm) {
          margin-bottom: 50px;
        }
      }

      .dossier-ad-sm {
        @include media-breakpoint-down(xs) {
          padding: 50px 0;
          margin: 40px 0;
          border-top: 1px solid var(--kui-slate-300);
          border-bottom: 1px solid var(--kui-slate-300);
        }
      }
    }
  }
}

mno-pager {
  margin: 0 auto;
}
