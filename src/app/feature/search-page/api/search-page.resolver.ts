import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, take, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { ArticleSearchResult, RedirectService, searchResultToArticleCard } from '@trendency/kesma-ui';
import { BackendAuthorData, calculateFilters, ListPageData, ListPageService, SearchFilterService } from '../../../shared';

const maxResultsPerPage = 10;

@Injectable({
  providedIn: 'root',
})
export class SearchPageResolver {
  constructor(
    private readonly searchPageService: ListPageService,
    private readonly router: Router,
    private readonly searchFilterService: SearchFilterService,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ListPageData> {
    const filters = calculateFilters(route.queryParams);
    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;
    const articlesObservable$ = this.searchPageService.searchArticle(currentPage, maxResultsPerPage, filters);
    const columns$ = this.searchFilterService.getColumns().pipe(take(1));
    const authors$ = this.searchFilterService.getAuthors().pipe(take(1)) as Observable<BackendAuthorData[]>;

    return forkJoin({
      articles: articlesObservable$,
      columns: columns$,
      authors: authors$,
    }).pipe(
      map(({ articles, columns, authors }) => ({
        articles: articles?.data?.map((sr: ArticleSearchResult) => searchResultToArticleCard(sr)),
        limitable: articles?.meta?.limitable,
        columns,
        authors,
      })),
      tap(({ articles }) => {
        if (this.redirectService.shouldBeRedirect(currentPage, articles)) {
          this.redirectService.redirectOldUrl('kereses', false, 302);
        }
      }),
      catchError((err) => {
        this.router.navigate(['/404'], { skipLocationChange: true }).then();
        return throwError(() => err);
      })
    );
  }
}
