<section>
  <div class="wrapper">
    <kesma-advertisement-adocean
      *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
      [style]="{
        margin: 'var(--ad-margin)',
        background: 'var(--ad-bg)',
        padding: 'var(--ad-padding)',
      }"
      [ad]="ad"
    >
    </kesma-advertisement-adocean>

    <kesma-advertisement-adocean
      *ngIf="adverts?.desktop?.roadblock_1 as ad"
      [style]="{
        margin: 'var(--ad-margin)',
        background: 'var(--ad-bg)',
        padding: 'var(--ad-padding)',
      }"
      [ad]="ad"
    >
    </kesma-advertisement-adocean>

    <app-search-filter (filterEvent)="onChangeFilters($event)"></app-search-filter>
    <h1 *ngIf="alternativeTitle">{{ alternativeTitle }}</h1>
    <div class="search-results" *ngIf="limitable?.rowAllCount; else noResult">
      Találatok: {{ (limitable?.rowFrom ?? 0) + 1 }}-{{ resultsCount }} cikk megjelenítése a {{ limitable?.rowAllCount }}-ből
      <ng-container *ngIf="globalFilter">, {{ globalFilter }} kifejezésre</ng-container>
    </div>
    <ng-template #noResult>
      <div class="search-results">
        Nincs találat
        <ng-container *ngIf="globalFilter"> {{ globalFilter }} kifejezésre</ng-container>
      </div>
    </ng-template>

    <div class="first-article" [class.opinion]="articles[0]?.contentType === 'opinion' || articles[0]?.isOpinion" *ngIf="articles[0]">
      <article
        mno-article-card
        [data]="articles[0]"
        [styleID]="(isMobile$ | async) ? ArticleCardType.Img16TopTagsTitleLeadBadge : ArticleCardType.DateImgRightTagsTitleLeadWideLarge"
        [asResult]="!!(isMobile$ | async)"
      ></article>
    </div>
  </div>

  <div class="wrapper narrow-wrapper with-aside">
    <div class="left-column">
      <app-page-newsletter-banner (subscribeClick)="onSubscriptionClick()"></app-page-newsletter-banner>
      <div class="articles" *ngIf="articles?.length && articles.length > 1">
        <ng-container *ngFor="let article of articles | slice: 1; let i = index">
          <article
            mno-article-card
            [data]="article"
            [styleID]="(isMobile$ | async) ? ArticleCardType.ImgRightTagsTitleLeadBadge : ArticleCardType.DateImgRightTagsTitleLeadWide"
            [asResult]="!!(isMobile$ | async)"
          ></article>
          <div class="advert" *ngIf="i === 3">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_2 as ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
              [ad]="ad"
            ></kesma-advertisement-adocean>
            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
              [ad]="ad"
            ></kesma-advertisement-adocean>
          </div>
        </ng-container>
      </div>

      <mno-pager
        *ngIf="limitable?.pageMax! > 0"
        [rowAllCount]="limitable?.rowAllCount!"
        [rowOnPageCount]="limitable?.rowOnPageCount!"
        [isListPager]="true"
        [hasFirstLastButton]="false"
        [hasSkipButton]="true"
        [allowAutoScrollToTop]="true"
        [maxDisplayedPages]="3"
      >
      </mno-pager>
      <kesma-advertisement-adocean *ngIf="adverts?.desktop?.roadblock_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
      <kesma-advertisement-adocean *ngIf="adverts?.mobile?.mobilrectangle_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
    </div>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
