import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseSearchPageComponent } from '../../../shared';
import { AdvertisementAdoceanComponent } from '@trendency/kesma-ui';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { ArticleCardComponent, PagerComponent } from 'src/app/shared';
import { SearchFilterComponent } from 'src/app/shared/components/search-filter/search-filter.component';
import { AsyncPipe, NgFor, NgIf, SlicePipe } from '@angular/common';
import { PageNewsletterBannerComponent } from 'src/app/shared/components/page-newsletter-banner/page-newsletter-banner.component';

@Component({
  selector: 'app-search-page',
  templateUrl: './search-page.component.html',
  styleUrls: ['./search-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AdvertisementAdoceanComponent,
    SidebarComponent,
    Pager<PERSON>omponent,
    ArticleCardComponent,
    SearchFilterComponent,
    SlicePipe,
    AsyncPipe,
    PageNewsletterBannerComponent,
    NgIf,
    NgFor,
  ],
})
export class SearchPageComponent extends BaseSearchPageComponent {}
