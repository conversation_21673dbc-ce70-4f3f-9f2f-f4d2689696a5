@use 'shared' as *;

:host {
  display: block;
  padding-top: 16px;

  @include media-breakpoint-down(sm) {
    .wrapper {
      width: calc(100% - #{2 * $mobile-side-padding});
      margin: 0 auto;
    }
  }

  app-search-filter {
    margin-bottom: 40px;
  }

  .search-results {
    font-size: 20px;
    font-weight: 700;
    line-height: 26px; /* 130% */
    letter-spacing: 0.3px;

    @include media-breakpoint-down(sm) {
      font-size: 16px;
      line-height: 21px; /* 131.25% */
    }
  }

  .first-article {
    margin-top: 20px;
    border-top: 1px solid var(--kui-slate-300);
    border-bottom: 1px solid var(--kui-slate-300);
    padding: 40px 0 16px;

    @include media-breakpoint-down(sm) {
      padding: 20px 0 16px;
    }

    &.opinion {
      border-bottom-color: var(--kui-origo-orange-500);
    }
  }

  .articles {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin: 24px 0 40px;

    [mno-article-card] {
      padding-bottom: 16px;
      border-bottom: 1px solid var(--kui-slate-300);

      &.is-opinion {
        border-bottom-color: var(--kui-slate-300);
      }
    }
  }

  mno-pager {
    &::ng-deep {
      .pager {
        @include media-breakpoint-down(sm) {
          margin-bottom: 30px;
        }
      }
    }
  }
}
