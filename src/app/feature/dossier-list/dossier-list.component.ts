import { ChangeDetectionStrategy, ChangeDetectorRef, <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  ArticleCard,
  DossierArticle,
  NEWSLETTER_COMPONENT_TYPE,
  RedirectService,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { combineLatest, Observable, Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { ConfigService, defaultMetaInfo, DossierService } from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { ArticleCardComponent, ArticleCardType, BlockTitleRowComponent, PagerComponent } from 'src/app/shared';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>orOf, NgIf, SlicePipe } from '@angular/common';
import { PageNewsletterBannerComponent } from 'src/app/shared/components/page-newsletter-banner/page-newsletter-banner.component';

@Component({
  selector: 'app-dossier-list',
  templateUrl: './dossier-list.component.html',
  styleUrls: ['./dossier-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AdvertisementAdoceanComponent,
    SidebarComponent,
    PagerComponent,
    ArticleCardComponent,
    AsyncPipe,
    SlicePipe,
    BlockTitleRowComponent,
    PageNewsletterBannerComponent,
    NgIf,
    NgFor,
    NgForOf,
  ],
})
export class DossierListComponent implements OnInit, OnDestroy {
  articles: DossierArticle[] = [];

  rowAllCount = 0;
  rowOnPageCount = 20;
  rowFrom = 0;
  titleRow = {
    text: '',
  };
  private readonly destroy$ = new Subject<boolean>();

  readonly ArticleCardType = ArticleCardType;
  ads: AdvertisementsByMedium = {} as AdvertisementsByMedium;

  get articleCards(): ArticleCard[] {
    return this.articles as ArticleCard[];
  }

  isMobile$: Observable<boolean>;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly dossierService: DossierService,
    private readonly seo: SeoService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly analyticsService: AnalyticsService,
    private readonly configService: ConfigService,
    private readonly redirectService: RedirectService
  ) {}

  ngOnInit(): void {
    this.isMobile$ = this.configService.isMobile$;

    combineLatest([this.route.queryParams, this.route.params])
      .pipe(takeUntil(this.destroy$))
      .subscribe(([{ page }, { slug }]) => {
        if (!slug) {
          return;
        }
        return (
          this.dossierService
            .getDossier(slug, page ? parseInt(page, 10) - 1 : 0, this.rowOnPageCount)
            // eslint-disable-next-line rxjs/no-nested-subscribe
            .subscribe(({ data, meta }) => {
              if (this.redirectService.shouldBeRedirect(Number(page), data)) {
                this.redirectService.redirectOldUrl(`dosszie/${slug}`, false, 302);
              }
              if (!data) {
                return;
              }

              const { rowAllCount, rowOnPageCount, rowFrom } = meta?.limitable || {};
              this.rowAllCount = rowAllCount ?? 0;
              this.rowOnPageCount = rowOnPageCount ?? 0;
              this.rowFrom = rowFrom ?? 0;
              this.articles = data ?? [];
              this.titleRow = { text: meta?.title ?? '' };
              if (!this.titleRow.text) {
                return;
              }

              const canonical = createCanonicalUrlForPageablePage('dosszie', this.route.snapshot);
              if (canonical) {
                this.seo.updateCanonicalUrl(canonical);
              }
              const title = `${this.titleRow.text} | Magyar Nemzet`;
              this.seo.setMetaData({
                ...defaultMetaInfo,
                title,
                ogTitle: title,
                // eslint-disable-next-line max-len
                ogDescription: `${this.titleRow.text} dosszié tartalmai a Magyar Nemzet oldalán. Hírek, képek, videók, vélemények és aktuális cikkek ${this.titleRow.text} témakörben.`,
              });
              this.changeRef.markForCheck();
            })
        );
      });

    this.adStore.advertisemenets$
      .pipe(takeUntil(this.destroy$))
      .pipe(map((ads) => this.adStore.separateAdsByMedium(ads)))
      .subscribe((ads: AdvertisementsByMedium) => {
        this.ads = ads;
        this.changeRef.markForCheck();
      });
  }

  trackByFn(index: number, item: ArticleCard): string {
    return item?.id ?? item.slug ?? index?.toString();
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  onSubscriptionClick(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.LAYOUT);
  }
}
