<section class="branding-list block">
  <div class="result-bar">
    <p>{{ title }}</p>
  </div>

  <article
    *ngIf="articles?.length"
    mno-article-card
    [data]="articles[0]"
    [styleID]="(isMobile$ | async) ? ArticleCardType.Img16TopTagsTitleLeadBadge : ArticleCardType.DateImgRightTagsTitleLeadWideLarge"
    [asResult]="!!(isMobile$ | async)"
  ></article>

  <div class="wrapper with-aside">
    <div class="branding-list-content">
      <ng-container *ngFor="let article of articles | slice: 1; let i = index; let first = first">
        <app-page-newsletter-banner *ngIf="first"></app-page-newsletter-banner>

        <article
          mno-article-card
          [data]="article"
          [styleID]="(isMobile$ | async) ? ArticleCardType.ImgRightTagsTitleLeadBadge : ArticleCardType.DateImgRightTagsTitleLeadWideLarge"
        ></article>

        <hr class="list-separator" />
      </ng-container>
      <mno-pager
        *ngIf="rowAllCount > rowOnPageCount"
        [rowAllCount]="rowAllCount"
        [rowOnPageCount]="rowOnPageCount"
        [isListPager]="true"
        [hasSkipButton]="true"
        [allowAutoScrollToTop]="true"
        [maxDisplayedPages]="5"
      >
      </mno-pager>
    </div>
    <app-sidebar></app-sidebar>
  </div>
</section>
