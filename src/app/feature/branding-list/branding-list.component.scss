@use 'shared' as *;

@mixin dossierLinkButton() {
  .dossier-link-button {
    display: inline-flex;
    position: relative;
    color: var(--kui-red);
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 500;
    padding-left: 18px;

    &.blue {
      color: var(--kui-blue);

      &:before {
        // background-image: url($images-path + 'red-blue-arrow.png');
        width: 7px;
        height: 9px;
      }
    }

    &:before {
      position: absolute;
      content: '';
      // background: url($images-path + 'red-right-arrow.png') no-repeat center;
      width: 6px;
      height: 11px;
      left: 0;
      top: 45%;
      transform: translateY(-50%);
    }
  }
}

.branding-list {
  padding: 40px 15px 80px;
  display: flex;
  flex-direction: column;
  gap: 20px;

  @include dossierLinkButton();

  .back-to {
    margin-top: 30px;
    text-align: center;
  }

  .wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    @include media-breakpoint-up(md) {
      max-width: 100%;
    }
  }

  .branding-list-content {
    width: calc(100% - 358px);

    @include media-breakpoint-down(sm) {
      width: 100%;
    }
  }

  app-page-newsletter-banner {
    margin-bottom: 20px;
  }

  .list-separator {
    margin: 15px 0;
  }

  .result-bar {
    height: 28px;
    background-color: #143d5d;
    padding-left: 10px;
    min-width: 595px;

    @include media-breakpoint-down(xs) {
      width: 100%;
      margin: auto;
      min-width: auto;
    }

    p {
      margin: auto;
      color: white;
      font-family: Roboto;
      font-size: 14px;
      font-weight: 500;
      line-height: 28px;
      text-align: left;
      text-transform: uppercase;
    }
  }

  app-article-card {
    display: block;
    margin-bottom: 50px;
  }

  mno-pager {
    margin-top: 40px;
  }

  .two-col-block {
    display: flex;
    flex-wrap: wrap;
    margin-left: -15px;
    margin-right: -15px;
    width: calc(100% + 30px);
    margin-bottom: 50px;

    app-article-card {
      margin-bottom: 22px;
    }
  }

  .branding-ad {
    padding: 50px 0;
    margin: 40px 0;
    border-top: 1px solid var(--kui-grey-200);
    border-bottom: 1px solid var(--kui-grey-200);

    .ad-container {
      margin: 0 auto;
      width: 640px;
      height: 360px;
      max-width: 100%;
      @include img('ad-horizontal.png');

      @include media-breakpoint-down(sm) {
        width: 350px;
        height: 200px;
      }

      @include media-breakpoint-down(xs) {
        width: 300px;
        height: 180px;
      }
    }
  }
}
