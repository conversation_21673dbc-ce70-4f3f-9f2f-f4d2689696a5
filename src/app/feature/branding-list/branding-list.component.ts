import { ChangeDetectionStrategy, ChangeDetectorRef, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanStoreService,
  AnalyticsService,
  ArticleCard,
  NEWSLETTER_COMPONENT_TYPE,
  RedirectService,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { map, Observable, Subject, takeUntil, tap } from 'rxjs';
import { ApiService } from 'src/app/shared/services/api.service';
import { ArticleCardTypes, BrandingType, ConfigService, DossierSlugResponse } from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { ArticleCardComponent, ArticleCardType, PagerComponent } from 'src/app/shared';
import { As<PERSON><PERSON>ip<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>or<PERSON><PERSON>, <PERSON>I<PERSON>, SlicePipe } from '@angular/common';
import { PageNewsletterBannerComponent } from 'src/app/shared/components/page-newsletter-banner/page-newsletter-banner.component';

@Component({
  selector: 'app-branding-list',
  templateUrl: './branding-list.component.html',
  styleUrls: ['./branding-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SidebarComponent, PagerComponent, NgIf, ArticleCardComponent, AsyncPipe, SlicePipe, PageNewsletterBannerComponent, NgIf, NgFor, NgForOf],
})
export class BrandingListComponent implements OnInit, OnDestroy {
  destroy$: Subject<boolean> = new Subject<boolean>();
  public rowOnPageCount = 20;
  public rowAllCount = 0;
  public rowFrom = 0;
  public articles: ArticleCard[];
  COUNT_FROM_ONE = 1;
  AD_FREQUENCY = 4;
  readonly cardType = ArticleCardTypes;
  public title: string;

  public arrayAds?: Record<'desktop' | 'mobile', Advertisement[]>;

  readonly ArticleCardType = ArticleCardType;

  isMobile$: Observable<boolean>;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly apiService: ApiService,
    private readonly router: Router,
    private readonly seo: SeoService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly configService: ConfigService,
    private readonly analyticsService: AnalyticsService,
    private readonly redirectService: RedirectService
  ) {}

  ngOnInit(): void {
    this.isMobile$ = this.configService.isMobile$;
    this.setPageTitle(this.route.snapshot.data['routeType']);
    this.route.queryParams
      .pipe(
        // eslint-disable-next-line rxjs/no-unsafe-takeuntil
        takeUntil(this.destroy$),
        tap(() => this.initAds())
      )
      .subscribe(({ page }) => {
        const pageIndex = page ? parseInt(page, 10) - 1 : 0;
        this.getBrandingListInfo(this.route.snapshot.data['routeType'], pageIndex);
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  initAds(): void {
    this.adStore?.advertisemenets$
      .pipe(takeUntil(this.destroy$))
      .pipe(
        tap(() => this.resetAds()),
        map((ads: Advertisement[]) => this.adStore.separateAdsByMedium(ads))
      )
      .subscribe((ads) => {
        if (!ads) return;

        this.arrayAds = {
          desktop: [ads.desktop.roadblock_1, ads.desktop.roadblock_2, ads.desktop.roadblock_3, ads.desktop.roadblock_4],
          mobile: [ads.mobile.mobilrectangle_1, ads.mobile.mobilrectangle_2, ads.mobile.mobilrectangle_3, ads.mobile.mobilrectangle_4],
        };
        this.changeRef.detectChanges();
      });
  }

  onSubscriptionClick(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.LAYOUT);
  }

  private resetAds(): void {
    this.arrayAds = null as any;
    this.changeRef.detectChanges();
  }

  private getBrandingListInfo(routeType: BrandingType, nextPage?: number): void {
    this.apiService.getBrandingList(routeType, nextPage, this.rowOnPageCount).subscribe((res: DossierSlugResponse) => {
      if (this.redirectService.shouldBeRedirect(Number(nextPage), res?.data)) {
        this.redirectService.redirectOldUrl(routeType, false, 302);
      }
      if (!res?.data) {
        return;
      }
      const { rowAllCount, rowOnPageCount, rowFrom } = res?.meta?.limitable || {};
      this.rowAllCount = rowAllCount;
      this.rowOnPageCount = rowOnPageCount;
      this.rowFrom = rowFrom;
      this.articles = res?.data;
      this.articles.forEach((article, i) => {
        this.articles[i] = {
          ...article,
          thumbnailFocusedImages: article?.thumbnailUrlFocusedImages,
          brand: article.brandingType,
        };
      });
      this.setMetaData();
      this.changeRef.detectChanges();
    });
  }

  private setPageTitle(routeType: BrandingType): void {
    let title: string;
    switch (routeType) {
      case 'automotor':
        title = 'Autó-Motor';
        break;
      case 'auto-motor':
        title = 'Autó-Motor';
        break;
      case 'mindmegette':
        title = 'Mindmegette';
        break;
      case 'orszagszerte':
        title = 'Országszerte';
        break;
      case 'lakaskultura':
        title = 'Lakáskultúra';
        break;
      case 'unilife':
        title = 'Unilife';
        break;
    }
    this.title = title;
  }

  public onSelectPage(page: string): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { page: page },
    });
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage(this.route.snapshot.data['routeType'], this.route.snapshot);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = `${this.title} | Magyar Nemzet`;
    const metaData: IMetaData = {
      title,
      ogTitle: title,
    };

    this.seo.setMetaData(metaData);
  }
}
