@if (isBrowser) {
  <kesma-layout-editor [layoutComponentRef]="layoutComponent" [placeholderOverrides]="placeholderOverrides" />
  <ng-template
    #layoutComponent
    let-structure="structure"
    let-content="content"
    let-contentComponentsWrapper="contentComponentsWrapper"
    let-contentComponentsInnerWrapper="contentComponentsInnerWrapper"
    let-blockTitleWrapper="blockTitleWrapper"
    let-editorFrameSize="editorFrameSize"
  >
    <app-layout
      [layoutType]="LayoutPageType.HOME"
      [structure]="structure"
      [configuration]="content"
      [contentComponentsWrapper]="contentComponentsWrapper"
      [contentComponentsInnerWrapper]="contentComponentsInnerWrapper"
      [blockTitleWrapper]="blockTitleWrapper"
      [editorFrameSize]="editorFrameSize"
    />
  </ng-template>

  <ng-template #placeholderOverrides let-layoutElement="layoutElement" let-contentDisplay="contentDisplay">
    @switch (layoutElement.contentType) {
      @case (LayoutElementContentType.Ad) {
        <kesma-advertisement-placeholder [layoutElement]="layoutElement" />
      }
      @case (LayoutElementContentType.FastNews) {
        <img src="/assets/images/placeholders/fresh-news.png" alt="" />
      }
      @default {
        <ng-container *ngTemplateOutlet="contentDisplay" />
      }
    }
  </ng-template>
}
