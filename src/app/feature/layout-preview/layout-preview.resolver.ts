import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Layout } from '@trendency/kesma-ui';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiService } from 'src/app/shared/services/api.service';

@Injectable()
export class LayoutPreviewResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<Layout> {
    return this.apiService.getLayoutPreview(route.params['layoutHash']).pipe(
      catchError((err) => {
        this.router.navigate(['/', '404'], {
          skipLocationChange: true,
        });
        return throwError(err);
      }),
      map(({ data }) => data)
    );
  }
}
