<section class="wrapper">
  <p *ngIf="rowAllCount === 0; else firstResult" class="videos-notfound text-center">nincs tal<PERSON>lat</p>

  <ng-template #firstResult>
    <app-module-heading
      [articles]="articles"
      [title]="titleRow.text"
      [showTitle]="pageNumber === 1"
      [color]="PageType.Video ? 'black' : 'gray'"
      [isVideo]="pageType === PageType.Video"
      [isPodcast]="pageType === PageType.Podcast"
      [isMobile]="(isMobile$ | async) === true"
      [forceDesktopCardStyle]="true"
    >
    </app-module-heading>
  </ng-template>

  <div class="videos-ad" *ngIf="ads?.desktop?.roadblock_1 as ad">
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  </div>

  <div class="videos-ad-sm" *ngIf="ads?.mobile?.mobilrectangle_1 as ad">
    <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
  </div>
</section>

<div class="wrapper with-aside videos-list-container">
  <div class="videos-list d-flex flex-column left-column">
    <app-page-newsletter-banner (subscribeClick)="onSubscriptionClick()"></app-page-newsletter-banner>
    <ng-container *ngFor="let article of articles | slice: 3; let i = index; let isLast = last; trackBy: trackByFn">
      <article
        mno-article-card
        [data]="article"
        [styleID]="(altArticleCardStyle$ | async) ? ArticleCardType.Img16TopTagsTitleLeadBadge : ArticleCardType.DateImgRightTagsTitleLeadWide"
        [showThumbnail]="!!article?.thumbnailUrl || !!article?.thumbnailUrl43 || !!article?.thumbnail?.url"
        [asVideo]="true"
      ></article>
      <hr class="list-separator" *ngIf="!isLast" />

      <ng-container *ngIf="i === 3">
        <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_2 as ad" [ad]="ad"> </kesma-advertisement-adocean>
      </ng-container>
    </ng-container>

    <mno-pager
      *ngIf="rowAllCount > 0"
      [rowAllCount]="rowAllCount"
      [rowOnPageCount]="rowOnPageCount"
      [isListPager]="true"
      [hasSkipButton]="true"
      [allowAutoScrollToTop]="true"
      [maxDisplayedPages]="5"
    ></mno-pager>

    <kesma-advertisement-adocean *ngIf="ads?.desktop?.roadblock_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
    <kesma-advertisement-adocean *ngIf="ads?.mobile?.mobilrectangle_3 as ad" [ad]="ad"> </kesma-advertisement-adocean>
  </div>
  <app-sidebar></app-sidebar>
</div>
