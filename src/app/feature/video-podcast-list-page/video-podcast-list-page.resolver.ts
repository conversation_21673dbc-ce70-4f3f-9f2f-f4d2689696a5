import { Inject, Injectable, Optional } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { RESPONSE, UtilService } from '@trendency/kesma-core';
import { backendDateToDate, mapBackendArticleDataToArticleCard, RedirectService } from '@trendency/kesma-ui';
import type { Response } from 'express';
import { Observable, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { ApiService } from 'src/app/shared/services/api.service';
import { environment } from '../../../environments/environment';
import { ArticleService } from '../../shared';
import { PageType, PodcastVideoResolverData } from './video-podcast.definitions';

const MAX_RESULTS_PER_PAGE = 20;

@Injectable({
  providedIn: 'root',
})
export class VideoPodcastListPageResolver {
  constructor(
    private readonly articleService: ArticleService,
    private readonly apiService: ApiService,
    private readonly router: Router,
    @Inject(RESPONSE) @Optional() private readonly response: Response,
    private readonly utilsService: UtilService,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<PodcastVideoResolverData> {
    const pageType = route.data['contentType'] as PageType;
    const page = route.queryParams['page'] ? route.queryParams['page'] - 1 : 0;

    const serviceCall$ =
      pageType === 'video'
        ? this.articleService.getVideoTypeArticles('', page, MAX_RESULTS_PER_PAGE, {
            'publishDate_order[0]': 'desc',
          })
        : this.apiService.searchByKeyword(page, MAX_RESULTS_PER_PAGE, {
            'publishDate_order[0]': 'desc',
            'content_types[]': 'articlePodcast',
          });

    return serviceCall$
      .pipe(
        map(({ data, meta }) => ({ data: (data ?? []).map(mapBackendArticleDataToArticleCard), meta })),
        map(({ data, meta }) => ({
          articleResult: {
            data: data.map((rec) => ({
              ...rec,
              publishDate: backendDateToDate(rec.publishDate as string) as Date,
            })),
            meta,
          },
          pageType,
        }))
      )
      .pipe(
        tap(({ articleResult: { data, meta } }) => {
          if (this.redirectService.shouldBeRedirect(page, data)) {
            this.redirectService.redirectOldUrl(pageType, false, 302);
          }
          if (data.length) {
            return;
          }

          if (!meta?.['redirect']?.tag?.slug) {
            return;
          }
          // if redirect needed (merged tag)
          const redirectUrl = `${environment.siteUrl}/cimke/${meta['redirect'].tag.slug}`;

          // client side (basic redirection)
          if (this.utilsService.isBrowser()) {
            this.router.navigateByUrl(redirectUrl);
            return;
          }

          // server side (SSR - 301 redirect with express js response injector)
          this.response.status(301);
          this.response.setHeader('location', redirectUrl);
        }),
        catchError((error) => {
          this.router.navigate(['/', '404'], {
            state: { errorResponse: JSON.stringify(error) },
            skipLocationChange: true,
          });
          return throwError(error);
        })
      );
  }
}
