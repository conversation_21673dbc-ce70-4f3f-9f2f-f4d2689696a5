@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: center;
}

mno-block-title-row::ng-deep {
  .block-title {
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;
    letter-spacing: 0.015em;
    text-align: left;
    margin: 24px 0;
  }
}

.videos-list {
  gap: 24px;

  [mno-article-card]::ng-deep {
    .article-title:not(:hover) {
      color: var(--kui-slate-950);
    }

    .article-label:not(:hover) {
      color: var(--kui-blue-900);
    }
  }
}

div.wrapper.with-aside.videos-list-container {
  @include media-breakpoint-down(md) {
    width: unset;
    margin: 0 16px;
  }
}

app-module-heading.black {
  @include media-breakpoint-down(md) {
    width: unset;
  }
}

mno-block-title-row::ng-deep {
  .block-title {
    color: var(--kui-slate-950);

    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 26px;
    letter-spacing: 0.3px;
  }

  margin-bottom: 25px;
}

mno-block-title-row + article + .videos-ad {
  margin-top: 24px;
}
