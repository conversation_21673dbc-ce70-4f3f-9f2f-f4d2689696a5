import { ChangeDetectionStrategy, ChangeDetectorRef, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SeoService, UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  ArticleCard,
  NEWSLETTER_COMPONENT_TYPE,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { fromEvent, Observable, Subject } from 'rxjs';
import { filter, map, startWith, takeUntil } from 'rxjs/operators';
import { ConfigService, defaultMetaInfo } from '../../shared';
import { PageType, PodcastVideoResolverData } from './video-podcast.definitions';
import { AsyncPipe, NgFor, NgIf, SlicePipe } from '@angular/common';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { ArticleCardComponent, ArticleCardType, PagerComponent } from 'src/app/shared';
import { ModuleHeadingComponent } from 'src/app/shared/components/module-heading/module-heading.component';
import { PageNewsletterBannerComponent } from 'src/app/shared/components/page-newsletter-banner/page-newsletter-banner.component';

@Component({
  selector: 'app-video-podcast-list-page',
  templateUrl: './video-podcast-list-page.component.html',
  styleUrls: ['./video-podcast-list-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    NgFor,
    AdvertisementAdoceanComponent,
    SidebarComponent,
    PagerComponent,
    AsyncPipe,
    ArticleCardComponent,
    SlicePipe,
    ModuleHeadingComponent,
    PageNewsletterBannerComponent,
  ],
})
export class VideoPodcastListPageComponent implements OnInit, OnDestroy {
  articles: ArticleCard[];
  slug: string;
  rowAllCount: number;
  rowOnPageCount: number;
  rowFrom: number;
  titleRow = {
    text: 'Videók',
  };
  pageType: PageType = PageType.Video;
  readonly ArticleCardType = ArticleCardType;
  readonly PageType = PageType;
  ads: AdvertisementsByMedium = {} as AdvertisementsByMedium;
  pageNumber = 1;
  isMobile$: Observable<boolean>;
  altArticleCardStyle$ = this.utilService.isBrowser()
    ? fromEvent(window, 'resize').pipe(
        startWith(window.innerWidth),
        map(() => window.innerWidth < 480 || (window.innerWidth < 1100 && window.innerWidth > 480))
      )
    : this.configService.isMobile$;
  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly cd: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly analyticsService: AnalyticsService,
    private readonly configService: ConfigService,
    private readonly utilService: UtilService
  ) {}

  ngOnInit(): void {
    this.isMobile$ = this.configService.isMobile$;
    this.route.data.pipe(takeUntil(this.destroy$)).subscribe((res: any) => {
      if (!res?.searchData) {
        return;
      }

      const {
        articleResult: { data: articles, meta },
        pageType,
      } = res?.searchData as PodcastVideoResolverData;
      const isVideoPageType = pageType === PageType.Video;

      this.articles = articles;
      this.pageType = pageType;
      this.titleRow.text = isVideoPageType ? 'Videók' : 'Podcastek';

      const limitable = meta?.limitable;
      this.rowAllCount = limitable?.rowAllCount ?? 0;
      this.rowOnPageCount = limitable?.rowOnPageCount ?? 0;
      this.rowFrom = limitable?.rowFrom ?? 0;

      const canonical = createCanonicalUrlForPageablePage(isVideoPageType ? 'video' : 'podcast', this.route.snapshot);
      if (canonical) {
        this.seo.updateCanonicalUrl(canonical);
      }
      const title = `${this.titleRow.text} | Magyar Nemzet`;
      this.seo.setMetaData({
        ...defaultMetaInfo,
        title,
        ogTitle: title,
        ogDescription: ``,
      });
      this.cd.detectChanges();
    });

    this.adStore.advertisemenets$
      .pipe(takeUntil(this.destroy$))
      .pipe(map((ads: Advertisement[]) => this.adStore.separateAdsByMedium(ads)))
      .subscribe((ads: AdvertisementsByMedium) => {
        this.ads = ads;
        this.cd.markForCheck();
      });

    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .pipe(
        filter((params) => 'page' in params),
        map((params) => params['page'])
      )
      .subscribe((page: string) => {
        this.pageNumber = parseInt(page, 10);
        this.cd.markForCheck();
      });
  }

  trackByFn(index: number, item: ArticleCard): string {
    return item?.id ?? item.slug ?? index?.toString();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSubscriptionClick(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.LAYOUT);
  }
}
