import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { StaticPageComponent } from '../static-page/static-page.component';
import { NgIf } from '@angular/common';
import { ArticleComponent } from '../article/article.component';

@Component({
  selector: 'app-slug-route-handler',
  templateUrl: './slug-route-handler.component.html',
  styleUrls: ['./slug-route-handler.component.scss'],
  imports: [StaticPageComponent, NgIf, ArticleComponent],
})
export class SlugRouteHandlerComponent implements OnInit, OnDestroy {
  staticPage: boolean;

  unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.data.pipe(takeUntil(this.unsubscribe$)).subscribe((res) => {
      this.staticPage = !(res?.['articlePageData']?.article || res?.['articlePageData']?.data);
      this.cdr.detectChanges();
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
