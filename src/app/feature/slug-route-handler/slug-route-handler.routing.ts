import { Routes } from '@angular/router';
import { SlugRouteHandlerComponent } from './slug-route-handler.component';
import { StaticPageResolver } from '../static-page/static-page.resolver';
import { ArticleResolverService } from '../article/article.resolver';

export const STATIC_PAGE_ROUTES: Routes = [
  {
    path: '',
    component: SlugRouteHandlerComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    providers: [StaticPageResolver, ArticleResolverService],
    resolve: {
      staticPageData: StaticPageResolver,
      articlePageData: ArticleResolverService,
    },
  },
  {
    path: ':reviewSlug',
    component: SlugRouteHandlerComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    providers: [ArticleResolverService],
    resolve: {
      articlePageData: ArticleResolverService,
    },
  },
];
