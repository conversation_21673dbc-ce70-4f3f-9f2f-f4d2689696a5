import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs/operators';
import { ApiR<PERSON>ult, ApiResponseMetaList, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { QuizCategory } from '../quiz.definitions';
import { SeoService } from '@trendency/kesma-core';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { BlockTitleRowComponent, defaultMetaInfo, PageNewsletterBannerComponent, PagerComponent } from '../../../shared';

@Component({
  selector: 'app-quiz-category-list',
  templateUrl: './quiz-category-list.component.html',
  styleUrl: './quiz-category-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SidebarComponent, BlockTitleRowComponent, RouterLink, PageNewsletterBannerComponent, PagerComponent],
})
export class QuizCategoryListComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);

  readonly routeData = toSignal<ApiResult<QuizCategory[], ApiResponseMetaList>>(
    this.route.data.pipe(
      map(({ data }) => {
        this.setMetaData();
        return data;
      })
    )
  );

  readonly quizCategories = computed<QuizCategory[] | undefined>(() => this.routeData()?.data);
  readonly quizMeta = computed<ApiResponseMetaList | undefined>(() => this.routeData()?.meta);

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('kvizek', this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = 'Kvíz kategóriák | Magyar Nemzet';
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    });
  }
}
