<section>
  <div class="wrapper">
    <mno-block-title-row [data]="{ text: '<PERSON><PERSON><PERSON><PERSON>' }" [isFullWidth]="true" [headingLevel]="1" [isColumn]="true"></mno-block-title-row>
  </div>
  <div class="wrapper with-aside">
    <div class="left-column">
      <app-page-newsletter-banner />
      @for (category of quizCategories(); track category.id) {
        <div class="category">
          <a class="category-link" [routerLink]="['/kvizek', category.slug]">
            <h2 class="category-title">{{ category.title }}</h2>
          </a>
          @if (!$last) {
            <hr class="list-separator" />
          }
        </div>
      }
      @if (quizMeta()?.limitable; as limitable) {
        @if (limitable?.pageMax) {
          <mno-pager
            [rowAllCount]="limitable?.rowAllCount"
            [rowOnPageCount]="limitable?.rowOnPageCount"
            [isListPager]="true"
            [hasSkipButton]="true"
            [allowAutoScrollToTop]="true"
            [maxDisplayedPages]="5"
          ></mno-pager>
        }
      }
    </div>
    <aside>
      <app-sidebar />
    </aside>
  </div>
</section>
