<section>
  <div class="wrapper">
    <mno-block-title-row [data]="{ text: quizCategoryTitle() }" [isFullWidth]="true" [headingLevel]="1" [isColumn]="true"></mno-block-title-row>
  </div>
  <div class="wrapper with-aside">
    <div class="left-column">
      <app-page-newsletter-banner />
      @for (quiz of quizList(); track quiz.quizId) {
        <div class="quiz">
          <a class="quiz-link" [routerLink]="['/kvizek', quiz.quizCategorySlug, quiz.quizSlug]">
            <h2 class="quiz-title">{{ quiz.quizTitle }}</h2>
          </a>
          @if (!$last) {
            <hr class="list-separator" />
          }
        </div>
      }
      @if (quizMeta()?.limitable; as limitable) {
        @if (limitable?.pageMax) {
          <mno-pager
            [rowAllCount]="limitable?.rowAllCount"
            [rowOnPageCount]="limitable?.rowOnPageCount"
            [isListPager]="true"
            [hasSkipButton]="true"
            [allowAutoScrollToTop]="true"
            [maxDisplayedPages]="5"
          ></mno-pager>
        }
      }
    </div>
    <aside>
      <app-sidebar />
    </aside>
  </div>
</section>
