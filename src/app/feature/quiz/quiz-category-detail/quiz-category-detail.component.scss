@use 'shared' as *;

:host {
  display: block;
  margin-block: 24px;
  @include media-breakpoint-down(md) {
    padding-inline: 16px;
  }
  .left-column {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  .quiz {
    display: flex;
    flex-direction: column;
    &-link {
      color: var(--kui-slate-950);
    }
    &-title {
      font-size: 20px;
      line-height: 26px;
      overflow-wrap: anywhere;
      &:hover {
        color: var(--kui-blue-700);
      }
    }
  }
  .list-separator {
    margin-top: 0;
  }
  .with-aside {
    gap: 24px;
  }
}
