import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ApiResponseMetaList, ApiResult, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { Quiz } from '../quiz.definitions';
import { map } from 'rxjs/operators';
import { BlockTitleRowComponent, defaultMetaInfo, PageNewsletterBannerComponent, PagerComponent } from '../../../shared';

@Component({
  selector: 'app-quiz-category-detail',
  templateUrl: './quiz-category-detail.component.html',
  styleUrl: './quiz-category-detail.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SidebarComponent, BlockTitleRowComponent, PageNewsletterBannerComponent, RouterLink, PagerComponent],
})
export class QuizCategoryDetailComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);

  readonly routeData = toSignal<ApiResult<Quiz[], ApiResponseMetaList>>(
    this.route.data.pipe(
      map(({ data: routeResult }) => {
        const { data } = routeResult as ApiResult<Quiz[], ApiResponseMetaList>;
        const categoryTitle = data?.[0]?.quizCategoryTitle;
        this.setMetaData(categoryTitle);
        return routeResult;
      })
    )
  );

  readonly quizList = computed<Quiz[] | undefined>(() => this.routeData()?.data);
  readonly quizMeta = computed<ApiResponseMetaList | undefined>(() => this.routeData()?.meta);

  readonly quizCategoryTitle = computed<string>(() => {
    return this.quizList()?.[0]?.quizCategoryTitle ?? '';
  });

  private setMetaData(categoryTitle: string): void {
    const canonical = createCanonicalUrlForPageablePage('kvizek', this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = `${categoryTitle} kvízek | Magyar Nemzet`;
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    });
  }
}
