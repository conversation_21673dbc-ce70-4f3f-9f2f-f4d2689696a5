import { inject, Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { ApiResult, Quiz as KesmaQuiz } from '@trendency/kesma-ui';
import { Quiz, QuizCategory } from './quiz.definitions';

@Injectable({
  providedIn: 'root',
})
export class QuizService {
  private readonly reqService = inject(ReqService);

  getQuizCategoryList(params: object): Observable<ApiResult<QuizCategory[]>> {
    return this.reqService.get<ApiResult<QuizCategory[]>>('content-group/source/quiz/quiz-category/list', {
      params,
    });
  }

  getQuizListByCategorySlug(params: object, categorySlug: string): Observable<ApiResult<Quiz[]>> {
    return this.reqService.get<ApiResult<Quiz[]>>('content-group/quiz/quiz-category/list', {
      params: {
        ...params,
        quizCategorySlug_filter: categorySlug,
      },
    });
  }

  getQuizBySlug(quizSlug: string): Observable<ApiResult<KesmaQuiz>> {
    return this.reqService.get<ApiResult<KesmaQuiz>>(`content-group/quiz/${quizSlug}`, {});
  }
}
