import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';
import { QuizService } from '../quiz.service';
import { inject } from '@angular/core';
import { ApiResult, Quiz } from '@trendency/kesma-ui';
import { catchError } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { throwError } from 'rxjs';

export const quizDetailResolver: ResolveFn<ApiResult<Quiz>> = (route: ActivatedRouteSnapshot) => {
  const quizService = inject(QuizService);
  const router = inject(Router);

  const { quizSlug } = route.params;

  return quizService.getQuizBySlug(quizSlug).pipe(
    catchError((error: HttpErrorResponse) => {
      router.navigate(['/404'], { skipLocationChange: true }).then();
      return throwError(() => error);
    })
  );
};
