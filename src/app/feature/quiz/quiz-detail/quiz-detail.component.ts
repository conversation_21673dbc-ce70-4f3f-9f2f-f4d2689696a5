import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { ApiResponseMetaList, ApiResult, createCanonicalUrlForPageablePage, Quiz } from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import { defaultMetaInfo, QuizComponent } from '../../../shared';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { BlockTitleRowComponent } from '../../../shared';
import { SeoService } from '@trendency/kesma-core';

@Component({
  selector: 'app-quiz-detail',
  templateUrl: './quiz-detail.component.html',
  styleUrl: './quiz-detail.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [QuizComponent, SidebarComponent, BlockTitleRowComponent],
})
export class QuizDetailComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);

  readonly routeData = toSignal<ApiResult<Quiz, ApiResponseMetaList>>(
    this.route.data.pipe(
      map(({ data: routeResult }) => {
        this.setMetaData(routeResult.data.title);
        return routeResult;
      })
    )
  );

  private setMetaData(quizTitle: string): void {
    const { categorySlug, quizSlug } = this.route.snapshot.params;
    const canonical = createCanonicalUrlForPageablePage(`kvizek/${categorySlug}/${quizSlug}`);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = `${quizTitle} | Magyar Nemzet`;
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    });
  }
}
