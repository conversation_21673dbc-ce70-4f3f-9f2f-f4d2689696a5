import { Inject, Injectable, Optional } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, switchMap, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import type { Response } from 'express';
import { RESPONSE, UtilService } from '@trendency/kesma-core';
import { TagService } from '../../shared';
import { ApiListResult, ApiResult, ArticleCard, RedirectService, Tag } from '@trendency/kesma-ui';

export interface TagsResolverResponse {
  articles: ApiListResult<ArticleCard>;
  tag: ApiResult<Tag>;
}

@Injectable()
export class TagListResolver {
  constructor(
    private readonly tagService: TagService,
    private readonly router: Router,
    @Inject(RESPONSE) @Optional() private readonly response: Response,
    private readonly utilsService: UtilService,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<TagsResolverResponse> {
    const slug = route.params['slug'];
    const page = route.queryParams['page'] ? route.queryParams['page'] - 1 : 0;
    return this.tagService
      .getTag(slug)
      .pipe(
        switchMap((tag) => {
          return this.tagService.getTagsList(slug, page).pipe(
            map((articles) => ({ articles, tag })),
            tap(({ articles }) => {
              if (this.redirectService.shouldBeRedirect(page, articles?.data)) {
                this.redirectService.redirectOldUrl(`cimke/${slug}`, false, 302);
              }
              if (articles.data.length) {
                return;
              }
              if (!articles.meta?.['redirect']?.tag?.slug) {
                return;
              }
              // if redirect needed (merged tag)
              const redirectUrl = `${environment.siteUrl}/cimke/${articles.meta['redirect'].tag.slug}`;

              // client side (basic redirection)
              if (this.utilsService.isBrowser()) {
                this.router.navigateByUrl(redirectUrl);
                return;
              }

              // server side (SSR - 301 redirect with express js response injector)
              this.response.status(301);
              this.response.setHeader('location', redirectUrl);
            })
          );
        })
      )
      .pipe(
        catchError((error) => {
          this.router
            .navigate(['/', '404'], {
              state: { errorResponse: JSON.stringify(error) },
              skipLocationChange: true,
            })
            .then();
          return throwError(() => error);
        })
      );
  }
}
