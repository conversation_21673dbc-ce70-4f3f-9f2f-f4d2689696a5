import { Routes } from '@angular/router';
import { TagListComponent } from './tag-list.component';
import { TagListResolver } from './tag-list.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const TAG_LIST_ROUTES: Routes = [
  {
    path: '',
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    component: TagListComponent,
    providers: [TagListResolver],
    resolve: { articles: TagListResolver },
    canActivate: [PageValidatorGuard],
  },
];
