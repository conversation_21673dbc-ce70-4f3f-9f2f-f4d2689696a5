@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: center;
}

mno-block-title-row::ng-deep {
  .block-title {
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;
    letter-spacing: 0.015em;
    text-align: left;
    margin: 24px 0;
  }
}

.tags-list {
  gap: 24px;
}

mno-block-title-row::ng-deep {
  .block-title {
    color: var(--kui-slate-950);

    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 26px;
    letter-spacing: 0.3px;
  }

  margin-bottom: 25px;
}

mno-block-title-row + article + .tags-ad {
  margin-top: 24px;
}
