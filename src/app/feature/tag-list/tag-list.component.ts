import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, SlicePipe } from '@angular/common';
import { ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  ArticleCard,
  NEWSLETTER_COMPONENT_TYPE,
  PortalConfigSetting,
  Tag,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { Observable, Subject, takeUntil } from 'rxjs';
import { map } from 'rxjs/operators';
import { ArticleCardComponent, ArticleCardType, BlockTitleRowComponent, PagerComponent } from 'src/app/shared';
import { EbHeaderLogoComponent } from 'src/app/shared/components/eb/eb-header-logo/eb-header-logo.component';
import { PageNewsletterBannerComponent } from 'src/app/shared/components/page-newsletter-banner/page-newsletter-banner.component';

import { PortalConfigService } from 'src/app/shared/services/portal-config.service';
import { ConfigService, defaultMetaInfo } from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';

@Component({
  selector: 'app-tag-list',
  templateUrl: './tag-list.component.html',
  styleUrls: ['./tag-list.component.scss'],
  imports: [
    EbHeaderLogoComponent,
    BlockTitleRowComponent,
    NgIf,
    NgFor,
    ArticleCardComponent,
    AdvertisementAdoceanComponent,
    AsyncPipe,
    SidebarComponent,
    PagerComponent,
    PageNewsletterBannerComponent,
    SlicePipe,
  ],
})
export class TagListComponent implements OnInit, OnDestroy {
  BRANDING_BOX_CONTENT_TYPE = 'branding_box';
  articles: ArticleCard[];
  slug: string;
  rowAllCount: number;
  rowOnPageCount: number;
  rowFrom: number;
  titleRow = {
    text: '',
  };
  tag: Tag;
  readonly ArticleCardType = ArticleCardType;
  ads: AdvertisementsByMedium = {} as AdvertisementsByMedium;
  isMobile$: Observable<boolean>;
  isEBEnabled = false;
  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly cd: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly analyticsService: AnalyticsService,
    private readonly configService: ConfigService,
    private readonly portalConfigService: PortalConfigService
  ) {
    this.isEBEnabled = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS);
  }

  ngOnInit(): void {
    this.isMobile$ = this.configService.isMobile$;
    this.route.data.pipe(takeUntil(this.destroy$)).subscribe((res: any) => {
      if (!res?.articles) {
        return;
      }
      this.articles = res?.articles?.articles?.data;
      this.slug = this.route?.snapshot?.params?.['slug'];
      this.tag = res?.articles?.tag?.data;
      this.titleRow = {
        text: (this.tag ? this.tag.title : this.slug) as string,
      };
      const limitable = res?.articles?.articles?.meta?.limitable;
      this.rowAllCount = limitable?.rowAllCount;
      this.rowOnPageCount = limitable?.rowOnPageCount;
      this.rowFrom = limitable?.rowFrom;

      const canonical = createCanonicalUrlForPageablePage('cimke', this.route.snapshot);
      if (canonical) {
        this.seo.updateCanonicalUrl(canonical);
      }
      const title = `${this.titleRow.text} | Magyar Nemzet`;
      this.seo.setMetaData({
        ...defaultMetaInfo,
        title,
        ogTitle: title,
        // eslint-disable-next-line max-len
        ogDescription: `${this.titleRow.text} címke oldal legfrissebb tartalmai a Magyar Nemzet oldalán. Friss hírek, képek, videók, vélemények és aktuális cikkek.${this.tag.title} témakörben.`,
        // eslint-disable-next-line max-len
        description: `${this.titleRow.text} címke oldal legfrissebb tartalmai a Magyar Nemzet oldalán. Friss hírek, képek, videók, vélemények és aktuális cikkek.`,
      });
      this.cd.detectChanges();
    });

    this.adStore.advertisemenets$
      .pipe(takeUntil(this.destroy$))
      .pipe(map((ads: Advertisement[]) => this.adStore.separateAdsByMedium(ads)))
      .subscribe((ads: AdvertisementsByMedium) => {
        this.ads = ads;
        this.cd.markForCheck();
      });
  }

  trackByFn(index: number, item: ArticleCard): string {
    return item.id ?? item.slug ?? index?.toString();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSubscriptionClick(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.LAYOUT);
  }
}
