import { inject } from '@angular/core';
import { Layout } from '@trendency/kesma-ui';
import { MedalTable } from '@trendency/kesma-ui/lib/components/olimpia/olimpia-medal-table/olimpia-medal-table.definitions';
import { forkJoin, Observable, take } from 'rxjs';
import { OlympicImportantSidebarService } from 'src/app/shared/services/olympic-important-sidebar.service';
import { OlympicsMedalTableService } from '../services/olympics-medal-table.service';

export function OlympicsMedalTableResolver(): Observable<{
  medalTable: MedalTable;
  olympicImportantSidebar: Layout | null;
}> {
  const YEAR = 2024;
  return forkJoin({
    medalTable: inject(OlympicsMedalTableService).getMedalTable(YEAR),
    olympicImportantSidebar: inject(OlympicImportantSidebarService).getLayout().pipe(take(1)),
  });
}
