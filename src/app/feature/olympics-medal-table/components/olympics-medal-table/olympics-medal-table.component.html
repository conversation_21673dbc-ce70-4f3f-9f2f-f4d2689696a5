<section>
  <div class="wrapper">
    <app-olympics-header></app-olympics-header>
  </div>
  <div class="wrapper with-aside main-content">
    <div class="left-column" *ngIf="medalTable$ | async as data">
      <kesma-olimpia-page-banner></kesma-olimpia-page-banner>
      <h1 class="table-title">Éremtáblázat</h1>
      <kesma-olimpia-medal-table [styleID]="OlimpicPortalEnum.OlimpicMNO" [data]="data" [showAllItems]="true"> </kesma-olimpia-medal-table>
      <kesma-olimpia-navigator [navigationLink]="['/', 'olimpia-2024']" [styleID]="OlimpicPortalEnum.OlimpicMNO"> </kesma-olimpia-navigator>
      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_1 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>
    </div>

    <aside>
      <app-layout
        *ngIf="olympicImportantSidebarLayout$ | async as layout"
        [adPageType]="adPageType"
        [configuration]="layout.content"
        [layoutType]="LayoutPageType.SIDEBAR"
        [structure]="layout.struct"
        class="olympic-important-sidebar"
        secondary_pageType="column_sport_all_articles_and_sub_pages"
      ></app-layout>
    </aside>
  </div>
</section>
