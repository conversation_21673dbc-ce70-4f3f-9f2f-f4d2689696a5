import { AfterViewInit, Component, ElementRef, inject, OnDestroy } from '@angular/core';
import { ActivatedRoute, Data } from '@angular/router';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { AnalyticsService, EBPortalEnum, KesmaEbNavigatorComponent, LayoutApiData, LayoutPageType, PortalConfigSetting } from '@trendency/kesma-ui';
import { map, Observable } from 'rxjs';
import { defaultMetaInfo, PortalConfigService } from '../../shared';
import { AsyncPipe, DOCUMENT, NgIf } from '@angular/common';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { NewsletterPopupComponent } from 'src/app/shared/components/newsletter-popup/newsletter-popup.component';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  imports: [LayoutComponent, AsyncPipe, NgIf, NewsletterPopupComponent, KesmaEbNavigatorComponent],
})
export class HomeComponent implements AfterViewInit, OnDestroy {
  private readonly document = inject(DOCUMENT);

  layoutApiData$: Observable<LayoutApiData> = this.route.data.pipe(
    map((data: Data | { layoutData: LayoutApiData }) => {
      this.seo.updateCanonicalUrl('', { skipSeoMetaCheck: true });
      this.seo.setMetaData(
        {
          ...defaultMetaInfo,
        },
        { skipSeoMetaCheck: true }
      );
      return data.layoutData;
    })
  );

  LayoutPageType = LayoutPageType;
  EBPortalEnum = EBPortalEnum;
  isEBEnabled = false;

  private links: HTMLCollectionOf<HTMLAnchorElement>;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly utils: UtilService,
    private readonly analytics: AnalyticsService,
    private readonly elementRef: ElementRef,
    private readonly portalConfigService: PortalConfigService
  ) {
    this.seo.updateCanonicalUrl('', { skipSeoMetaCheck: true });
    this.isEBEnabled = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS);
  }

  ngAfterViewInit(): void {
    if (!this.utils.isBrowser()) {
      return;
    }

    queueMicrotask(() => {
      this.links = this.elementRef.nativeElement.getElementsByTagName('a');
      Array.from(this.links ?? []).forEach((element) => {
        element.addEventListener('click', this.handleAnchorClickEvent.bind(this));
        element.addEventListener('auxclick', this.handleAnchorClickEvent.bind(this));
      });
    });
  }

  ngOnDestroy(): void {
    if (!this.utils.isBrowser()) {
      return;
    }

    Array.from(this.links ?? []).forEach((element) => {
      element.removeEventListener('click', this.handleAnchorClickEvent.bind(this));
      element.removeEventListener('auxclick', this.handleAnchorClickEvent.bind(this));
    });
  }

  private handleAnchorClickEvent(mouseEvent: MouseEvent): void {
    const link = (mouseEvent.composedPath().find((elem: EventTarget) => (elem as Element).nodeName === 'A') as HTMLAnchorElement)?.href;
    link && this.analytics.sendMainPageClick(link, this.document.referrer);
  }
}
