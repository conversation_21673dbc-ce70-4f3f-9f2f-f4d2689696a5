import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { LayoutApiData, LayoutService } from '@trendency/kesma-ui';
import { catchError, map } from 'rxjs/operators';
import { HttpParams } from '@angular/common/http';

@Injectable()
export class HomeResolver {
  constructor(
    private readonly layoutService: LayoutService,
    private readonly router: Router
  ) {}

  resolve(): Observable<LayoutApiData> {
    const params: HttpParams = new HttpParams({
      fromObject: { 'fields[]': ['foundationTagSlug', 'foundationTagTitle'] },
    });
    return this.layoutService.getHomePage(params).pipe(
      catchError((err) => {
        this.router.navigate(['/', '500'], {
          skipLocationChange: true,
        });
        return throwError(err);
      }),
      map(({ data }) => data)
    );
  }
}
