@use 'shared' as *;

.wrapper {
  max-width: $layout-max-width;
  width: 100%; // calc(100% - #{$side-padding * 2});
  margin-inline: auto;

  @include media-breakpoint-down(xs) {
    width: calc(100% - #{$mobile-side-padding * 2});
  }

  ::ng-deep {
    kesma-layout {
      /**
      Moved from layout component as it is problematic to use it everywhere. It is only needed for the homepage.
      Originated from feat(KESMA-11802): mobile & layout fixes
       */
      @include media-breakpoint-down(sm) {
        .col-12 {
          margin-bottom: 16px;
        }

        > .row-element {
          > .horizontal:nth-child(1) {
            order: 2;
          }

          > .horizontal:nth-child(2) {
            order: 1;
          }

          > .horizontal:nth-child(1).col-lg-8,
          > .horizontal:nth-child(1).col-lg-6 {
            order: 1;

            & + div {
              order: 2;
            }
          }

          > .horizontal:nth-child(1).col-lg-4 + .col-lg-8 {
            order: 3;
          }

          > .horizontal:nth-child(n + 3) {
            order: 3;
          }
        }
      }
    }
  }
}

kesma-eb-navigator {
  margin-bottom: 30px;
}
