import { forkJoin, take, throwError } from 'rxjs';
import { Layout, OlimpiaMedalsNational } from '@trendency/kesma-ui';
import { ApiService, OlympicImportantSidebarService } from '../../../shared';
import { inject } from '@angular/core';
import { catchError, map } from 'rxjs/operators';
import { ResolveFn, Router } from '@angular/router';

export const olympicsNationalMedalsResolver: ResolveFn<{
  medals: OlimpiaMedalsNational[];
  olympicImportantSidebar: Layout | null;
}> = () => {
  const router = inject(Router);
  const apiService = inject(ApiService);
  return forkJoin({
    medals: apiService.getOlimpiaNationalMedals().pipe(
      map(({ data }) => data),
      catchError((err) => {
        router.navigate(['/404'], { skipLocationChange: true }).then();
        return throwError(() => err);
      })
    ),
    olympicImportantSidebar: inject(OlympicImportantSidebarService).getLayout().pipe(take(1)),
  });
};
