<section>
  <div class="wrapper">
    <app-olympics-header></app-olympics-header>
  </div>
  <div class="wrapper with-aside main-content">
    <div class="left-column">
      <kesma-olimpia-page-banner></kesma-olimpia-page-banner>
      <h2 class="title">Di<PERSON><PERSON>ségtábla</h2>

      <kesma-olimpia-national-medals-table
        [data]="tableData$ | async"
        [showNoMedalsButton]="false"
        [styleID]="OlimpicPortalEnum.OlimpicMNO"
      ></kesma-olimpia-national-medals-table>

      <kesma-olimpia-navigator [styleID]="OlimpicPortalEnum.OlimpicMNO" [navigationLink]="['/', 'olimpia-2024']"></kesma-olimpia-navigator>
      <div class="ads">
        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.roadblock_3 as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg)',
            padding: 'var(--ad-padding)',
          }"
        >
        </kesma-advertisement-adocean>
      </div>
    </div>

    <aside>
      <app-layout
        *ngIf="olympicImportantSidebarLayout$ | async as layout"
        [adPageType]="adPageType"
        [configuration]="layout.content"
        [layoutType]="LayoutPageType.SIDEBAR"
        [structure]="layout.struct"
        class="olympic-important-sidebar"
        secondary_pageType="column_sport_all_articles_and_sub_pages"
      ></app-layout>
    </aside>
  </div>

  <div class="ads">
    <kesma-advertisement-adocean
      *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
      [style]="{
        margin: 'var(--ad-margin)',
        background: 'var(--ad-bg)',
        padding: 'var(--ad-padding)',
      }"
      [ad]="ad"
    >
    </kesma-advertisement-adocean>
  </div>
</section>
