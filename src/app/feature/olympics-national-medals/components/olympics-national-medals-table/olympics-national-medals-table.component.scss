@use '../../../../../scss/shared.scss' as *;

:host {
  display: block;
  ::ng-deep {
    .content-element {
      margin-bottom: 0 !important;
    }
  }
  .wrapper {
    gap: 24px;
    @include media-breakpoint-down(md) {
      padding: 0 $mobile-side-padding;
    }
  }
  .main-content {
    margin-top: 0;

    @include media-breakpoint-down(lg) {
      flex-direction: column;

      .left-column,
      aside {
        width: 100%;
      }
    }

    .left-column {
      margin-top: 32px;
    }
  }

  .left-column {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .title {
      color: #020617;
      font-size: 32px;
      font-weight: 700;
      line-height: 40px;

      @include media-breakpoint-down(md) {
        font-size: 24px;
        line-height: 32px;
        letter-spacing: 0.24px;
        margin-top: -8px;
        margin-bottom: 8px;
      }
    }

    kesma-olimpia-national-medals-table {
      margin-bottom: 24px;

      @include media-breakpoint-down(md) {
        margin-bottom: 16px;
      }
    }

    kesma-olimpia-navigator {
      margin: 16px 0 24px;

      @include media-breakpoint-down(md) {
        margin: 4px 0 16px;
      }
    }
  }
}
