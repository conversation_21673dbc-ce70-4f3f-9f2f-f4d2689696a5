import { ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, inject, OnInit } from '@angular/core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  Layout,
  LayoutPageType,
  OlimpiaMedalsNational,
  OlimpiaNationalMedalsTableComponent,
  OlimpiaNavigatorComponent,
  OlimpiaPageBannerComponent,
  OlimpicPortalEnum,
  PAGE_TYPES,
} from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { map } from 'rxjs/operators';
import { OlympicsHeaderComponent } from 'src/app/shared/components/olympics-header/olympics-header.component';
import { LayoutComponent } from 'src/app/feature/layout/components/layout/layout.component';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-olympics-national-medals-table',
  templateUrl: './olympics-national-medals-table.component.html',
  styleUrl: './olympics-national-medals-table.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AdvertisementAdoceanComponent,
    OlympicsHeaderComponent,
    OlimpiaPageBannerComponent,
    OlimpiaNationalMedalsTableComponent,
    OlimpiaNavigatorComponent,
    AsyncPipe,
    LayoutComponent,
    NgIf,
  ],
})
export class OlympicsNationalMedalsTableComponent implements OnInit {
  adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  adverts?: AdvertisementsByMedium;

  tableData$: Observable<OlimpiaMedalsNational[]> = this.route.data.pipe(map(({ data }) => data['medals']));
  olympicImportantSidebarLayout$: Observable<Layout> = this.route.data.pipe(map(({ data }) => data['olympicImportantSidebar']));

  readonly OlimpicPortalEnum = OlimpicPortalEnum;
  protected readonly LayoutPageType = LayoutPageType;
  private readonly destroyRef = inject(DestroyRef);

  constructor(
    private readonly route: ActivatedRoute,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initAds();
  }

  protected initAds(): void {
    this.resetAds();
    this.adStore.advertisemenets$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads, this.adPageType);

      this.cdr.detectChanges();
    });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }
}
