import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, share, switchMap, take, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { ApiService, AuthorData, calculateFilters, PortalConfigService } from '../../../shared';
import { mapSocialAuthorToAuthor } from '../utils/author-mapper';
import { SeoService } from '@trendency/kesma-core';
import { ArticleCard, ArticleSearchResult, LimitableMeta, PortalConfigSetting, RedirectService, searchResultToArticleCard } from '@trendency/kesma-ui';

@Injectable()
export class AuthorPageResolver {
  constructor(
    private readonly router: Router,
    private readonly apiService: ApiService,
    private readonly portalConfigService: PortalConfigService,
    private readonly seoService: SeoService,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<{
    articles: ArticleCard[];
    limitable: LimitableMeta;
    author?: AuthorData;
  }> {
    const authorSlug = route.paramMap.get('authorSlug');

    // Redirect plural url to singular
    if (this.seoService.currentUrl.includes('szerzok')) {
      this.redirectService.redirectOldUrl(`szerzo/${authorSlug}`);
    }

    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;

    const publicAuthor$ = this.apiService.getPublicAuthorSocial(authorSlug ?? '').pipe(
      share(),
      take(1),
      map(({ data }) => mapSocialAuthorToAuthor(data))
    );

    const articlesObservable$ = publicAuthor$.pipe(
      take(1),
      switchMap((author) => {
        if (!author?.id) {
          this.router.navigate(['/', '404'], { skipLocationChange: true }).then();
          throwError(() => 'Nincs ilyen szerző');
        }
        const authorFilter = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_EXTERNAL_PUBLIC_AUTHOR_M2M)
          ? { 'author[]': author.id }
          : { author: author.publicAuthorName };
        return this.apiService
          .searchArticles(currentPage, 10, {
            ...calculateFilters(route.queryParams),
            ...authorFilter,
          })
          .pipe(
            tap(({ data }) => {
              if (this.redirectService.shouldBeRedirect(currentPage, data)) {
                this.redirectService.redirectOldUrl(`szerzo/${authorSlug}`, false, 302);
              }
            })
          );
      })
    );

    return forkJoin({
      articles: articlesObservable$,
      author: publicAuthor$,
    }).pipe(
      map(({ articles, author }) => ({
        articles: articles?.data?.map((sr: ArticleSearchResult) => searchResultToArticleCard(sr)),
        limitable: articles?.meta?.limitable,
        author,
      })),
      catchError((err) => {
        this.router.navigate(['/', '404'], { skipLocationChange: true }).then();
        return throwError(() => err);
      })
    );
  }
}
