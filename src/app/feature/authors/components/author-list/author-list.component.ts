import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { map, takeUntil } from 'rxjs/operators';
import { Observable, Subject } from 'rxjs';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  LimitableMeta,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { BackendAuthorData, defaultMetaInfo } from '../../../../shared';
import { IconComponent, PagerComponent } from 'src/app/shared';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';

@Component({
  selector: 'app-author-list',
  templateUrl: './author-list.component.html',
  styleUrls: ['./author-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AdvertisementAdoceanComponent, PagerComponent, AsyncPipe, RouterLink, IconComponent, NgForOf, NgIf],
})
export class AuthorListComponent implements OnInit, OnDestroy {
  adverts: AdvertisementsByMedium;

  authors$: Observable<BackendAuthorData[]> = this.route.data.pipe(map((res) => res['data']?.['data']));
  limitables$: Observable<LimitableMeta> = this.route.data.pipe(map((res) => res['data']?.['meta'].limitable));

  private readonly unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initAds();
    this.setMetaData();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  trackBySlug(_: number, item: BackendAuthorData): string {
    return item?.slug ?? '';
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('szerzo', this.route.snapshot);
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
    const title = `Szerzők | Magyar Nemzet`;
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    };

    this.seo.setMetaData(metaData);
  }

  private initAds(): void {
    this.adStore.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);
      this.cdr.markForCheck();
    });
  }
}
