<section class="author-list">
  <div class="wrapper">
    <h1><PERSON><PERSON><PERSON><PERSON><PERSON></h1>

    <ng-container *ngFor="let author of authors$ | async; trackBy: trackBySlug; let i = index">
      <div class="author-item">
        <div class="author-avatar">
          <img [alt]="author.public_author_name" [src]="author.avatar?.fullSizeUrl || 'assets/images/placeholder.svg'" loading="lazy" />
        </div>
        <div class="author-info">
          <h2 [routerLink]="['/', 'szerzo', author?.slug]" class="author-name">{{ author.public_author_name }}</h2>
          <div *ngIf="author.rank" class="author-rank">{{ author.rank }}</div>
        </div>
        <a [routerLink]="['/', 'szerzo', author?.slug]" class="author-more-button">
          <span>Szerző cikkei</span>
          <i mno-icon="chevron-right"></i>
        </a>
      </div>

      <div *ngIf="i === 3 && adverts?.mobile?.mobilrectangle_1 as ad" class="mobile">
        <kesma-advertisement-adocean
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            paddingTop: 'var(--ad-padding-top)',
            paddingBottom: 'var(--ad-padding-bottom)',
            background: 'var(--kui-gray-300)',
          }"
        ></kesma-advertisement-adocean>
      </div>

      <div *ngIf="i === 3 && adverts?.desktop?.roadblock_1 as ad" class="desktop">
        <kesma-advertisement-adocean
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            paddingTop: 'var(--ad-padding-top)',
            paddingBottom: 'var(--ad-padding-bottom)',
            background: 'var(--kui-gray-300)',
          }"
        ></kesma-advertisement-adocean>
      </div>

      <div *ngIf="i === 7 && adverts?.mobile?.mobilrectangle_2 as ad" class="mobile">
        <kesma-advertisement-adocean
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            paddingTop: 'var(--ad-padding-top)',
            paddingBottom: 'var(--ad-padding-bottom)',
            background: 'var(--kui-gray-300)',
          }"
        ></kesma-advertisement-adocean>
      </div>

      <div *ngIf="i === 7 && adverts?.desktop?.roadblock_2 as ad" class="desktop">
        <kesma-advertisement-adocean
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            paddingTop: 'var(--ad-padding-top)',
            paddingBottom: 'var(--ad-padding-bottom)',
            background: 'var(--kui-gray-300)',
          }"
        ></kesma-advertisement-adocean>
      </div>

      <div *ngIf="i === 11 && adverts?.mobile?.mobilrectangle_3 as ad" class="mobile">
        <kesma-advertisement-adocean
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            paddingTop: 'var(--ad-padding-top)',
            paddingBottom: 'var(--ad-padding-bottom)',
            background: 'var(--kui-gray-300)',
          }"
        ></kesma-advertisement-adocean>
      </div>

      <div *ngIf="i === 11 && adverts?.desktop?.roadblock_3 as ad" class="desktop">
        <kesma-advertisement-adocean
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            paddingTop: 'var(--ad-padding-top)',
            paddingBottom: 'var(--ad-padding-bottom)',
            background: 'var(--kui-gray-300)',
          }"
        ></kesma-advertisement-adocean>
      </div>

      <div *ngIf="i === 15 && adverts?.mobile?.mobilrectangle_4 as ad" class="mobile">
        <kesma-advertisement-adocean
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            paddingTop: 'var(--ad-padding-top)',
            paddingBottom: 'var(--ad-padding-bottom)',
            background: 'var(--kui-gray-300)',
          }"
        ></kesma-advertisement-adocean>
      </div>

      <div *ngIf="i === 15 && adverts?.desktop?.roadblock_4 as ad" class="desktop">
        <kesma-advertisement-adocean
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            paddingTop: 'var(--ad-padding-top)',
            paddingBottom: 'var(--ad-padding-bottom)',
            background: 'var(--kui-gray-300)',
          }"
        ></kesma-advertisement-adocean>
      </div>
    </ng-container>
    <ng-container *ngIf="limitables$ | async as limitable">
      <mno-pager
        *ngIf="limitable?.pageMax! > 0"
        [allowAutoScrollToTop]="true"
        [hasSkipButton]="true"
        [isListPager]="true"
        [maxDisplayedPages]="5"
        [rowAllCount]="limitable?.rowAllCount!"
        [rowOnPageCount]="limitable?.rowOnPageCount!"
      ></mno-pager>
    </ng-container>
  </div>
</section>
