@use 'shared' as *;

.author {
  &-list {
    > .wrapper {
      margin: 24px auto;

      @include media-breakpoint-down(sm) {
        width: calc(100% - 32px);
      }
    }

    h1 {
      font-family: var(--kui-font-condensed);
      font-size: 32px;
      line-height: 40px;
    }

    mno-pager {
      display: block;
      margin: 24px 0;
    }
  }

  &-item {
    border-bottom: 1px solid var(--kui-blue-900);
    padding: 24px 0;
    display: flex;
    align-items: center;
    gap: 20px;

    @include media-breakpoint-only(sm) {
      flex-wrap: wrap;
    }

    @include media-breakpoint-down(xs) {
      padding: 20px 0;
    }
  }

  &-avatar {
    img {
      border-radius: 50%;
      min-width: 100px;
      width: 100px;
      height: 100px;
      object-fit: cover;

      @include media-breakpoint-down(sm) {
        min-width: 80px;
        width: 80px;
        height: 80px;
      }
    }
  }

  &-info {
    @include media-breakpoint-only(sm) {
      width: calc(100% - 120px);
    }
  }

  &-name {
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;
    cursor: pointer;
  }

  &-rank {
    color: var(--kui-blue-900);
    line-height: 18px;
    font-size: 14px;
    font-weight: 500;
  }

  &-more-button {
    margin-left: auto;
    align-self: flex-end;
    display: flex;
    align-items: center;
    font-size: 16px;
    line-height: 20px;
    margin-right: 4px;
    color: var(--kui-blue-500);
    gap: 5px;
    flex-shrink: 0;

    i {
      min-width: 16px;
      width: 16px;
      height: 16px;
    }

    @include media-breakpoint-down(sm) {
      align-self: center;

      span {
        display: none;
      }

      i {
        min-width: 32px;
        width: 32px;
        height: 32px;
      }
    }
  }
}
