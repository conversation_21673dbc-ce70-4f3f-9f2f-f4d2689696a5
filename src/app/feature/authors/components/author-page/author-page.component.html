<section class="author-page">
  <div *ngIf="author" class="wrapper top-wrapper">
    <div [ngClass]="{ center: !author.rank && !author.publicAuthorDescription }" class="author-data">
      <div class="author-avatar">
        <img [alt]="author.public_author_name" [src]="author.avatar?.fullSizeUrl || 'assets/images/placeholder.svg'" loading="lazy" />
      </div>
      <div class="author-info">
        <h1 class="author-name">{{ author.public_author_name }}</h1>
        <div *ngIf="author.rank" class="author-rank">{{ author.rank }}</div>
        <div *ngIf="author.publicAuthorDescription" class="author-description">
          <div class="author-description-title">Szakmai be<PERSON>tat<PERSON></div>
          <div class="author-description-content">{{ author.publicAuthorDescription }}</div>
        </div>
      </div>
    </div>

    <app-search-filter (filterEvent)="onSearch($event)" [showAuthorFilter]="false" [showPublishDateSort]="true"></app-search-filter>

    <div *ngIf="portalConfigService.isConfigSet(PortalConfigSetting.SHOW_NUMBER_OF_ARTICLES_BY_AUTHORS_ENABLED)" class="author-page-count">
      <strong>{{ limitable?.rowAllCount ?? 0 }} db</strong> cikk összesen
    </div>

    <div *ngIf="firstArticleDate" class="author-page-date-group first">{{ firstArticleDate }}</div>
    <div *ngIf="articles[0]" class="first-article">
      <article
        [asResult]="(isMobile$ | async) === true"
        [data]="articles[0]"
        [showThumbnail]="!!articles[0]?.thumbnail"
        [styleID]="(isMobile$ | async) ? ArticleCardType.Img16TopTagsTitleLeadBadge : ArticleCardType.DateImgRightTagsTitleLeadWideLarge"
        mno-article-card
      ></article>
    </div>
  </div>
  <div *ngIf="author" class="wrapper articles-wrapper">
    <div class="articles">
      <ng-container *ngFor="let dateGroup of articlesByDate">
        <hr *ngIf="firstArticleDate === dateGroup.date" class="list-separator" />
        <div *ngIf="firstArticleDate !== dateGroup.date" class="author-page-date-group">{{ dateGroup.date }}</div>
        <ng-container *ngFor="let article of dateGroup.articles; let i = index; let isLast = last">
          <article
            [asResult]="(isMobile$ | async) === true"
            [data]="article"
            [showThumbnail]="!!article?.thumbnailUrl || !!article?.thumbnailUrl43 || !!article?.thumbnail?.url"
            [styleID]="(isMobile$ | async) ? ArticleCardType.ImgRightTagsTitleLeadBadge : ArticleCardType.DateImgRightTagsTitleLeadWide"
            mno-article-card
          ></article>

          <hr *ngIf="!isLast" class="list-separator" />

          <!-- ADS -->
          <ng-container *ngIf="i === 3">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_1 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>
            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>
          </ng-container>

          <ng-container *ngIf="i === 8">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_2 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>
          </ng-container>

          <ng-container *ngIf="i === 13">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_3 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>
          </ng-container>

          <ng-container *ngIf="i === 18">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_4 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_4 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>
          </ng-container>

          <!-- ADS -->
        </ng-container>
      </ng-container>
    </div>

    <mno-pager
      *ngIf="limitable?.pageMax! > 0"
      [allowAutoScrollToTop]="true"
      [hasSkipButton]="true"
      [isListPager]="true"
      [maxDisplayedPages]="5"
      [rowAllCount]="limitable?.rowAllCount!"
      [rowOnPageCount]="limitable?.rowOnPageCount!"
    ></mno-pager>
  </div>
</section>
