@use 'shared' as *;

.author {
  &-page {
    > .wrapper {
      &.top-wrapper {
        margin-top: 24px;
      }

      &.articles-wrapper {
        margin-bottom: 24px;
      }

      @include media-breakpoint-down(sm) {
        width: calc(100% - 32px);
      }
    }

    &-date-group {
      font-weight: 700;
      font-size: 24px;
      line-height: 28px;
      margin: 40px 0 30px;
    }

    mno-pager {
      display: block;
      margin: 30px 0;
    }

    hr {
      border-width: 1px 0 0 0;
      border-color: var(--kui-slate-300);
      margin: 24px 0;
    }
  }

  &-data {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--kui-blue-900);

    @include media-breakpoint-down(sm) {
      flex-direction: column;
    }

    &.center {
      align-items: center;
    }
  }

  &-avatar {
    img {
      border-radius: 50%;
      min-width: 120px;
      width: 120px;
      height: 120px;
      object-fit: cover;
    }
  }

  &-name {
    font-size: 36px;
    font-weight: 700;
    line-height: 44px;
    font-family: var(--kui-font-secondary);
    letter-spacing: 0;
  }

  &-rank {
    color: var(--kui-blue-900);
    line-height: 22px;
    font-size: 18px;
    font-weight: 500;
  }

  &-description {
    border-top: 1px dotted var(--kui-slate-300);
    margin-top: 20px;
    padding-top: 20px;
    font-size: 18px;
    line-height: 27px;

    &-title {
      font-weight: 700;
    }

    &-content {
      padding: 10px 0;
    }
  }
}
