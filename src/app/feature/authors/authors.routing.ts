import { Routes } from '@angular/router';
import { AuthorListComponent } from './components/author-list/author-list.component';
import { AuthorListResolver } from './api/author-list.resolver';
import { AuthorPageComponent } from './components/author-page/author-page.component';
import { AuthorPageResolver } from './api/author-page.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const AUTHOR_ROUTES: Routes = [
  {
    path: '',
    component: AuthorListComponent,
    pathMatch: 'full',
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    providers: [AuthorListResolver],
    resolve: {
      data: AuthorListResolver,
    },
    canActivate: [PageValidatorGuard],
  },
  {
    path: ':authorSlug',
    component: AuthorPageComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    providers: [AuthorPageResolver],
    resolve: {
      data: AuthorPageResolver,
    },
    canActivate: [PageValidatorGuard],
  },
];
