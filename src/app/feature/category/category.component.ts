import { ChangeDetectionStrategy, Component, ElementRef, HostBinding, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  ArticleCard,
  LayoutApiData,
  LayoutPageType,
  LimitableMeta,
  NEWSLETTER_COMPONENT_TYPE,
  PAGE_TYPES,
  PortalConfigSetting,
  PublicAuthor,
} from '@trendency/kesma-ui';
import { combineLatest, Observable, of, Subject } from 'rxjs';
import { map, switchMap, tap } from 'rxjs/operators';
import { BayerBlogService, ConfigService, defaultMetaInfo } from '../../shared';
import { PortalConfigService } from 'src/app/shared/services/portal-config.service';
import { ArticleCardComponent, ArticleCardType, OpinionCardComponent, OpinionCardType, PagerComponent } from 'src/app/shared';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { AsyncPipe, NgClass, NgFor, NgIf, SlicePipe } from '@angular/common';
import { OlympicsHeaderComponent } from 'src/app/shared/components/olympics-header/olympics-header.component';
import { ModuleHeadingComponent } from 'src/app/shared/components/module-heading/module-heading.component';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { PageNewsletterBannerComponent } from 'src/app/shared/components/page-newsletter-banner/page-newsletter-banner.component';
import { CpacComponent } from 'src/app/shared/components/cpac/cpac.component';
import { DateFnsModule } from 'ngx-date-fns';

@Component({
  selector: 'app-category',
  templateUrl: './category.component.html',
  styleUrls: ['./category.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AdvertisementAdoceanComponent,
    PagerComponent,
    ArticleCardComponent,
    LayoutComponent,
    AsyncPipe,
    NgIf,
    NgFor,
    OlympicsHeaderComponent,
    SlicePipe,
    ModuleHeadingComponent,
    SidebarComponent,
    OpinionCardComponent,
    NgClass,
    PageNewsletterBannerComponent,
    CpacComponent,
    RouterLink,
    DateFnsModule,
  ],
})
export class CategoryComponent implements OnInit, OnDestroy {
  @ViewChild('articleList') readonly articleList: ElementRef<HTMLDivElement>;

  readonly ArticleCardType = ArticleCardType;

  categoryTitle = '';
  public excludedIds: string[] = [];
  adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  destroy$: Subject<boolean> = new Subject<boolean>();
  isMobile$: Observable<boolean>;
  currentSlug: string;
  readonly LayoutPageTypes = LayoutPageType;

  medium_rectangle_1_tablet?: Advertisement;
  medium_rectangle_2_tablet?: Advertisement;
  medium_rectangle_3_tablet?: Advertisement;
  medium_rectangle_4_tablet?: Advertisement;
  @HostBinding('class.is-bayer-blog') isBayerBlog = false;
  public pageData$: Observable<{
    columnTitle: string;
    columnSlug: string;
    layoutData: LayoutApiData;
    excludedIds: string[];
  }> = this.route.data.pipe(
    map((res: any) => {
      this.currentSlug = res?.['pageData']?.['slug'] || '';
      return {
        columnTitle: res['pageData']['columnTitle'],
        columnSlug: res['pageData']['slug'],
        layoutData: res['pageData']['layoutApiResponse'],
        excludedIds: res['pageData']['excludedIds'],
      };
    })
  );
  articles$: Observable<ArticleCard[]> = this.route.data.pipe(
    map(
      ({
        pageData: {
          category: { data },
        },
      }) => {
        return data;
      }
    )
  );
  limitables$: Observable<LimitableMeta> = this.route.data.pipe(
    map(
      ({
        pageData: {
          category: { meta },
        },
      }) => meta.limitable
    )
  );
  ads$: Observable<AdvertisementsByMedium> = this.route.data.pipe(
    tap((res: any) => {
      this.adPageType = `column_${res.pageData?.slug}`;
      this.categoryTitle = res.pageData?.columnTitle;
      this.adStore.setArticleParentCategory(this.adPageType);

      setTimeout(() => {
        this.analyticsService.sendPageView({
          pageCategory: res.pageData?.slug,
        });
      }, 0);

      this.setMetaData();
    }),
    switchMap(() => this.adStore.advertisemenets$),
    map((ads) => {
      const allAds = this.adStore.separateAdsByMedium(ads, this.adPageType);

      this.medium_rectangle_1_tablet = { ...allAds?.mobile?.['mobilrectangle_1'], medium: 'desktop' };
      this.medium_rectangle_2_tablet = { ...allAds?.mobile?.['mobilrectangle_2'], medium: 'desktop' };
      this.medium_rectangle_3_tablet = { ...allAds?.mobile?.['mobilrectangle_3'], medium: 'desktop' };
      this.medium_rectangle_4_tablet = { ...allAds?.mobile?.['mobilrectangle_3'], medium: 'desktop' };

      return allAds;
    })
  );
  allData$ = combineLatest({
    pageData: this.pageData$,
    articles: this.articles$,
    limitable: this.limitables$,
    ads: this.ads$,
  });
  hasLayout$ = this.allData$.pipe(map(({ pageData }) => !!pageData?.layoutData?.content?.length));
  isBayerBlog$ = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_BAYER_ZSOLT_BLOG_COMPONENTS)
    ? this.pageData$
        .pipe(takeUntilDestroyed())
        .pipe(map(({ columnSlug }) => this.bayerBlogService.isBayerBlogColumn(columnSlug)))
        .pipe(tap((value) => (this.isBayerBlog = value)))
    : of(false);
  public authorBayerZsolt: PublicAuthor;
  protected readonly OpinionCardType = OpinionCardType;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly analyticsService: AnalyticsService,
    private readonly configService: ConfigService,
    private readonly portalConfigService: PortalConfigService,
    private readonly bayerBlogService: BayerBlogService
  ) {}

  @HostBinding('class.olympics-2024')
  get isOlympics2024(): boolean {
    return this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_OLYMPICS_ELEMENTS) && this.currentSlug === 'sport';
  }

  get isEbEnabled(): boolean {
    return !!(this.currentSlug === 'sport' && this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS));
  }

  ngOnInit(): void {
    this.isMobile$ = this.configService.isMobile$;
  }

  setMetaData(): void {
    if (!this.categoryTitle) {
      return;
    }
    if (this.seo.currentUrl) {
      this.seo.updateCanonicalUrl(this.seo.currentUrl, {
        skipSeoMetaCheck: true,
        addHostUrl: false,
      });
    }

    const isEnglish = this.currentSlug === 'english';

    const title = `${this.categoryTitle} rovat | Magyar Nemzet`;
    const description = isEnglish
      ? `Explore the latest news happening around Hungary. Discover the
    daily top headlines and breaking events that are happening in Hungary and the world each day with Magyar Nemzet since 1938.`
      : `${this.categoryTitle} rovat legfrissebb tartalmai a Magyar Nemzet oldalán.
    Friss hírek, képek, videók, vélemények és aktuális cikkek különböző izgalmas témakörökben.`;
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
      // eslint-disable-next-line max-len
      ogDescription: description,
      description,
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
    this.adStore.setArticleParentCategory('');
  }

  onSubscriptionClick(): void {
    this.analyticsService.newsLetterSubscriptionClicked(NEWSLETTER_COMPONENT_TYPE.LAYOUT);
  }
}
