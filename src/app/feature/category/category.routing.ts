import { Routes } from '@angular/router';
import { CategoryComponent } from './category.component';
import { CategoryResolver } from './category.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const CATEGORY_ROUTES: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: CategoryComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    providers: [CategoryResolver],
    resolve: {
      pageData: CategoryResolver,
    },
    canActivate: [PageValidatorGuard],
  },
  {
    // Alrovat oldal – aut. listázó
    path: ':subCategorySlug',
    component: CategoryComponent,
    providers: [CategoryResolver],
  },
];
