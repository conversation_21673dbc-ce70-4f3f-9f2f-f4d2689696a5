@use 'shared' as *;

:root {
  .main-container.is-bayer-blog {
    mno-opinion-card.style-AuthorOpinionHeader::ng-deep {
      padding-top: 0;
    }
  }
}

.wrapper.olympics-2024 {
  width: 100%;
  max-width: $layout-max-width;
  @include media-breakpoint-down(sm) {
    padding: 0 $mobile-side-padding;
  }
}

.eb-wrapper {
  width: 1272px;
  max-width: 100%;
  margin: 0 auto;

  @include media-breakpoint-down(sm) {
    padding-inline: 15px;
  }

  .eb-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    margin: 0 auto;
    height: 160px;
    background-image: url('/assets/images/eb/eb-static-bg-desktop.jpg');
    background-position: top center;
    background-size: cover;
    background-repeat: no-repeat;
    overflow: hidden;
    padding: 5px;

    &-overlay {
      position: absolute;
      width: 1272px;
      min-width: 1272px;
      height: 81px;
      bottom: 0;
      right: 0;
    }

    &-logo {
      width: 514px;
      height: 56px;
    }

    @include media-breakpoint-down(sm) {
      background-image: url('/assets/images/eb/eb-static-bg-mobile.jpg');
      background-position: center center;
      height: 140px;
      background-size: 177%;
      &-overlay {
        display: none;
      }
      &-logo {
        width: 215px;
        height: 23px;
      }
    }
  }
}

.main-container {
  position: relative;
  display: flex;
  justify-content: space-between;
  width: $layout-max-width;
  max-width: 100%;
  gap: 24px;

  &.without-eb-header {
    margin: 40px auto;
  }

  @include media-breakpoint-down(md) {
    flex-wrap: wrap;
    padding: 0 15px;
  }

  .category {
    display: flex;
    flex-direction: column;
    align-content: flex-start;
    margin-bottom: 125px;
    // margin-right: 20px;
    margin-right: auto;

    &.full {
      width: 100%;
    }

    @include media-breakpoint-down(xs) {
      width: 90%;
      margin: auto;
    }

    .wrapper {
      max-width: 100%;

      &.article-list {
        gap: 12px;
        display: flex;
        flex-direction: column;
        margin-top: 24px;

        [mno-article-card] {
          padding-bottom: 12px;
        }
      }

      .dossier-ad {
        padding: 50px 0;
        margin: 40px 0;
        border-top: 1px solid var(--kui-slate-300);
        border-bottom: 1px solid var(--kui-slate-300);
      }

      .dossier-ad-sm {
        @include media-breakpoint-down(xs) {
          padding: 50px 0;
          margin: 40px 0;
          border-top: 1px solid var(--kui-slate-300);
          border-bottom: 1px solid var(--kui-slate-300);
        }
      }
    }

    .break-title {
      text-transform: uppercase;
      background-color: var(--kui-slate-500);
      color: var(--kui-white);
      font-weight: 500;
      width: 100%;
      padding: 6px 10px;
      margin-bottom: 40px;
    }
  }

  &.is-bayer-blog {
    > .category.full {
      margin-bottom: 0;
    }

    .category {
      .article-list {
        article[mno-article-card] {
          padding-bottom: 8px;

          .article-container {
            gap: 24px;

            .article-thumbnail-figure {
              flex: unset;

              .article-thumbnail {
                max-width: 192px;
                max-height: 144px;
                object-fit: cover;
              }
            }
          }

          @include media-breakpoint-down(sm) {
            .article-thumbnail {
              aspect-ratio: 4/3 !important;
            }
          }
        }

        .blog-article-date {
          border-bottom: 0.5px solid var(--kui-slate-900);
          display: flex;
          padding: 8px 8px 8px 0px;
          align-items: center;
          justify-content: flex-start;
          gap: 4px;
          color: var(--kui-slate-900);

          font-size: 12px;
          font-weight: 700;
          line-height: 16px; /* 133.333% */
        }
      }
    }
  }

  .sidebar {
    width: 300px;

    @include media-breakpoint-down(sm) {
      margin: 0;
      width: 100%;
    }
  }
}

.desktop-ad-local {
  display: none;
  @media only screen and (min-width: 1100px) {
    display: block;
  }
}

.tablet-ad-local {
  display: none;
  @media only screen and (max-width: 1099px) {
    display: block;
  }
}
