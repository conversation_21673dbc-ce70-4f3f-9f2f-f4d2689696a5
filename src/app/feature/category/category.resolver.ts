import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { ApiResponseMetaList, ApiResult, ArticleCard, LayoutService, LayoutWithExcludeIds, Sponsorship } from '@trendency/kesma-ui';
import { BayerBlogService, CategoryService, ConfigService } from '../../shared';
import { CategoryResolverResponse } from './category.definitions';

@Injectable()
export class CategoryResolver {
  constructor(
    private readonly categoryService: CategoryService,
    private readonly router: Router,
    private readonly layoutService: LayoutService,
    private readonly configService: ConfigService,
    private readonly bayerBlogService: BayerBlogService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<CategoryResolverResponse> {
    const isRedirect = route?.data?.['redirect'];
    const brand = route?.data?.['brand'];
    const isYear = !isNaN(route.params['year']);

    const categorySlug: string = brand ?? route.params['categorySlug'];
    const subCategorySlug: string = isYear ? '' : route.params['year'];
    const year: string = isYear && route.params['year'] ? route.params['year'] : '';
    const month: string = isYear && route.params['month'] ? route.params['month'] : '';
    const slug: string = subCategorySlug ? subCategorySlug : categorySlug;
    const pageIndex: number = route.queryParams['page'] ? route.queryParams['page'] - 1 : 0;
    const layoutRequest$: Observable<LayoutWithExcludeIds> =
      isYear && !isRedirect
        ? of({
            data: null,
            excludedIds: [],
            meta: {},
          } as unknown as LayoutWithExcludeIds)
        : this.layoutService.getLayoutWithExcludeIds(slug);

    return layoutRequest$.pipe(
      switchMap((layoutResponse: LayoutWithExcludeIds) => {
        const isBayerBlog = this.bayerBlogService.isBayerBlogColumn(slug);
        let rowOnPageCount = layoutResponse?.data && pageIndex === 0 ? 6 : 20;
        if (isBayerBlog) {
          rowOnPageCount = 11;
        }
        if (isYear) {
          rowOnPageCount = 5;
        }
        const excludedIds: string[] = (layoutResponse?.excludedIds ?? []).filter((e) => !!e);
        const rowFrom = this.getRowFrom(pageIndex, !!layoutResponse?.data?.struct.length, isBayerBlog);
        this.configService.setSponsorship(layoutResponse?.sponsorship as Sponsorship);

        return this.categoryService.getCategoryArticlesEnsurePage(slug, pageIndex, rowOnPageCount, year, month, excludedIds, rowFrom).pipe(
          catchError((error) => {
            this.router.navigate(['/', '404'], {
              state: { errorResponse: JSON.stringify(error) },
              skipLocationChange: true,
            });
            return throwError(() => error);
          }),
          map(
            (
              categoryResponse: ApiResult<
                ArticleCard[],
                ApiResponseMetaList & {
                  column?: { title: string; slug: string };
                  columnParentTitle?: string;
                  sponsorship?: Sponsorship;
                }
              >
            ) => {
              if (categoryResponse.meta.sponsorship) {
                this.configService.setSponsorship(categoryResponse.meta.sponsorship);
              }
              return {
                layoutApiResponse: layoutResponse.data,
                // meta: layoutResponse.meta,
                excludedIds: layoutResponse.excludedIds,
                category: categoryResponse,
                slug,
                year,
                month,
                columnTitle: categoryResponse.meta?.column?.title,
                columnParentTitle: categoryResponse.meta?.columnParentTitle,
              } as CategoryResolverResponse;
            }
          )
        );
      })
    );
  }

  private getRowFrom(pageIndex: number, isLayout: boolean, isBayerBlog: boolean): number {
    const rowOnPageCount = pageIndex <= 1 && isLayout ? 16 : isBayerBlog ? 10 : 20;
    const baseRowFrom = isLayout ? 16 : isBayerBlog ? 10 : 20;
    if (pageIndex < 1) {
      return 0;
    }
    if (pageIndex === 1) {
      return baseRowFrom;
    } else {
      return (pageIndex - 1) * rowOnPageCount + baseRowFrom;
    }
  }
}
