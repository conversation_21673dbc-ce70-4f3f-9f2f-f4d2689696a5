{"apps": [{"name": "mno-dev", "script": "dist/server/server.mjs", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4000}}, {"name": "mno-test", "script": "dist/server/server.mjs", "exec_mode": "cluster", "instances": "2", "cwd": "/content/apps/mnofe/app", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30009}, "out_file": "/content/logs/mnofe/out.log", "err_file": "/content/logs/mnofe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "mno-prod", "script": "dist/server/server.mjs", "max_memory_restart": "500M", "cwd": "/content/apps/mnofe/app", "exec_mode": "cluster", "instances": "6", "max_restarts": 10, "env": {"PORT": 30009}, "out_file": "/content/logs/mnofe/out.log", "err_file": "/content/logs/mnofe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "mno-prod-b", "script": "dist/server/server.mjs", "max_memory_restart": "500M", "cwd": "/content/apps/mnofe/app-b", "exec_mode": "cluster", "instances": "6", "max_restarts": 10, "env": {"PORT": 40009}, "out_file": "/content/logs/mnofe/out-b.log", "err_file": "/content/logs/mnofe/err-b.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "mno-prod-c", "script": "dist/server/server.mjs", "max_memory_restart": "500M", "cwd": "/content/apps/mnofe/app-c", "exec_mode": "cluster", "instances": "6", "max_restarts": 10, "env": {"PORT": 50009}, "out_file": "/content/logs/mnofe/out-c.log", "err_file": "/content/logs/mnofe/err-c.log", "log_type": "json", "time": true, "merge_logs": true}]}